import defaultTheme from 'tailwindcss/defaultTheme';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/**/*.blade.php',
        './resources/**/*.js',
        './resources/**/*.vue',
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],  
            },
            gridTemplateColumns: {
                '15': "repeat(15, minmax(0, 1fr))",
            },
            gridColumn: {
                'span-15': 'span 15 / span 15',
            }
        },
    },
    plugins: [],
};
