<?php

declare(strict_types=1);

namespace App\Traits;

use App\Enums\PeriodicityType;
use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\FeaturePlan;
use App\Models\Plan;
use App\Models\Subscription;
use Carbon\CarbonImmutable;
use InvalidArgumentException;

trait HasSubscription
{
    public function getFeature(string $name): Feature
    {
        $feature = Feature::where('name', $name)->first();

        if (! $feature) {
            // Feature does not exist
            throw new InvalidArgumentException(
                "Feature with name '$name' does not exist.",
            );
        }

        return $feature;
    }

    public function getActiveSubscription(): Subscription
    {
        $subscription = $this->subscription()
            ->with('plan')
            ->where('expired_at', '>=', now())
            ->where('canceled_at', null)
            ->latest()
            ->first();

        if (! $subscription) {
            // No active subscription
            throw new InvalidArgumentException('No active subscription found.');
        }

        return $subscription;
    }

    public function getActiveSubscriptionByFeature(
        string $featureName,
    ): Subscription {
        $subscription = Subscription::with('plan')
            ->where('project_id', $this->id)
            ->where('expired_at', '>=', now())
            ->whereRelation('plan.feature', 'name', '=', $featureName)
            ->first();

        if (! $subscription) {
            throw new InvalidArgumentException(
                "No subscription found for feature $featureName",
            );
        }

        return $subscription;
    }

    public function subscribeTo(
        Plan $plan,
        CarbonImmutable $startDate,
        int $quantity = 1,
    ): Subscription {
        // Calculate the expiration date based on the plan periodicity type and quantity
        $expiredDate = PeriodicityType::getExpirationDateModifier(
            $plan->periodicity_type,
            $startDate,
            $quantity,
        );

        return Subscription::create([
            'plan_id' => $plan->id,
            'project_id' => $this->id,
            'started_at' => $startDate,
            'expired_at' => $expiredDate->call($startDate),
            'canceled_at' => null,
        ]);
    }

    public function extendSubscription(): void
    {
        // Check for an active subscription
        $activeSubscription = $this->subscription()
            ->where('started_at', '<=', now())
            ->where('expired_at', '>=', now())
            ->first();

        if ($activeSubscription) {
            // If there is an active subscription, do nothing
            throw new InvalidArgumentException(
                'There is an active subscription.',
            );
        }

        $expiredSubscription = $this->subscription()
            ->where('expired_at', '<', now())
            ->latest('expired_at')
            ->first();

        if (! $expiredSubscription) {
            throw new InvalidArgumentException(
                'There is no subscription funds to extend.',
            );
        }

        // should never happen
        // plan not null in database
        //        if (! $expiredSubscription->plan) {
        //            throw new InvalidArgumentException(
        //                'The expired subscription does not have an associated plan.',
        //            );
        //        }

        /** @var Plan $plan */
        $plan = $expiredSubscription->plan;

        // Calculate the expiration date based on the plan periodicity type and quantity
        $expiredDate = PeriodicityType::getExpirationDateModifier(
            $plan->periodicity_type,
            CarbonImmutable::now(),
            1,
        );

        $expiredSubscription->started_at = now();
        $expiredSubscription->expired_at = $expiredDate();
        $expiredSubscription->save();
    }

    public function cancelSubscription(): void
    {
        $subscription = $this->getActiveSubscription();

        $subscription->canceled_at = now();
        $subscription->save();
    }

    public function canConsume(string $featureName, int $amount): bool
    {
        // Find the feature by name
        $feature = $this->getFeature($featureName);

        // Get the latest subscription for the project
        $subscription = $this->getActiveSubscriptionByFeature($featureName);

        // Check if the feature is included in the plan
        /** @var FeaturePlan $featurePlan */
        $featurePlan = FeaturePlan::where('feature_id', $feature->id)
            ->where('plan_id', $subscription->plan_id)
            ->first();

        // checked in to the getActiveSubscriptionByFeature
        //        if (! $featurePlan) {
        //            // Feature is not part of the subscribed plan
        //            return false;
        //        }

        // If the feature is not consumable, it's unlimited
        if (! $feature->consumable) {
            return true;
        }

        // Calculate total consumption for this feature and subscription
        $consumed = FeatureConsumption::where('feature_id', $feature->id)
            ->where('subscription_id', $subscription->id)
            ->sum('consumption');

        // Retrieve the allocated charges (or limits) for this feature
        $allocated = $featurePlan->charges;

        // Check if the remaining allocation is sufficient
        $remaining = $allocated - $consumed;

        return $remaining >= $amount;
    }

    public function consume(
        string $featureName,
        int $amount,
        string $type,
    ): FeatureConsumption {
        if (! $this->canConsume($featureName, $amount)) {
            // Insufficient consumption
            throw new InvalidArgumentException('Insufficient consumption.');
        }

        $feature = $this->getFeature($featureName);

        $subscription = $this->getActiveSubscriptionByFeature($featureName);

        return FeatureConsumption::create([
            'consumption' => $amount,
            'type' => $type,
            'subscription_id' => $subscription->id,
            'feature_id' => $feature->id,
        ]);
    }

    public function balance(string $featureName): int|float
    {
        $feature = $this->getFeature($featureName);

        $subscription = $this->getActiveSubscriptionByFeature($featureName);

        $total = $feature
            ->featurePlans()
            ->where('plan_id', $subscription->plan_id)
            ->sum('charges');

        $consumed = FeatureConsumption::where('feature_id', $feature->id)
            ->where('subscription_id', $subscription->id)
            ->sum('consumption');

        return $total - $consumed;
    }

    public function featureLimit(string $featureName): int
    {
        $feature = $this->getFeature($featureName);

        $subscription = $this->getActiveSubscriptionByFeature($featureName);

        return (int) $feature
            ->featurePlans()
            ->where('plan_id', $subscription->plan_id)
            ->sum('charges');
    }
}
