<?php

declare(strict_types=1);

namespace App\Services\AI;

use <PERSON>uz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use JsonException;

class MessageCheck
{
    private readonly string $token;

    private readonly string $url;

    private readonly string $model;

    public function __construct(public string $message)
    {
        $this->token = Config::string('message_check.token');
        $this->url = Config::string('message_check.url');
        $this->model = Config::string('message_check.model');
    }

    /**
     * @throws JsonException
     */
    public function moderate(): string
    {
        $prompt = "You are a content moderation AI. Your task is to review the message below. Respond with ONLY the word \"approved\" or the word \"rejected\". Do not provide any explanation or surrounding text. Reject any content that is political or particularly sensitive to Libyan people.\n\n--- MESSAGE START ---\n$this->message\n--- MESSAGE END ---";

        $client = $this->getClient();
        $url = "$this->url$this->model:generateContent?key=$this->token";

        try {
            $body = json_encode([
                'contents' => [
                    ['parts' => [['text' => $prompt]]],
                ],
            ], JSON_THROW_ON_ERROR);

            $response = $client->post($url, [
                'headers' => ['Content-Type' => 'application/json'],
                'body' => $body,
                'http_errors' => false,
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                Log::error('MessageCheck: API request failed', [
                    'status' => $statusCode,
                    'response' => $responseBody,
                ]);

                return 'rejected';
            }

            $data = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
            /** @var array{candidates: array<int, array{content: array{parts: array<int, array{text: string|null}>}}>} $data */
            $text = $data['candidates'][0]['content']['parts'][0]['text'] ?? null;

            if (! $text) {

                Log::error('MessageCheck: Missing text in API response', [
                    'response' => $responseBody,
                ]);

                return 'rejected';
            }

            $clean = strtolower(trim($text, " \t\n\r\0\x0B\"'"));

            return in_array($clean, ['approved', 'rejected']) ? $clean : 'rejected';

        } catch (RequestException $e) {
            return 'Error: Request failed - '.$e->getMessage();
        } catch (JsonException $e) {
            return 'Error: JSON parsing failed - '.$e->getMessage();
        }
    }

    // @codeCoverageIgnoreStart
    /**
     * Get the HTTP client instance.
     * This method is primarily used for testing to allow mocking the client.
     */
    protected function getClient(): Client
    {
        return new Client();
    }
    // @codeCoverageIgnoreEnd
}
