<?php

declare(strict_types=1);

namespace App\Services\Jasmin;

use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;
use App\Services\Jasmin\Models\IncomingMessage;
use App\Services\Jasmin\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class JasminClient
{
    public function __construct(private readonly HttpService $httpService) {}

    /**
     * Provides static factory method for backward compatibility
     */
    public static function createWithConfig(?string $username = null, ?string $password = null, ?string $url = null): self
    {
        return new self(new HttpService($username, $password, $url));
    }

    /**
     * Get the configured HttpService instance
     */
    public function http(): HttpService
    {
        return $this->httpService;
    }

    /**
     * Create a new Message instance
     */
    public function message(): Message
    {
        return new Message();
    }

    /**
     * Receive a dlr callback from the jasmin gateway
     *
     * @param  callable(DeliveryCallback $dlr): bool  $callback
     */
    public function receiveDlrCallback(Request $request, callable $callback): Response
    {
        return $this->httpService->receiveDlrCallback($request, $callback);
    }

    /**
     * Receive a MO message callback from the jasmin gateway
     *
     * @param  callable(IncomingMessage $message): bool  $callback
     */
    public function receiveMessage(Request $request, callable $callback): Response
    {
        return $this->httpService->receiveMessage($request, $callback);
    }
}
