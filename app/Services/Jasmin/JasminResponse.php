<?php

declare(strict_types=1);

namespace App\Services\Jasmin;

use Illuminate\Http\Client\Response;

class JasminResponse
{
    public ?int $status;

    /** @var array<string, mixed>|string|null */
    public array|string|null $data;

    /**
     * @param  array<string, mixed>|null  $data
     */
    public function __construct(public Response $response, ?int $status = null, ?array $data = null)
    {
        $this->status = $status ?? $this->response->status();
        /** @phpstan-ignore-next-line */
        $this->data = $data ?? $this->response->body() ?? null;
    }

    public static function from(Response $response): self
    {
        return new self($response);
    }

    public function isSuccessful(): bool
    {
        return $this->response->ok();
    }

    //    private function translateEror(string $error): ?string
    //    {
    //        // TODO: implement
    //        return '';
    //    }
}
