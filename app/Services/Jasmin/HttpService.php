<?php

declare(strict_types=1);

namespace App\Services\Jasmin;

use App\Services\Jasmin\Exceptions\JasminClientException;
use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;
use App\Services\Jasmin\Models\IncomingMessage;
use App\Services\Jasmin\Validators\HttpMessageValidator;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class HttpService
{
    private readonly string $url;

    private readonly string $username;

    private readonly string $password;

    public function __construct(?string $username = null, ?string $password = null, ?string $url = null)
    {
        /** @var string $jasminUrl */
        $jasminUrl = Config::get('jasmin_client.url');
        /** @var string $jasminUsername */
        $jasminUsername = Config::get('jasmin_client.username');
        /** @var string $jasminPassword */
        $jasminPassword = Config::get('jasmin_client.password');

        $this->url = $url ?? $jasminUrl;
        $this->username = $username ?? $jasminUsername;
        $this->password = $password ?? $jasminPassword;
    }

    /**
     * Send a single message
     *
     * @throws JasminClientException
     * @throws ValidationException
     */
    public function sendMessage(
        string $content,
        string $to,
        string $from,
        int $coding,
        ?int $priority,
        ?string $sdt,
        ?int $validityPeriod,
        string $dlr,
        string $dlrUrl,
        int $dlrLevel,
        string $dlrMethod,
        ?string $tags,
        ?string $hexContent): JasminResponse
    {
        $url = $this->url.'/send';
        $data = [
            'content' => $content,
            'to' => $to,
            'from' => $from,
            'coding' => $coding,
            'priority' => $priority,
            'sdt' => $sdt,
            'validity-period' => $validityPeriod,
            'dlr' => $dlr,
            'dlr-url' => $dlrUrl,
            'dlr-level' => $dlrLevel,
            'dlr-method' => $dlrMethod,
            'tags' => $tags,
            'hex-content' => $hexContent,
            'username' => $this->username,
            'password' => $this->password,
        ];

        $data = array_filter($data);

        $validator = HttpMessageValidator::validate($data);

        if ($validator->fails()) {
            Log::error('JasminClient: Data validation failed for http message');
            foreach ($validator->errors()->all() as $error) {
                /** @phpstan-ignore-next-line */
                Log::error('JasminClient: '.$error);
            }
            throw ValidationException::withMessages($validator->errors()->all());
        }

        try {
            $response = Http::withHeaders($this->makeHeaders())->post($url, $data);
            if (! $response->ok() && Config::get('jasmin.log_http_failures')) {
                Log::info('HTTP Response failed: '.$response->body());
            }
        } catch (ConnectionException $e) {
            Log::debug($e->getMessage());
            throw JasminClientException::from($e);
        }

        return JasminResponse::from($response);
    }

    /**
     * @throws JasminClientException
     */
    public function getMetrics(): JasminResponse
    {
        $url = $this->url.'/metrics';
        try {
            $response = Http::withHeaders($this->makeHeaders())->get($url);
        } catch (ConnectionException $e) {
            throw JasminClientException::from($e);
        }

        return JasminResponse::from($response);
    }

    /**
     * @param  callable(IncomingMessage $message): bool  $callback
     */
    public function receiveMessage(Request $request, callable $callback): Response
    {
        $rules = [
            'id' => ['required', 'string'],
            'from' => ['required'],
            'to' => ['required'],
            'origin-connector' => ['required'],
            'priority' => ['nullable'],
            'coding' => ['nullable'],
            'validity' => ['nullable'],
            'content' => ['nullable'],
            'binary' => ['nullable'],
        ];

        /** @phpstan-ignore-next-line */
        $validator = Validator::make($request->input(), $rules);
        if ($validator->fails()) {
            Log::info('Invalid request received from jasmin');

            return new Response('Invalid Request', 400);
        }

        /** @var array{
         *     id: string,
         *     from: string,
         *     to: string,
         *     origin-connector: string,
         *     priority: string,
         *     coding: int,
         *     validity: string,
         *     content: string,
         *     binary: string,
         * } $validatedData
         */
        $validatedData = $validator->validated();

        $IncomingMessage = new IncomingMessage(
            id: $validatedData['id'],
            from: $validatedData['from'],
            to: $validatedData['to'],
            originConnector: $validatedData['origin-connector'],
            priority: $validatedData['priority'],
            coding: $validatedData['coding'],
            validity: $validatedData['validity'],
            content: $validatedData['content'],
            binary: $validatedData['binary']
        );
        if ($callback($IncomingMessage)) {
            return new Response('ACK/Jasmin', 200);
        }

        return new Response('FAIL/Jasmin', 400);
    }

    /**
     * @param  callable(DeliveryCallback $dlr): bool  $callback
     */
    public function receiveDlrCallback(Request $request, callable $callback): Response
    {
        /** @phpstan-ignore-next-line */
        $validator = Validator::make($request->input(), DeliveryCallback::rules());
        if ($validator->fails()) {
            Log::info('JasminClient: Invalid request received from jasmin');
            Log::debug($validator->errors()->all());

            return new Response('Invalid Request', 400);
        }

        /** @var array{
         *    id: string,
         *     message_status: string,
         *     level: int|string,
         *     connector: string,
         *     id_smsc: ?string,
         *     subdate: ?string,
         *     donedate: ?string,
         *     sub: int|string|null,
         *     dlvrd: int|string|null,
         *     err: int|string|null,
         *     text: ?string
         * } $validatedData
         */
        $validatedData = $validator->validated();

        $dlr = new DeliveryCallback(
            id: $validatedData['id'],
            messageStatus: $validatedData['message_status'],
            level: (int) $validatedData['level'],
            connector: $validatedData['connector'],
            smscId: $validatedData['id_smsc'] ?? null,
            submittedDate: $validatedData['subdate'] ?? null,
            doneDate: $validatedData['donedate'] ?? null,
            submittedCount: isset($validatedData['sub']) ? (int) $validatedData['sub'] : null,
            deliveredCount: isset($validatedData['dlvrd']) ? (int) $validatedData['dlvrd'] : null,
            error: isset($validatedData['err']) ? (int) $validatedData['err'] : null,
            text: $validatedData['text'] ?? null,
        );
        if ($callback($dlr)) {
            return new Response('ACK/Jasmin', 200, ['Content-Type' => 'text/plain']);
        }

        return new Response('FAIL/Jasmin', 400, ['Content-Type' => 'text/plain']);
    }

    /**
     * @return array<string, string>
     */
    private function makeHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
        ];
    }
}
