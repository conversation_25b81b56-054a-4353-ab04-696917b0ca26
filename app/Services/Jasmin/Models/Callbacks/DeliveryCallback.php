<?php

declare(strict_types=1);

namespace App\Services\Jasmin\Models\Callbacks;

class DeliveryCallback
{
    // UUID

    public function __construct(
        /**
         * Internal Jasmin’s gateway message id used for tracking messages
         */
        private readonly string $id,
        /**
         * Delivery status
         */
        public string $messageStatus,
        /**
         * This is a static value indicating the dlr-level originally requested
         */
        public int $level,
        /**
         * The SMPP Connector used to send the message
         */
        public string $connector,
        /**
         * Message id returned from the SMS-C
         */
        public ?string $smscId,
        /**
         * The time and date at which the short message was submitted
         */
        public ?string $submittedDate,
        /**
         * The time and date at which the short message reached it’s final state
         */
        public ?string $doneDate,
        /**
         * Number of short messages originally submitted. This is only relevant when the original message was submitted to a distribution list.The value is padded with leading zeros if necessary
         */
        public ?int $submittedCount,
        /**
         * Number of short messages delivered. This is only relevant where the original message was submitted to a distribution list.The value is padded with leading zeros if necessary
         */
        public ?int $deliveredCount,
        /**
         * Where appropriate this may hold a Network specific error code or an SMSC error code for the attempted delivery of the message
         */
        public ?int $error,
        /**
         * The first 20 characters of the short message
         */
        public ?string $text
    ) {}

    /**
     * @return array<string, string|array<string>>
     */
    public static function rules(): array
    {
        return [
            'id' => 'required|string',
            'message_status' => 'required',
            'level' => 'required|integer|in:1,2,3',
            'connector' => 'required|string',
            'id_smsc' => 'nullable|integer',
            'subdate' => 'nullable',
            'donedate' => 'nullable',
            'sub' => 'nullable',
            'dlvrd' => 'nullable',
            'err' => 'nullable',
            'text' => 'nullable|string|max:25',
        ];
    }

    public function getMessageId(): string
    {
        return $this->id;
    }
}
