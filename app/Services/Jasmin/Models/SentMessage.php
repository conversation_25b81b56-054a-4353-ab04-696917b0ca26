<?php

declare(strict_types=1);

namespace App\Services\Jasmin\Models;

use App\Services\Jasmin\JasminResponse;
use Illuminate\Support\Str;
use RuntimeException;

final class SentMessage
{
    public function __construct(public ?string $status = null, public ?string $messageId = null, public ?string $rawResponse = null) {}

    /**
     * @throws RuntimeException
     */
    public static function fromResponse(JasminResponse $response): self
    {
        /** @var string $data */
        $data = $response->data ?? '';
        if ($data !== '') {
            [$status, $messageId] = self::extractValues($data);

            return new self(status: $status, messageId: $messageId, rawResponse: $data);
        }
        throw new RuntimeException('Invalid response from jasmin');
    }

    public function isSent(): bool
    {
        return $this->messageId !== '' && $this->status === 'Success';
    }

    /**
     * @return array{0: string|null, 1: string|null}
     */
    private static function extractValues(string $data): array
    {
        if (Str::contains($data, 'Success ')) {
            $status = Str::before($data, ' ');
            $messageId = Str::of($data)->after('Success "')->before('"')->toString();

            return [$status, $messageId];
        }

        return [null, null];
    }
}
