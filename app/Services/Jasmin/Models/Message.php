<?php

declare(strict_types=1);

namespace App\Services\Jasmin\Models;

use App\Services\Jasmin\Exceptions\JasminClientException;
use App\Services\Jasmin\HttpService;
use Illuminate\Support\Facades\Log;

class Message
{
    /**
     * Whether to request a delivery report (DLR) from the SMSC
     */
    public bool $dlr = true;

    /**
     * Delivery Report Level to be requested from the SMSC
     * 1: SMS-C level, 2: Terminal level, 3: Both
     */
    public int $dlrLevel = 3;

    private bool $isBinary = false;

    /**
     * Sets the Data Coding Scheme bits, default is 0, accepts values all allowed values in SMPP protocol
     */
    private int $coding = 0;

    /**
     * Default is 0 (lowest priority)
     */
    private int $priority = 0;

    /**
     * Specifies the scheduled delivery time at which the message delivery should be first attempted,
     * default is value is None (message will take SMSC’s default).
     * Supports Absolute and Relative Times per SMPP v3.4 Issue 1.2
     */
    private ?string $sdt = null;

    /**
     * Message validity (minutes) to be passed to SMSC, default is value is None (message will take SMSC’s default)
     */
    private ?int $validityPeriod = null;

    /**
     * DLR is transmitted through http to a third party application using GET or POST method.
     */
    private string $dlrMethod = 'POST';

    /**
     * Will tag the routable to help interceptor or router enable specific business logics.
     */
    private ?string $tags = null;

    /** @phpstan-ignore-next-line  */
    private ?string $hexContent = null;

    private ?string $routeUsername = null;

    private ?string $routePassword = null;

    private ?string $routeUrl = null;

    private ?HttpService $httpService = null;

    public function __construct(/**
     * Destination address, only one address is supported per request
     */
        public ?string $to = null, /**
     * Originating address, In case rewriting of the sender’s address is supported or permitted by the SMS-C
     * used to transmit the message, this number is transmitted as the originating address
     */
        public ?string $from = null, /**
     * Message content
     */
        public ?string $content = null, ?bool $dlr = null, /**
         * If a DLR is requested (dlr = ‘yes’), dlr-url MUST be set, if not, dlr value is reconsidered as ‘no’
         */
        public ?string $dlrUrl = null)
    {
        $this->dlr = $dlr ?? $this->dlr;
    }

    /**
     * Set if the message should be sent as binary
     *
     * @return $this
     */
    public function asBinary(bool $binary): self
    {
        $this->isBinary = $binary;

        return $this;
    }

    /**
     * Set the message content
     *
     * @return $this
     */
    public function content(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    /**
     * Set the destination address (phone number)
     *
     * @return $this
     */
    public function to(string $to): self
    {
        $this->to = $to;

        return $this;
    }

    /**
     * Set the originating address (SENDER ID)
     *
     * @return $this
     */
    public function from(string $from): self
    {
        $this->from = $from;

        return $this;
    }

    /**
     * Set the DLR callback url
     *
     * @return $this
     */
    public function dlrCallbackUrl(string $dlrUrl): self
    {
        $this->dlrUrl = $dlrUrl;

        return $this;
    }

    /**
     * Set if the message should be tracked for delivery
     *
     * @return $this
     */
    public function trackDelivery(bool $value = true): self
    {
        $this->dlr = $value;

        return $this;
    }

    /**
     * Set the route to send the message
     *
     * @return $this
     */
    public function via(string $route, ?string $username = null, ?string $password = null): self
    {
        if ($username !== null && $username !== '' && $username !== '0') {
            $this->routeUsername = $username;
        }
        if ($password !== null && $password !== '' && $password !== '0') {
            $this->routePassword = $password;
        }

        return $this;
    }

    /**
     * Set the priority of the message
     *
     * @return $this
     */
    public function priority(int $priority): self
    {
        $this->priority = min($priority, 3);

        return $this;
    }

    public function sdt(?string $sdt): self
    {
        $this->sdt = $sdt;

        return $this;
    }

    /**
     * @return $this
     */
    public function validityPeriod(?int $validityPeriod): self
    {
        $this->validityPeriod = $validityPeriod;

        return $this;
    }

    /**
     * Set the tags of the message
     *
     * @return $this
     */
    public function tags(?string $tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * @return $this
     */
    public function coding(?int $coding): self
    {
        $this->coding = min(($coding ?? 0), 14);

        return $this;
    }

    /**
     * Allow injecting a custom HttpService (for testing)
     */
    public function setHttpService(HttpService $service): self
    {
        $this->httpService = $service;

        return $this;
    }

    /**
     * @throws JasminClientException
     */
    public function send(): SentMessage
    {
        try {
            $httpService = $this->httpService ?? new HttpService($this->routeUsername, $this->routePassword, $this->routeUrl);
            $response = $httpService
                ->sendMessage(
                    content: $this->content ?? '',
                    to: $this->sanitizeNumber($this->to ?? ''),
                    from: $this->from ?? '',
                    coding: $this->coding,
                    priority: $this->priority,
                    sdt: $this->sdt,
                    validityPeriod: $this->validityPeriod ?? null,
                    dlr: $this->dlr ? 'yes' : 'no',
                    dlrUrl: $this->dlrUrl ?? '',
                    dlrLevel: $this->dlrLevel ?? 3,
                    dlrMethod: $this->dlrMethod ?? 'POST',
                    tags: $this->tags,
                    hexContent: $this->hexContent,
                );
            if ($response->isSuccessful()) {
                return SentMessage::fromResponse($response);
            }
            throw new JasminClientException('Failed to send message');
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw new JasminClientException($e->getMessage(), $e->getCode(), $e);
        } catch (JasminClientException $e) {
            Log::error('JasminClient: '.$e->getMessage());
            throw $e;
        }
    }

    /**
     * @return $this
     */
    public function withCredentials(?string $username = null, ?string $password = null, ?string $url = null): self
    {
        $this->routeUsername = $username ?? $this->routeUsername;
        $this->routePassword = $password ?? $this->routePassword;
        $this->routeUrl = $url ?? $this->routeUrl;

        return $this;
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return array_filter([
            'to' => $this->to,
            'from' => $this->from,
            'content' => $this->content,
            'dlr' => $this->dlr ? 'yes' : 'no',
            'dlr-url' => $this->dlrUrl,
            'dlr-level' => $this->dlrLevel,
            'dlr-method' => $this->dlrMethod,
            'is_binary' => $this->isBinary,
            'priority' => $this->priority,
            'sdt' => $this->sdt,
            'validity-period' => $this->validityPeriod,
            'tags' => $this->tags,
            'coding' => $this->coding,
            'hex-content' => $this->hexContent,
        ]);
    }

    private function sanitizeNumber(string $phone): string
    {
        return preg_replace('/\D/', '', $phone) ?? $phone;
    }
}
