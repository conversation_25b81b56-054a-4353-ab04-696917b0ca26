<?php

declare(strict_types=1);

namespace App\Services\Jasmin\Models;

class IncomingMessage
{
    public function __construct(
        /**
         * Internal Jasmin’s gateway message id
         */
        public string $id,
        /**
         * Originating address
         */
        public string $from,
        /**
         * Destination address, only one address is supported per request
         */
        public string $to,
        /**
         * Jasmin http connector id
         */
        public string $originConnector,
        /**
         * Default is 1 (lowest priority)
         */
        public string $priority,
        /**
         * Default is 0, accepts values all allowed values in SMPP protocol
         */
        public int $coding,
        /**
         * The validity period parameter indicates the Jasmin GW expiration time, after which the message should be discarded if not delivered to the destination
         */
        public string $validity,
        /**
         * Content of the message
         */
        public string $content,
        public string $binary
    ) {}

    /**
     * @param  array{
     *     id: string,
     *     from: string,
     *     to: string,
     *     origin-connector: string,
     *     priority: string,
     *     coding: int,
     *     validity: string,
     *     content: string,
     *     binary: string,
     * }  $data
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'],
            $data['from'],
            $data['to'],
            $data['origin-connector'],
            $data['priority'],
            $data['coding'],
            $data['validity'],
            $data['content'],
            $data['binary']
        );
    }
}
