<?php

declare(strict_types=1);

namespace App\Services;

use App\Facades\PhoneProvider;
use App\Models\Project;
use App\Models\Sender;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

final readonly class SenderHelper
{
    /**
     * Get a sender filtered by the provided project and pivot conditions.
     */
    public function getSender(string $sender, Project $project): ?Sender
    {
        return Sender::where('sender', $sender)
            ->whereHas('companies', function (Builder $query) use (
                $project,
            ): void {
                $query
                    ->where('id', $project->company_id)
                    ->where(function (Builder $query): void {
                        $query
                            ->where(
                                'sender_company.expired_at',
                                '>=',
                                Carbon::now(),
                            )
                            ->orWhereNull('sender_company.expired_at');
                    });
            })
            ->where('status', 'active')
            ->with('provider.provider')
            ->first();
    }

    /**
     * @param  array<string>  $providers
     */
    public function checkSenderProvider(
        string $receiver,
        array $providers,
    ): bool {
        $provider = PhoneProvider::detectPhoneNumberProvider($receiver);

        if ($provider === 'Unknown') {
            return false;
        }

        return in_array($provider, $providers);
    }
}
