<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Message;

final class SmsService
{
    /** @return array{ 'parts': int, 'type': string } */
    public static function calculateSmsParts(string $message): array
    {
        // Function to check if the message contains only GSM 7-bit characters
        $isGSM7Message = function (string $message): bool {
            $gsm7Characters = [
                "\x0a",
                "\x0d",
                "\x20",
                "\x21",
                "\x22",
                "\x23",
                "\x24",
                "\x25",
                "\x26",
                "\x27",
                "\x28",
                "\x29",
                "\x2a",
                "\x2b",
                "\x2c",
                "\x2d",
                "\x2e",
                "\x2f",
                "\x30",
                "\x31",
                "\x32",
                "\x33",
                "\x34",
                "\x35",
                "\x36",
                "\x37",
                "\x38",
                "\x39",
                "\x3a",
                "\x3b",
                "\x3c",
                "\x3d",
                "\x3e",
                "\x3f",
                "\x40",
                "\x41",
                "\x42",
                "\x43",
                "\x44",
                "\x45",
                "\x46",
                "\x47",
                "\x48",
                "\x49",
                "\x4a",
                "\x4b",
                "\x4c",
                "\x4d",
                "\x4e",
                "\x4f",
                "\x50",
                "\x51",
                "\x52",
                "\x53",
                "\x54",
                "\x55",
                "\x56",
                "\x57",
                "\x58",
                "\x59",
                "\x5a",
                "\x5b",
                "\x5c",
                "\x5d",
                "\x5e",
                "\x5f",
                "\x60",
                "\x61",
                "\x62",
                "\x63",
                "\x64",
                "\x65",
                "\x66",
                "\x67",
                "\x68",
                "\x69",
                "\x6a",
                "\x6b",
                "\x6c",
                "\x6d",
                "\x6e",
                "\x6f",
                "\x70",
                "\x71",
                "\x72",
                "\x73",
                "\x74",
                "\x75",
                "\x76",
                "\x77",
                "\x78",
                "\x79",
                "\x7a",
                "\x7b",
                "\x7c",
                "\x7d",
                "\x7e",
            ];

            for ($i = 0; $i < mb_strlen($message); $i++) {
                if (! in_array($message[$i], $gsm7Characters)) {
                    return false;
                }
            }

            return true;
        };

        // Determine if the message is GSM 7-bit
        $isGSM7 = $isGSM7Message($message);
        $bitsPerChar = $isGSM7 ? 7 : 16;

        // Calculate message length
        $messageLength = $isGSM7
            ? mb_strlen($message)
            : mb_strlen($message, 'UTF-8');

        // Calculate SMS parts
        $smsConcatSpace = (140 * 8 - 6 * 8) / $bitsPerChar;
        $smsParts = 0;

        if ($messageLength > 0) {
            if ($messageLength <= floor((140 * 8) / $bitsPerChar)) {
                $smsParts = 1;
            } else {
                $smsParts = (int) floor($messageLength / $smsConcatSpace);
                if ($messageLength % $smsConcatSpace > 0) {
                    $smsParts++;
                }
            }
        }

        return ['parts' => $smsParts, 'type' => $isGSM7 ? 'GSM7' : 'UCS2'];
    }
}
