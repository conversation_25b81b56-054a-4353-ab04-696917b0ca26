<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Provider;
use Exception;
use Illuminate\Support\Facades\Log;

class PhoneProvider
{
    /**
     * Detect the provider based on the phone number.
     */
    public static function detectPhoneNumberProvider(string $phoneNumber): string
    {
        // Fetch all providers with their prefixes and country codes from the database
        $providers = Provider::get();

        foreach ($providers as $provider) {

            if (preg_match("/{$provider->pattern}/", $phoneNumber) === 1) {
                return $provider->name;
            }
        }

        return 'Unknown';
    }

    public static function prepareForSMSC(Provider $provider, string $phoneNumber): string
    {
        $replace = $provider->smpp_pattern_replace ?? '';

        if (! $provider->smpp_pattern) {
            return $phoneNumber;
        }

        try {
            return (string) preg_replace("/{$provider->smpp_pattern}/", $replace, $phoneNumber);
        } catch (Exception $e) {
            Log::error('Error formatting number', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $phoneNumber;
        }
    }
}
