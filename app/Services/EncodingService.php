<?php

declare(strict_types=1);

namespace App\Services;

class EncodingService
{
    public static function isGSM7Message(string $message): bool
    {
        return preg_match('/^[\x00-\x7F]*$/', $message) === 1;
    }

    public static function encodeToUCS2(string $message): string
    {
        /** @var string|false $result */
        $result = mb_convert_encoding($message, 'UCS-2', 'auto');
        if ($result === '' || $result === false) {
            return $message;
        }

        return $result;
    }
}
