<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\FeaturePlanFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

final class FeaturePlan extends Pivot
{
    /** @use HasFactory<FeaturePlanFactory> */
    use HasFactory, HasUuids;

    protected $table = 'feature_plan';

    protected $fillable = ['charges', 'feature_id', 'plan_id'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Feature, $this> */
    public function feature(): BelongsTo
    {
        return $this->belongsTo(Feature::class, 'feature_id', 'id');
    }

    /** @return BelongsTo<Plan, $this> */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'id');
    }
}
