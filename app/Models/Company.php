<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\CompanyFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property ?Pivot $pivot
 */
final class Company extends Model implements HasMedia
{
    /** @use HasFactory<CompanyFactory> */
    use HasFactory;

    use HasUuids;
    use InteractsWithMedia;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'category',
        'description',
        'city_id',
        'status',
        'auto_approve',
        'verified_at',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsToMany<User, $this> */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_user');
    }

    /** @return BelongsToMany<Role, $this> */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class);
    }

    /** @return HasMany<Representative, $this> */
    public function representatives(): HasMany
    {
        return $this->hasMany(Representative::class);
    }

    /** @return BelongsTo<City, $this> */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /** @return HasMany<Transaction, $this> */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /** @return BelongsToMany<Sender, $this> */
    public function senders(): BelongsToMany
    {
        return $this->belongsToMany(Sender::class, 'sender_company');
    }

    /** @return HasMany<Project, $this> */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /** @return HasMany<ContactGroup, $this> */
    public function contactGroups(): HasMany
    {
        return $this->hasMany(ContactGroup::class, 'company_id');
    }

    public function balance(): float|int
    {
        return $this->transactions()
            ->where('status', 'completed')
            ->sum('amount') / 1000;
    }

    /** @return HasMany<Message, $this> */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    /** @return HasMany<CampaignMessage, $this> */
    public function campaignMessages(): HasMany
    {
        return $this->hasMany(CampaignMessage::class);
    }

    /** @return HasMany<MessageTemplate, $this> */
    public function messageTemplates(): HasMany
    {
        return $this->hasMany(MessageTemplate::class);
    }
}
