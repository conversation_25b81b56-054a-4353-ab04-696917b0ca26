<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\SubscriptionFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Staudenmeir\EloquentHasManyDeep\HasOneDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

final class Subscription extends Model
{
    /** @use HasFactory<SubscriptionFactory> */
    use HasFactory;

    use HasRelationships;
    use HasUuids;

    protected $fillable = [
        'plan_id',
        'project_id',
        'started_at',
        'expired_at',
        'canceled_at',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'expired_at' => 'datetime',
        'canceled_at' => 'datetime',
    ];

    /** @return BelongsTo<Plan, $this> */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /** @return BelongsTo<Project, $this> */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /** @return HasMany<FeatureConsumption, $this> */
    public function consumptions(): HasMany
    {
        return $this->hasMany(
            FeatureConsumption::class,
            'subscription_id',
            'id',
        );
    }

    public function isActive(): bool
    {
        return $this->expired_at && $this->expired_at->isFuture();
    }

    /** @return HasOneDeep<Company, $this> */
    public function company(): HasOneDeep
    {
        return $this->hasOneDeep(
            Company::class,
            [Project::class],
            ['id', 'id'],
            ['project_id', 'company_id'],
        );
    }
}
