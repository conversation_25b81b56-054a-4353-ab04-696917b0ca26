<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\MessageReceiptFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class MessageReceipt extends Model
{
    /** @use HasFactory<MessageReceiptFactory> */
    use HasFactory, HasUuids;

    protected $table = 'message_recipients';

    protected $fillable = [
        'message_id',
        'number',
        'contact_id',
        'sent_at',
        'delivered_at',
        'status',
        'smpp_message_id',
        'delivery_report',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Message, $this> */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'message_id');
    }

    /** @return BelongsTo<Contact, $this> */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class, 'contact_id');
    }

    /**
     * @return Attribute<string, null>
     */
    public function number(): Attribute
    {
        return Attribute::make(
            set: fn (string $value): string =>
                // remove + from the number and replace it with 00
                str_replace('+', '00', $value),
        );
    }
}
