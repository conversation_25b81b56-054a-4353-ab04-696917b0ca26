<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ProjectObserver;
use App\Traits\HasSubscription;
use Database\Factories\ProjectFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;

#[ObservedBy([ProjectObserver::class])]
final class Project extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<ProjectFactory> */
    use HasFactory;

    use HasSubscription;
    use HasUuids;

    protected $fillable = [
        'name',
        'description',
        'company_id',
        'status',
        'token',
        'type',
        'limit',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Company, $this> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /** @return BelongsToMany<ContactGroup, $this> */
    public function contact_groups(): BelongsToMany
    {
        return $this->belongsToMany(
            ContactGroup::class,
            'contact_group_project',
            'project_id',
            'contact_group_id',
        );
    }

    /**
     * @return HasMany<Subscription, $this>
     */
    public function subscription(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * @return HasManyThrough<FeatureConsumption, Subscription, $this>
     */
    public function consumption(): HasManyThrough
    {
        return $this->hasManyThrough(
            FeatureConsumption::class,
            Subscription::class,
            'project_id',
            'subscription_id',
            'id',
            'id',
        );
    }

    /** @return HasMany<Message, $this> */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class, 'project_id', 'id');
    }

    /** @return HasMany<Transaction, $this> */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /** @return HasMany<IpWhitelist, $this> */
    public function ipWhitelists(): HasMany
    {
        return $this->hasMany(IpWhitelist::class);
    }
}
