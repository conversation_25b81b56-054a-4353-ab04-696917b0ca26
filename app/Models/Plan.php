<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\PlanFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Plan extends Model
{
    /** @use HasFactory<PlanFactory> */
    use HasFactory;

    use HasUuids;

    protected $fillable = [
        'name',
        'periodicity_type',
        'price',
        'status',
        'over_quota_price',
        'periodicity_quantity',
        'extended_price',
        'tags',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'tags' => 'array',
    ];

    /** @return BelongsToMany<Feature, $this, FeaturePlan> */
    public function feature(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 'feature_plan')
            ->using(FeaturePlan::class)
            ->withPivot('charges');
    }

    /** @return HasMany<FeaturePlan, $this> */
    public function features(): HasMany
    {
        return $this->hasMany(FeaturePlan::class);
    }

    /** @return HasMany<Subscription, $this> */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /** @return HasMany<FeaturePlan, $this> */
    public function featurePlans(): HasMany
    {
        return $this->hasMany(FeaturePlan::class);
    }

    /**
     * @return Attribute<int|float, int|float>
     */
    public function price(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, mixed $attributes): int|float => (is_numeric($value) ? $value / 1000 : 0),
            set: fn (mixed $value): int|float => (is_numeric($value) ? $value * 1000 : 0),
        );
    }

    /**
     * @return Attribute<int|float, int|float>
     */
    public function overQuotaPrice(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, mixed $attributes): int|float => (is_numeric($value) ? $value / 1000 : 0),
            set: fn (mixed $value): int|float => (is_numeric($value) ? $value * 1000 : 0),
        );
    }
}
