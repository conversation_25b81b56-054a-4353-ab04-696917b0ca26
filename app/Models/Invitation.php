<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class Invitation extends Model
{
    use HasUuids;

    protected $fillable = [
        'email',
        'company_id',
        'expires_at',
    ];

    /**
     * @var array{
     *   expires_at: 'datetime'
     * }
     */
    protected $casts = [
        'expires_at' => 'datetime',
    ];

    /** @return BelongsTo<Company, $this> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
