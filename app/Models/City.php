<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\CityFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class City extends Model
{
    /** @use HasFactory<CityFactory> */
    use HasFactory;

    use HasUuids;

    protected $fillable = ['name', 'description'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return HasMany<Company, $this> */
    public function companies(): HasMany
    {
        return $this->hasMany(Company::class);
    }

    /** @return BelongsToMany<CampaignMessage, $this> */
    public function campaign_messages(): BelongsToMany
    {
        return $this->belongsToMany(
            CampaignMessage::class,
            'campaign_message_cities',
            'city_id',
            'campaign_message_id',
        );
    }
}
