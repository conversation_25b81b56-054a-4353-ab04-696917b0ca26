<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\SenderFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Sender extends Model
{
    /** @use HasFactory<SenderFactory> */
    use HasFactory;

    use HasUuids;

    protected $fillable = [
        'sender',
        'category',
        'type',
        'status',
        'notes',
        'provider_id',
        'company_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    //    public function provider(): BelongsTo
    //    {
    //        return $this->belongsTo(Provider::class);
    //    }

    /** @return BelongsTo<Company, $this> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /** @return HasMany<SenderProvider, $this> */
    public function provider(): HasMany
    {
        return $this->hasMany(SenderProvider::class, 'sender_id', 'id');
    }

    /** @return BelongsToMany<Company, $this> */
    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(
            Company::class,
            'sender_company',
        )->withPivot('expired_at');
    }
}
