<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\CampaignMessageFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

final class CampaignMessage extends Model
{
    /** @use HasFactory<CampaignMessageFactory> */
    use HasFactory;

    use HasUuids;

    protected $fillable = [
        'short_message',
        'age_from',
        'age_to',
        'sex',
        'quantity',
        'status',
        'company_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Company, $this> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /** @return BelongsToMany<City, $this> */
    public function cities(): BelongsToMany
    {
        return $this->belongsToMany(
            City::class,
            'campaign_message_cities',
            'campaign_message_id',
            'city_id',
        );
    }

    /** @return BelongsToMany<Provider, $this> */
    public function providers(): BelongsToMany
    {
        return $this->belongsToMany(
            Provider::class,
            'campaign_message_providers',
            'campaign_message_id',
            'provider_id',
        );
    }
}
