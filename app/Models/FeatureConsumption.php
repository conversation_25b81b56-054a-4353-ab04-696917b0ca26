<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\FeatureConsumptionFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class FeatureConsumption extends Model
{
    /** @use HasFactory<FeatureConsumptionFactory> */
    use HasFactory, HasUuids;

    protected $table = 'feature_consumptions';

    protected $fillable = [
        'consumption',
        'type',
        'feature_id',
        'subscription_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Feature, $this> */
    public function feature(): BelongsTo
    {
        return $this->belongsTo(Feature::class);
    }

    /** @return BelongsTo<Subscription, $this> */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }
}
