<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\IpWhitelistFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class IpWhitelist extends Model
{
    /** @use HasFactory<IpWhitelistFactory> */
    use HasFactory;

    use HasRelationships;
    use HasUuids;

    protected $fillable = [
        'ip_address',
        'description',
        'project_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return BelongsTo<Project, $this> */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
