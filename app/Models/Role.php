<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\SenderFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

final class Role extends \Spatie\Permission\Models\Role
{
    /** @use HasFactory<SenderFactory> */
    use HasFactory;

    /** @return BelongsToMany<Company, $this> */
    public function company(): BelongsToMany
    {
        return $this->belongsToMany(Company::class);
    }
}
