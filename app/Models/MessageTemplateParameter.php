<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\MessageTemplateParameterFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MessageTemplateParameter extends Model
{
    /** @use HasFactory<MessageTemplateParameterFactory> */
    use HasFactory;

    use HasUuids;

    protected $fillable = [
        'message_template_id',
        'name',
        'type',
        'max_limit',
    ];

    /** @return BelongsTo<MessageTemplate, $this> */
    public function messageTemplate(): BelongsTo
    {
        return $this->belongsTo(MessageTemplate::class, 'message_template_id');
    }
}
