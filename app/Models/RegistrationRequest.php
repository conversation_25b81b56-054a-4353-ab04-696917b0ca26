<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\RegistrationRequestFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegistrationRequest extends Model
{
    /** @use HasFactory<RegistrationRequestFactory> */
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'city',
        'company',
        'category',
        'have_smsapi',
        'target',
        'system_type',
        'notes',
        'status',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'target' => 'array',
    ];
}
