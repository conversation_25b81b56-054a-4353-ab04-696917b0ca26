<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\FeatureFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Feature extends Model
{
    /** @use HasFactory<FeatureFactory> */
    use HasFactory, HasUuids;

    protected $fillable = ['name', 'consumable'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return HasMany<FeatureConsumption, $this> */
    public function consumption(): HasMany
    {
        return $this->hasMany(FeatureConsumption::class);
    }

    /** @return HasMany<FeaturePlan, $this> */
    public function featurePlans(): HasMany
    {
        return $this->hasMany(FeaturePlan::class);
    }
}
