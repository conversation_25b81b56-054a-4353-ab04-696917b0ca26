<?php

declare(strict_types=1);

namespace App\Models;

use Database\Factories\ProviderFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

final class Provider extends Model implements HasMedia
{
    /** @use HasFactory<ProviderFactory> */
    use HasFactory, HasUuids;

    use InteractsWithMedia;

    protected $fillable = [
        'name',
        'host',
        'port',
        'system_id',
        'password',
        'system_type',
        'connection_timeout',
        'enquire_link_interval',
        'status',
        'pattern',
        'smpp_pattern',
        'smpp_pattern_replace',
    ];

    protected $casts = [
        'host' => 'encrypted',
        'port' => 'encrypted:integer',
        'system_id' => 'encrypted',
        'password' => 'encrypted',
        'connection_timeout' => 'integer',
        'enquire_link_interval' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /** @return HasMany<SenderProvider, $this> */
    public function sender(): HasMany
    {
        return $this->hasMany(SenderProvider::class);
    }

    /** @return BelongsToMany<CampaignMessage, $this> */
    public function campaign_messages(): BelongsToMany
    {
        return $this->belongsToMany(
            CampaignMessage::class,
            'campaign_message_providers',
            'provider_id',
            'campaign_message_id',
        );
    }
}
