<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\TransactionResource\Pages;

use App\Filament\Admin\Resources\TransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditTransaction extends EditRecord
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (
            $data['action_type'] === 'charge' || $data['action_type'] === 'refund' && $data['amount'] > 0
        ) {
            assert(is_numeric($data['amount']));
            $data['amount'] *= -1;
        }

        return $data;
    }
}
