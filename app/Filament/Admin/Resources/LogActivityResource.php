<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\LogActivityResource\Pages;
use App\Models\LogActivity;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

final class LogActivityResource extends Resource
{
    protected static ?string $model = LogActivity::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-group';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('log_name')
                    ->label('Log name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('event')
                    ->label('Event')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('causer.name')
                    ->label('Causer')
                    ->searchable()
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->since()
                    ->translateLabel(),
            ])
            ->filters([
                //
            ])
            ->actions([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLogActivities::route('/'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Log Activities');
    }

    public static function getNavigationGroup(): string
    {
        return __('filament-shield::filament-shield.nav.group');
    }

    public static function getLabel(): string
    {
        return __('Log Activity');
    }
}
