<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SenderResource\Pages;
use App\Models\Provider;
use App\Models\Sender;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class SenderResource extends Resource
{
    protected static ?string $model = Sender::class;

    protected static ?string $navigationIcon = 'heroicon-o-swatch';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('sender')
                            ->label('Sender')
                            ->translateLabel()
                            ->required(),
                        Select::make('category')
                            ->label('Category')
                            ->translateLabel()
                            ->options([
                                'SenderId' => __('SenderId'),
                                'ShortNumber' => __('ShortNumber'),
                            ])
                            ->required(),
                        Select::make('type')
                            ->label('Type of Use')
                            ->translateLabel()
                            ->options([
                                'private' => __('private'),
                                'multi' => __('multi'),
                            ])
                            ->required()
                            ->default('private'),
                        RichEditor::make('notes')
                            ->label('Notes')
                            ->translateLabel(),
                    ]),
                    Section::make('')->schema([
                        Forms\Components\Repeater::make('provider')
                            ->label('Providers')
                            ->translateLabel()
                            ->relationship()
                            ->schema([
                                Select::make('provider_id')
                                    ->label('Provider')
                                    ->translateLabel()
                                    ->options(Provider::pluck('name', 'id'))
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->required(),
                                DatePicker::make('expired_at')
                                    ->label('Expired At')
                                    ->translateLabel()
                                    ->helperText(
                                        __(
                                            'The date when the provider will expire. this date just us to be alerted.',
                                        ),
                                    ),
                            ])
                            ->itemLabel(function (array $state): string {
                                if (! empty($state['id'])) {
                                    $provider = Provider::find(
                                        $state['provider_id'],
                                    );

                                    /** @var string $name */
                                    $name =
                                        $provider->name ?? __('New provider');

                                    return $name;
                                }

                                return __('New provider');
                            }),
                    ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')->schema([
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->required()
                            ->options([
                                'pending' => __('pending'),
                                'active' => __('active'),
                                'rejected' => __('rejected'),
                            ])
                            ->default('pending'),
                    ]),
                    Section::make('')
                        ->schema([
                            Placeholder::make('company_id')
                                ->label('Applicant')
                                ->translateLabel()
                                ->content(
                                    fn (?Sender $record) => $record instanceof Sender
                                        ? $record->company?->name
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Sender $record): bool => ! empty(
                                $record->company
                            ),
                        ),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?Sender $record) => $record instanceof Sender && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Sender $record,
                                    ): string => $record instanceof Sender &&
                                    $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Sender $record): bool => $record instanceof Sender,
                        ),
                ])->columnSpan(1),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'pending' => 'warning',
                            'active' => 'success',
                            'rejected' => 'danger',
                            default => 'secondary',
                        },
                    ),
                TextColumn::make('type')
                    ->label('Type of Use')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state)),
                TextColumn::make('provider.provider.name')
                    ->label('Providers')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('category')
                    ->label('Category')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(fn (string $state): string => __($state)),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading(__('No senders found'));
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditSender::class,
            Pages\ListAssociatedCompany::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSenders::route('/'),
            'create' => Pages\CreateSender::route('/create'),
            'edit' => Pages\EditSender::route('/{record}/edit'),
            'associated-companies' => Pages\ListAssociatedCompany::route(
                '/{record}/associated-companies',
            ),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Sending numbers');
    }

    public static function getLabel(): string
    {
        return __('Sending number');
    }
}
