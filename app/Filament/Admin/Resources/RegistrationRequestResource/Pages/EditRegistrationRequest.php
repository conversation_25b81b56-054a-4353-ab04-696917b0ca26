<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\RegistrationRequestResource\Pages;

use App\Actions\ApproveCustomers;
use App\Filament\Admin\Resources\RegistrationRequestResource;
use App\Models\RegistrationRequest;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\DB;
use Throwable;

class EditRegistrationRequest extends EditRecord
{
    protected static string $resource = RegistrationRequestResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make([
                        Group::make([
                            TextInput::make('name')
                                ->label('Name')
                                ->translateLabel()
                                ->required()
                                ->maxLength(255),
                            TextInput::make('phone')
                                ->label('Phone')
                                ->translateLabel()
                                ->required()
                                ->maxLength(255),
                            TextInput::make('email')
                                ->label('Email')
                                ->translateLabel()
                                ->email()
                                ->maxLength(255),
                            TextInput::make('city')
                                ->label('City')
                                ->translateLabel()
                                ->maxLength(255),
                            TextInput::make('company')
                                ->label('Company')
                                ->translateLabel()
                                ->maxLength(255),
                            TextInput::make('category')
                                ->label('Category')
                                ->translateLabel()
                                ->maxLength(255),
                            Select::make('have_smsapi')
                                ->label('Have SMS API')
                                ->translateLabel()
                                ->options([
                                    'yes' => __('Yes I have'),
                                    'no' => __('No I have not'),
                                    'developing' => __('Developing'),
                                ]),
                            TextInput::make('system_type')
                                ->label('System Type')
                                ->translateLabel()
                                ->maxLength(255),
                            Select::make('target')
                                ->label('Target')
                                ->translateLabel()
                                ->multiple()
                                ->options([
                                    'OTP' => __('OTP'),
                                    'Campaign' => __('Campaign'),
                                    'Notification' => __('Notification'),
                                ]),
                        ])->columns(2),
                        Group::make([
                            Textarea::make('notes')
                                ->label('Notes')
                                ->translateLabel(),
                        ]),
                    ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make([
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->options([
                                'pending' => __('pending'),
                                'approved' => __('approved'),
                                'rejected' => __('rejected'),
                            ]),
                    ]),

                    Section::make([
                        Placeholder::make('created_at')
                            ->label('Created At')
                            ->translateLabel()
                            ->content(fn (?RegistrationRequest $record) => $record?->created_at
                                ? $record->created_at->diffForHumans()
                                : '-'),
                        Placeholder::make('updated_at')
                            ->label('Updated At')
                            ->translateLabel()
                            ->content(
                                fn (?RegistrationRequest $record): string => $record?->updated_at
                                    ? $record->updated_at->diffForHumans()
                                    : '-',
                            ),
                    ]),
                ]),
            ])->columns([
                'lg' => 3,
            ]);
    }

    protected function getRedirectUrl(): ?string
    {
        $url = $this->getResource()::getUrl('index');

        return is_string($url) ? $url : null;
    }

    /**
     * @throws Throwable
     */
    protected function afterSave(): void
    {
        $this->ApprovedRegistration();
    }

    /**
     * @throws Throwable
     */
    private function ApprovedRegistration(): void
    {
        DB::transaction(function (): void {

            /** @var RegistrationRequest $registration_request */
            $registration_request = $this->getRecord();

            if ($registration_request->status === 'approved') {
                $approveCustomers = new ApproveCustomers();
                $approveCustomers->execute($registration_request);
            }
        });
    }
}
