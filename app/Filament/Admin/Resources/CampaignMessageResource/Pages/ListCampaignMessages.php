<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\CampaignMessageResource\Pages;

use App\Filament\Admin\Resources\CampaignMessageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListCampaignMessages extends ListRecords
{
    protected static string $resource = CampaignMessageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
