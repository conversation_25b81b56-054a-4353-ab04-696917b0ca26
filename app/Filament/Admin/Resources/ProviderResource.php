<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ProviderResource\Pages;
use App\Models\Provider;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class ProviderResource extends Resource
{
    protected static ?string $model = Provider::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube-transparent';

    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->label('Name')
                ->translateLabel()
                ->required(),
            TextInput::make('host')
                ->label('Host')
                ->translateLabel()
                ->required(),
            TextInput::make('port')
                ->label('Port')
                ->translateLabel()
                ->required(),
            TextInput::make('system_id')
                ->label('System ID')
                ->password()
                ->translateLabel()
                ->required(),
            TextInput::make('password')
                ->label('Password')
                ->password()
                ->translateLabel()
                ->required(),
            TextInput::make('system_type')
                ->label('System Type')
                ->translateLabel()
                ->required(),
            TextInput::make('connection_timeout')
                ->label('Connection Timeout')
                ->numeric()
                ->default(30)
                ->translateLabel()
                ->required(),
            TextInput::make('enquire_link_interval')
                ->label('Enquire Link Interval')
                ->numeric()
                ->default(60)
                ->translateLabel()
                ->required(),
            TextInput::make('pattern')
                ->label('Pattern')
                ->translateLabel()
                ->extraAttributes(['dir' => 'ltr'])
                ->required(),
            TextInput::make('smpp_pattern')
                ->label('SMPP Pattern')
                ->translateLabel()
                ->extraAttributes(['dir' => 'ltr'])
                ->required(),
            TextInput::make('smpp_pattern_replace')
                ->label('SMPP Pattern Replace')
                ->translateLabel()
                ->extraAttributes(['dir' => 'ltr']),
            Select::make('status')
                ->label('Status')
                ->translateLabel()
                ->options([
                    'active' => __('Active'),
                    'inactive' => __('Inactive'),
                ])
                ->default('inactive'),
            SpatieMediaLibraryFileUpload::make('provider_logos')
                ->collection('provider_logos')
                ->label('')
                ->disk('local')
                ->visibility('private')
                ->downloadable()
                ->responsiveImages(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('provider_logos')
                    ->collection('provider_logos')
                    ->label('')
                    ->searchable(),
                TextColumn::make('name')->label('Name')->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            default => 'primary',
                        },
                    ),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProviders::route('/'),
            'create' => Pages\CreateProvider::route('/create'),
            'edit' => Pages\EditProvider::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Providers');
    }

    public static function getNavigationGroup(): string
    {
        return __('Settings');
    }

    public static function getLabel(): string
    {
        return __('Provider');
    }
}
