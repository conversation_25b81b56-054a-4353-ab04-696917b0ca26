<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FAQResource\Pages;
use App\Models\FAQ;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class FAQResource extends Resource
{
    protected static ?string $model = FAQ::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('question')
                    ->label('Question')
                    ->translateLabel()
                    ->required(),
                Textarea::make('answer')
                    ->label('Answer')
                    ->translateLabel()
                    ->required(),
                Toggle::make('is_active')
                    ->label('Is Active')
                    ->default(true)
                    ->translateLabel(),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question')
                    ->label('Question')
                    ->translateLabel(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Is Active')
                    ->translateLabel()
                    ->boolean(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFAQS::route('/'),
            'create' => Pages\CreateFAQ::route('/create'),
            'edit' => Pages\EditFAQ::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('FAQ');
    }

    public static function getLabel(): string
    {
        return __('FAQ');
    }

    public static function getNavigationGroup(): string
    {
        return __('Settings');
    }
}
