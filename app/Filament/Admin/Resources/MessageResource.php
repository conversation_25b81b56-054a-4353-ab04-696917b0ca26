<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MessageResource\Pages;
use App\Jobs\MessageJobCreator;
use App\Models\Company;
use App\Models\Message;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class MessageResource extends Resource
{
    protected static ?string $model = Message::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No message found pending status'))
            ->columns([
                Tables\Columns\TextColumn::make('short_message')
                    ->label('Message')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('company.name')
                    ->label('Company')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('project.name')
                    ->label('Project')
                    ->translateLabel(),
                Tables\Columns\TextColumn::make('message_type')
                    ->label('Message Type')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(
                        fn (string $state) => mb_strtoupper($state),
                    ),
                Tables\Columns\TextColumn::make('message_consumption')
                    ->label('Consumption')
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\SelectColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'approved' => __('approved'),
                        'rejected' => __('rejected'),
                        'pending' => __('pending'),
                    ])
                    ->disabled(fn (Message $record): bool => $record->status === 'approved')
                    ->rules(['required'])
                    ->afterStateUpdated(function (Message $record): void {
                        // @codeCoverageIgnoreStart
                        if ($record->status === 'approved') {
                            // Get the JasminClient from the container and dispatch the job
                            $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
                            MessageJobCreator::dispatch($record, $jasminClient);
                        }
                        // @codeCoverageIgnoreEnd
                    }),
            ])
            ->filters([
                SelectFilter::make('company_id')
                    ->label('Company')
                    ->translateLabel()
                    ->options(fn (): array => Company::query()->pluck('name', 'id')->all()),
            ])
            ->actions([
                //
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessages::route('/'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Message Pending');
    }

    public static function getLabel(): string
    {
        return __('Message Pending');
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) Message::where('status', 'pending')->count();
    }
}
