<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CampaignMessageResource\Pages;
use App\Models\CampaignMessage;
use App\Models\Company;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class CampaignMessageResource extends Resource
{
    protected static ?string $model = CampaignMessage::class;

    protected static ?string $navigationIcon = 'heroicon-o-at-symbol';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        Forms\Components\Textarea::make('short_message')
                            ->label('Message')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),
                        Select::make('sex')
                            ->label('Sex')
                            ->translateLabel()
                            ->options([
                                'male' => __('male'),
                                'female' => __('female'),
                                'both' => __('both'),
                            ])
                            ->required()
                            ->default('both'),
                        Fieldset::make('Age_From_To')
                            ->schema([
                                TextInput::make('age_from')
                                    ->label('Age from')
                                    ->translateLabel()
                                    ->minValue(0)
                                    ->required()
                                    ->numeric(),
                                TextInput::make('age_to')
                                    ->label('Age to')
                                    ->minValue(0)
                                    ->translateLabel()
                                    ->required()
                                    ->numeric(),
                            ])
                            ->label('Age')
                            ->translateLabel(),
                        TextInput::make('quantity')
                            ->minValue(5000)
                            ->default(5000)
                            ->label('Quantity')
                            ->translateLabel()
                            ->required()
                            ->numeric(),
                        Select::make('city_id')
                            ->label('City')
                            ->translateLabel()
                            ->multiple()
                            ->required()
                            ->relationship('cities', 'name')
                            ->preload(),
                        Select::make('provider_id')
                            ->label('Provider')
                            ->translateLabel()
                            ->multiple()
                            ->required()
                            ->relationship('providers', 'name')
                            ->preload(),
                    ]),
                ])->columnSpan('2'),
                Group::make([
                    Section::make('')
                        ->label('Company')
                        ->translateLabel()
                        ->schema([
                            Select::make('company_id')
                                ->label('Company')
                                ->translateLabel()
                                ->options(
                                    Company::where('status', 'active')
                                        ->pluck('name', 'id'),
                                )
                                ->searchable()
                                ->required()
                                ->preload(),
                        ]),
                    Section::make('')->schema([
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->required()
                            ->options([
                                'active' => __('active'),
                                'inactive' => __('inactive'),
                                'pending' => __('pending'),
                                'rejected' => __('rejected'),
                            ])
                            ->default('pending'),
                    ]),
                    Section::make('')
                        ->schema([
                            Forms\Components\Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(fn (?CampaignMessage $record) => $record?->created_at?->diffForHumans() ?? '-'),
                            Forms\Components\Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (?CampaignMessage $record): string => $record?->updated_at?->diffForHumans() ?? '-',
                                ),
                        ])
                        ->visible(
                            fn (?CampaignMessage $record): bool => $record instanceof CampaignMessage,
                        ),
                ]),
            ])
            ->columns(['lg' => 3]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('company.name')
                    ->label('Company')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('short_message')
                    ->label('Message')
                    ->wrap()
                    ->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->icon(
                        fn (string $state): string => match ($state) {
                            'active' => 'heroicon-o-check-circle',
                            'inactive', 'rejected' => 'heroicon-o-x-circle',
                            'pending' => 'heroicon-o-clock',
                            default => '',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'gray',
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            default => '',
                        },
                    ),

                TextColumn::make('providers.name')
                    ->label('Providers')
                    ->badge()
                    ->translateLabel(),
                TextColumn::make('quantity')
                    ->label('Quantity')
                    ->translateLabel()
                    ->numeric()
                    ->formatStateUsing(
                        fn (CampaignMessage $record): int => $record->quantity,
                    ),
            ])
            ->filters([
                //
            ])
            ->actions([EditAction::make()])
            ->bulkActions([BulkActionGroup::make([DeleteBulkAction::make()])])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaignMessages::route('/'),
            'create' => Pages\CreateCampaignMessage::route('/create'),
            'edit' => Pages\EditCampaignMessage::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('CampaignMessage');
    }

    public static function getLabel(): string
    {
        return __('CampaignMessage');
    }
}
