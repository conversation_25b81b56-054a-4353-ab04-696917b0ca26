<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\LogActivityResource\Pages;

use App\Filament\Admin\Resources\LogActivityResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListLogActivities extends ListRecords
{
    protected static string $resource = LogActivityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
