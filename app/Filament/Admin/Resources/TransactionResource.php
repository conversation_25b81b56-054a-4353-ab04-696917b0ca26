<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TransactionResource\Pages;
use App\Models\Company;
use App\Models\Transaction;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-currency-dollar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make(__('Transaction info'))->schema([
                        TextInput::make('amount')
                            ->label('Amount')
                            ->translateLabel()
                            ->inputMode('decimal')
                            ->numeric()
                            ->helperText(
                                __(
                                    "This field's value represents the amount of the current transaction and it's value of 1 is equal to 1 LYD",
                                ),
                            )
                            ->required(),
                        Select::make('action_type')
                            ->required()
                            ->label('Action Type')
                            ->translateLabel()
                            ->options([
                                'deposit' => __('deposit'),
                                'withdraw' => __('withdraw'),
                                'charge' => __('charge'),
                                'refund' => __('refund'),
                            ])
                            ->default('deposit'),
                        Select::make('company_id')
                            ->required()
                            ->searchable()
                            ->label('Company')
                            ->translateLabel()
                            ->options(Company::pluck('name', 'id')),
                    ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')->schema([
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->options([
                                'pending' => __('pending'),
                                'completed' => __('completed'),
                                'rejected' => __('rejected'),
                            ])
                            ->default('pending'),
                    ]),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Transaction $record,
                                    ) => $record instanceof Transaction &&
                                    $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Transaction $record,
                                    ): string => $record instanceof Transaction && $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Transaction $record): bool => $record instanceof Transaction,
                        ),
                ])->columnSpan(1),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('company.name')
                    ->label('Company Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('amount')
                    ->label('Amount')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 2).
                            ' '.
                            'د.ل',
                    )
                    ->sortable(),
                TextColumn::make('action_type')
                    ->label('Action Type')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'deposit' => 'success',
                            'withdraw' => 'danger',
                            'charge' => 'warning',
                            default => 'primary',
                        },
                    ),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'pending' => 'warning',
                            'completed' => 'success',
                            'rejected' => 'danger',
                            default => 'warning',
                        },
                    ),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading(__('No transactions found'));
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'create' => Pages\CreateTransaction::route('/create'),
            'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Transactions');
    }

    public static function getNavigationGroup(): string
    {
        return __('Balance and Subscriptions');
    }

    public static function getLabel(): string
    {
        return __('Transaction');
    }
}
