<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RegistrationRequestResource\Pages;
use App\Models\RegistrationRequest;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class RegistrationRequestResource extends Resource
{
    protected static ?string $model = RegistrationRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No Registration Request found'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('company')
                    ->label('Company')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('city')
                    ->label('City')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('category')
                    ->label('Category')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('have_smsapi')
                    ->label('Have SMS API')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'yes' => __('Yes I have'),
                        'no' => __('No I have not'),
                        'developing' => __('Developing'),
                        default => __('No I have not'),
                    })
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'pending' => 'warning',
                        default => 'primary',
                    }, )
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => __($state)),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRegistrationRequests::route('/'),
            'edit' => Pages\EditRegistrationRequest::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Registration Requests');
    }

    public static function getLabel(): string
    {
        return __('Registration Requests');
    }
}
