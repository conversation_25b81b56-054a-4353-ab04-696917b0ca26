<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\SubscriptionResource\Pages;

use App\Enums\TransactionStatus;
use App\Filament\Admin\Resources\SubscriptionResource;
use App\Models\Company;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use App\Models\Transaction;
use Carbon\CarbonImmutable;
use DB;
use Filament\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Collection;

final class ListSubscriptions extends ListRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('new-subscription')
                ->label('New Subscription')
                ->translateLabel()
                /** @phpstan-ignore-next-line  */
                ->action(fn (array $data) => $this->subscribeTo($data))
                ->form($this->newSubscriptionForm()),
        ];
    }

    /**
     * Check if the project already has an active subscription with overlapping features.
     */
    private function hasDuplicateFeatures(Project $project, Plan $plan): bool
    {
        $plan->load('feature');

        /** @var Collection<int, Subscription> $activeSubscriptions */
        $activeSubscriptions = $project
            ->subscription()
            ->where('expired_at', '>=', now())
            ->where('canceled_at', null)
            ->with('plan.feature')
            ->get();

        $selectedFeatureNames = $plan->feature->pluck('name');

        foreach ($activeSubscriptions as $subscription) {
            assert($subscription->plan instanceof Plan);
            $activeFeatureNames = $subscription->plan->feature->pluck('name');

            if (
                $selectedFeatureNames
                    ->intersect($activeFeatureNames)
                    ->isNotEmpty()
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Process the subscription and create a transaction.
     */
    private function processSubscription(
        Company $company,
        Project $project,
        Plan $plan,
    ): void {
        DB::transaction(function () use ($company, $project, $plan): void {
            $subscription = $project->subscribeTo(
                $plan,
                CarbonImmutable::now(),
                $plan->periodicity_quantity,
            );

            Transaction::create([
                'amount' => $plan->price * -1,
                'action_type' => 'charge',
                'status' => TransactionStatus::Completed,
                'description' => 'Subscription to '.$plan->name,
                'company_id' => $company->id,
                'subscription_id' => $subscription->id,
            ]);

            Notification::make()
                ->title(__('Subscription Successful'))
                ->body(__('You have successfully subscribed to ').$plan->name)
                ->success()
                ->send();
        });
    }

    /**
     * @return array<Component>
     */
    private function newSubscriptionForm(): array
    {
        return [
            Select::make('company_id')
                ->label('Company')
                ->required()
                ->searchable()
                ->live()
                ->options(
                    Company::where('status', 'active')
                        ->pluck('name', 'id'),
                ),

            Select::make('project_id')
                ->label('Project')
                ->required()
                ->searchable()
                ->live()
                ->disabled(fn (Get $get): bool => $get('company_id') === null)
                ->options(fn (Get $get) => Project::where(
                    'company_id',
                    $get('company_id'),
                )->pluck('name', 'id')),
            Select::make('plan_id')
                ->label('Plan')
                ->required()
                ->searchable()
                ->live()
                ->options(function () {
                    /** @var Collection<int, Plan> $plans */
                    $plans = Plan::where('status', 'active')->get();

                    return $plans
                        ->mapWithKeys(
                            fn (Plan $plan) => [
                                $plan->id => sprintf(
                                    '%s - %s %s (%s)',
                                    __($plan->periodicity_type),
                                    number_format($plan->price, 2),
                                    __('LYD'),
                                    $plan->name,
                                ),
                            ],
                        )
                        ->toArray();
                }),
        ];
    }

    /** @param array{company_id: int, project_id: int, plan_id: int} $data */
    private function subscribeTo(array $data): void
    {
        $company = Company::findOrFail($data['company_id']);
        $project = Project::findOrFail($data['project_id']);
        $plan = Plan::findOrFail($data['plan_id']);

        // Check for duplicate features in active subscriptions
        if ($this->hasDuplicateFeatures($project, $plan)) {
            Notification::make()
                ->title(__('Duplicate Plan Features'))
                ->body(
                    __(
                        'already subscribed to a You areplan with similar features.',
                    ),
                )
                ->danger()
                ->send();

            return;
        }

        // Check if the company has sufficient balance
        if ($company->balance() < $plan->price) {
            Notification::make()
                ->title(__('Insufficient Balance'))
                ->body(
                    __(
                        'You do not have enough balance to subscribe to this plan.',
                    ),
                )
                ->danger()
                ->send();

            return;
        }
        $this->processSubscription($company, $project, $plan);
    }
}
