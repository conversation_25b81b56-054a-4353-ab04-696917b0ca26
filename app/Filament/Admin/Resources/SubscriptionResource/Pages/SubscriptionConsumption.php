<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\SubscriptionResource\Pages;

use App\Filament\Admin\Resources\SubscriptionResource;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class SubscriptionConsumption extends ManageRelatedRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected static string $relationship = 'consumptions';

    public static function getNavigationLabel(): string
    {
        // @codeCoverageIgnoreStart
        return __('Subscription Consumption');
        // @codeCoverageIgnoreEnd
    }

    public function getTitle(): string
    {
        return __('Subscription Consumption');
    }

    public function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No record founds'))
            ->columns([
                TextColumn::make('consumption')
                    ->label('Consumption')
                    ->translateLabel(),
                TextColumn::make('type')
                    ->badge()
                    ->label('Type')
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ]);
    }
}
