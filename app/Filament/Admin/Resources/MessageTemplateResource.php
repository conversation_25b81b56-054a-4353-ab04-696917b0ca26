<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Enums\MessageTemplateParameterType;
use App\Enums\MessageTemplateStatus;
use App\Enums\MessageTemplateType;
use App\Filament\Admin\Resources\MessageTemplateResource\Pages;
use App\Models\Company;
use App\Models\MessageTemplate;
use App\Models\Project;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class MessageTemplateResource extends Resource
{
    protected static ?string $model = MessageTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('display_name')
                            ->label('Display Name')
                            ->translateLabel()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (
                                string $operation,
                                string $state,
                                Set $set,
                            ): void {
                                if ($operation === 'create') {
                                    $set('short_name', Str::slug($state));
                                }
                            })
                            ->required(),
                        TextInput::make('short_name')
                            ->label('Short Name')
                            ->translateLabel()
                            ->helperText(__('This name will be used to identify the template in the Api.'))
                            ->required(),
                        Select::make('company_id')
                            ->label('Company')
                            ->translateLabel()
                            ->live()
                            ->options(Company::where('status', 'active')->pluck('name', 'id'))
                            ->afterStateUpdated(function (string $state, Set $set): void {
                                $set('project_id', null);
                            })
                            ->required(),
                        Select::make('project_id')
                            ->label('Project')
                            ->translateLabel()
                            ->options(fn (Get $get) => Project::where('company_id', $get('company_id'))
                                ->where('status', 'active')
                                ->pluck('name', 'id'))
                            ->required(),
                        Textarea::make('content')
                            ->label('Message')
                            ->translateLabel()
                            ->live(onBlur: true)
                            ->helperText(__('You can use parameters in the message by enclosing them in double curly braces, e.g. {{name}}.'))
                            ->afterStateUpdated(function (string $state, Set $set): void {

                                $set('parameters', []);

                                preg_match_all('/{{(.*?)}}/', $state, $matches);
                                $parameters = [];
                                foreach ($matches[1] as $match) {
                                    $parameters[] = [
                                        'name' => $match,
                                        'type' => MessageTemplateParameterType::string->value,
                                        'max_limit' => 1,
                                    ];
                                }
                                $set('parameters', $parameters);
                            })
                            ->required(),
                        Select::make('type')
                            ->label('Type')
                            ->translateLabel()
                            ->options([
                                MessageTemplateType::marketing->value => __(MessageTemplateType::marketing->value),
                                MessageTemplateType::transactional->value => __(MessageTemplateType::transactional->value),
                            ])
                            ->required(),
                        Repeater::make('parameters')
                            ->label('Parameters')
                            ->translateLabel()
                            ->relationship('parameters')
                            ->reorderable(false)
                            ->collapsible()
                            ->schema([
                                Grid::make()
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Name')
                                            ->translateLabel()
                                            ->rules(function (Get $get): array {
                                                /** @var string $messageTemplateId */
                                                $messageTemplateId = $get('../../id');

                                                return [
                                                    Rule::unique('message_template_parameters', 'name')
                                                        ->where('message_template_id', $messageTemplateId)
                                                        ->ignore($get('id')),
                                                ];
                                            })
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (
                                                string $operation,
                                                string $state,
                                                Set $set,
                                            ): void {
                                                if ($operation === 'create') {
                                                    $set('name', Str::slug($state));
                                                }
                                            })
                                            ->required(),
                                        TextInput::make('max_limit')
                                            ->label('Max Limit')
                                            ->translateLabel()
                                            ->helperText(__('The maximum number of characters allowed for this parameter is 100.'))
                                            ->default(1)
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(100)
                                            ->required(),
                                        Select::make('type')
                                            ->label('Type')
                                            ->translateLabel()
                                            ->options([
                                                MessageTemplateParameterType::string->value => __(MessageTemplateParameterType::string->value),
                                                MessageTemplateParameterType::number->value => __(MessageTemplateParameterType::number->value),
                                                MessageTemplateParameterType::date->value => __(MessageTemplateParameterType::date->value),
                                            ])
                                            ->required(),
                                    ])
                                    ->columns(3),
                            ]),

                    ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')
                        ->schema([
                            Select::make('status')
                                ->label('Status')
                                ->translateLabel()
                                ->options([
                                    MessageTemplateStatus::active->value => __(MessageTemplateStatus::active->value),
                                    MessageTemplateStatus::inactive->value => __(MessageTemplateStatus::inactive->value),
                                    MessageTemplateStatus::pending->value => __(MessageTemplateStatus::pending->value),
                                    MessageTemplateStatus::rejected->value => __(MessageTemplateStatus::rejected->value),
                                ]),
                        ]),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?MessageTemplate $record) => $record instanceof MessageTemplate && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?MessageTemplate $record,
                                    ): string => $record instanceof MessageTemplate &&
                                    $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?MessageTemplate $record): bool => $record instanceof MessageTemplate,
                        ),
                ])
                    ->columnSpan(1),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('company.name')
                    ->label('Company')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('display_name')
                    ->label('Display Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('short_name')
                    ->label('Short Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            MessageTemplateStatus::active->value => 'success',
                            MessageTemplateStatus::inactive->value, MessageTemplateStatus::rejected->value => 'danger',
                            MessageTemplateStatus::pending->value => 'warning',
                            default => 'primary',
                        },
                    ),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessageTemplates::route('/'),
            'create' => Pages\CreateMessageTemplate::route('/create'),
            'edit' => Pages\EditMessageTemplate::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Message Templates');
    }

    public static function getLabel(): string
    {
        return __('Message Template');
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) MessageTemplate::where('status', 'pending')->count();
    }
}
