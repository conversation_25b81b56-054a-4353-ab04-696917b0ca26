<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SubscriptionResource\Pages;
use App\Models\FeatureConsumption;
use App\Models\Plan;
use App\Models\Subscription;
use Carbon\CarbonImmutable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

final class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['plan']))
            ->columns([
                TextColumn::make('project.company.name')
                    ->label('Company Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('project.name')
                    ->label('Project Name')
                    ->translateLabel(),
                TextColumn::make('plan.name')
                    ->label('Plan Name')
                    ->badge()
                    ->translateLabel(),
                TextColumn::make('started_at')
                    ->label('Started At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('expired_at')
                    ->label('Expired At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('id')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->color(function (Subscription $record): string {
                        if ($record->canceled_at) {
                            return 'danger';
                        }

                        if ($record->expired_at >= now()) {
                            return 'success';
                        }

                        return 'danger';
                    })
                    ->formatStateUsing(function (Subscription $record) {
                        if ($record->canceled_at) {
                            return __('Canceled');
                        }

                        if ($record->expired_at >= now()) {
                            return __('Active');
                        }

                        return __('Expired');
                    }),
                TextColumn::make('canceled_at')
                    ->label('Canceled At')
                    ->translateLabel()
                    ->date(),
                TextColumn::make('project_id')
                    ->label('Balance')
                    ->translateLabel()
                    ->formatStateUsing(function (Subscription $record): string {
                        assert($record->plan instanceof Plan);
                        $feature = $record->plan->features()->firstOrFail();

                        $balance = FeatureConsumption::where(
                            'feature_id',
                            $feature->feature_id,
                        )
                            ->where('subscription_id', $record->id)
                            ->sum('consumption');

                        $total = $feature->charges;

                        return number_format((int) $total).
                            ' '.
                            '/ '.
                            number_format($total - $balance);
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('cancel')
                    ->label('Cancel')
                    ->translateLabel()
                    ->requiresConfirmation()
                    ->disabled(fn (Subscription $record) => $record->canceled_at)
                    ->action(function (Subscription $record): void {
                        $record->canceled_at = new CarbonImmutable();
                        $record->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->recordUrl(fn (Subscription $record): string => SubscriptionResource::getUrl('consumption', ['record' => $record]));
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'consumption' => Pages\SubscriptionConsumption::route(
                '/{record}/subscription-consumption',
            ),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Subscriptions');
    }

    public static function getNavigationGroup(): string
    {
        return __('Balance and Subscriptions');
    }

    public static function getLabel(): string
    {
        return __('Subscription');
    }
}
