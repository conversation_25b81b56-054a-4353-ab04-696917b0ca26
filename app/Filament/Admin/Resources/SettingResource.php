<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SettingResource\Pages;
use App\Models\Plan;
use App\Models\Sender;
use App\Models\Setting;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make('')
                    ->schema(
                        fn (Get $get): array => match ($get('key')) {
                            'free_plan' => [
                                Select::make('value')
                                    ->label('Free Plan')
                                    ->translateLabel()
                                    ->multiple()
                                    ->maxItems(2)
                                    ->minItems(1)
                                    ->required()
                                    ->options(Plan::pluck('name', 'id')),
                            ],
                            'is_free_plan' => [
                                Select::make('value')
                                    ->label('Is Free Plan')
                                    ->translateLabel()
                                    ->required()
                                    ->options([
                                        0 => __('No'),
                                        1 => __('Yes'),
                                    ]),
                            ],
                            'default_sender' => [
                                Select::make('value')
                                    ->label('Default Sender')
                                    ->translateLabel()
                                    ->required()
                                    ->options(Sender::pluck('sender', 'id')),
                            ],
                            default => [
                                TextInput::make('value')
                                    ->label('Value')
                                    ->required()
                                    ->translateLabel(),
                            ],
                        },
                    ),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('Key')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (Setting $record): string => __($record->key),
                    ),
                Tables\Columns\TextColumn::make('value')
                    ->label('Value')
                    ->translateLabel()
                    ->formatStateUsing(
                        function (Setting $record): string {
                            if ($record->key === 'free_plan') {
                                return Plan::whereIn('id', $record->value)
                                    ->pluck('name')
                                    ->implode(', ');
                            }

                            if ($record->key === 'is_free_plan') {
                                return $record->value ? __('Yes') : __('No');
                            }

                            if ($record->key === 'default_sender') {
                                /** @var Sender $sender */
                                $sender = Sender::findOrFail($record->value);

                                return $sender->sender;
                            }

                            if ($record->key === 'single_sms_cost') {
                                $value = is_numeric($record->value) ? (float) $record->value : 0;

                                return ($value / 1000).' '.__('LYD');
                            }

                            if ($record->key === 'multiple_sms_cost') {
                                $value = is_numeric($record->value) ? (float) $record->value : 0;

                                return ($value / 1000).' '.__('LYD');
                            }

                            if ($record->key === 'multiple_sms_threshold') {
                                $value = is_numeric($record->value) ? (int) $record->value : 0;

                                return $value.' '.__('Message');
                            }
                            /** @var string $value */
                            $value = $record->value;

                            return $value;
                        },
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettings::route('/'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return __('Settings');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Settings');
    }

    public static function getLabel(): string
    {
        return __('Settings');
    }
}
