<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompanyResource\Pages;
use App\Filament\Admin\Resources\CompanyResource\Pages\ListCompanyTransaction;
use App\Filament\Admin\Resources\CompanyResource\Pages\ManageCompanyUsers;
use App\Models\City;
use App\Models\Company;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-lifebuoy';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make(__('Company Info'))->schema([
                        Group::make()
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required(),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->required(),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->required(),
                                TextInput::make('category')
                                    ->label('Company type')
                                    ->translateLabel()
                                    ->required(),
                                TextInput::make('address')
                                    ->label('Address')
                                    ->translateLabel()
                                    ->required(),
                                Select::make('city_id')
                                    ->label('City')
                                    ->translateLabel()
                                    ->options(fn () => City::query()->pluck('name', 'id')->toArray()),
                            ])
                            ->columns(2),
                    ]),

                    Section::make(__('Company Description'))->schema([
                        RichEditor::make('description')
                            ->label('Description')
                            ->translateLabel()
                            ->required(),
                    ]),

                    Section::make(__('Company Representatives'))
                        ->collapsible()
                        ->schema([
                            Repeater::make('representatives')
                                ->label('')
                                ->relationship('representatives')
                                ->addActionLabel(__('Add Representative'))
                                ->schema([
                                    Group::make()
                                        ->schema([
                                            TextInput::make('name')
                                                ->label('Name')
                                                ->translateLabel()
                                                ->required(),
                                            TextInput::make('phone')
                                                ->label('Phone')
                                                ->translateLabel()
                                                ->required(),
                                            TextInput::make('email')
                                                ->label('Email')
                                                ->translateLabel()
                                                ->email(),
                                            TextInput::make('position')
                                                ->label('Position')
                                                ->translateLabel(),
                                        ])
                                        ->columns(2),
                                ]),
                        ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(fn (?Company $record) => $record?->created_at
                                    ? $record->created_at->diffForHumans()
                                    : '-'),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (?Company $record): string => $record?->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Company $record): bool => $record instanceof Company,
                        ),
                    Section::make(__('Company Status'))->schema([
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->options([
                                'active' => __('Active'),
                                'inactive' => __('Inactive'),
                            ])
                            ->default('inactive'),
                        Toggle::make('auto_approve')
                            ->label('Auto Approve')
                            ->translateLabel()
                            ->helperText(__('Company can send messages without approval')),
                    ]),
                    Section::make(__('Company Logo'))->schema([
                        SpatieMediaLibraryFileUpload::make('logo')
                            ->collection('company_logos')
                            ->label('')
                            ->disk('local')
                            ->visibility('private')
                            ->downloadable()
                            ->responsiveImages(),
                    ]),
                    Section::make(__('Company Documents'))->schema([
                        SpatieMediaLibraryFileUpload::make('company_documents')
                            ->label('')
                            ->disk('local')
                            ->visibility('private')
                            ->downloadable()
                            ->multiple()
                            ->collection('company_documents'),
                    ]),
                ]),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Company Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('category')
                    ->label('Category')
                    ->translateLabel(),
                TextColumn::make('city.name')
                    ->label('City')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            default => 'primary',
                        },
                    ),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditCompany::class,
            ManageCompanyUsers::class,
            ListCompanyTransaction::class,
            Pages\ManageSenders::class,
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
            'manage-users' => ManageCompanyUsers::route(
                '/{record}/manage-users',
            ),
            'transactions' => ListCompanyTransaction::route(
                '/{record}/transactions',
            ),
            'senders' => Pages\ManageSenders::route('/{record}/senders'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Companies');
    }

    public static function getLabel(): string
    {
        return __('Company');
    }
}
