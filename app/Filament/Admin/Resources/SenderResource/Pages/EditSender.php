<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\SenderResource\Pages;

use App\Filament\Admin\Resources\SenderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditSender extends EditRecord
{
    protected static string $resource = SenderResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Sender');
    }

    public function getTitle(): string
    {
        return __('Edit Sender');
    }

    protected function getHeaderActions(): array
    {
        return [Actions\DeleteAction::make()];
    }
}
