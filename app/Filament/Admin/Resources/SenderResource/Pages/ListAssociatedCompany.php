<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\SenderResource\Pages;

use App\Filament\Admin\Resources\SenderResource;
use App\Models\Company;
use App\Models\Sender;
use Carbon\Carbon;
use Exception;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

/**
 * @property Sender $record
 */
final class ListAssociatedCompany extends ManageRelatedRecords
{
    protected static string $resource = SenderResource::class;

    protected static string $relationship = 'companies';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function getNavigationLabel(): string
    {
        return __('Companies');
    }

    public function getTitle(): string
    {
        return __('Companies');
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->emptyStateHeading(__('No Companies found'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Company Name')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('category')
                    ->label('Category')
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('id')
                    ->label('Expired At')
                    ->translateLabel()
                    ->formatStateUsing(function (Company $record): string {
                        if (! $record->pivot?->getAttributeValue('expired_at')) {
                            return '-';
                        }

                        return Carbon::parse(
                            /** @phpstan-ignore-next-line */
                            $record->pivot->getAttributeValue('expired_at'),
                        )->format('Y-m-d');
                    })
                    ->searchable(),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form([
                        Select::make('company_id')
                            ->label('Company')
                            ->translateLabel()
                            ->options(Company::pluck('name', 'id')),
                        DatePicker::make('expired_at')
                            ->label('Expired At')
                            ->translateLabel(),
                    ])
                    ->action(function (array $data): void {
                        if ($this->record->type !== 'multi') {
                            Notification::make()
                                ->title(__('Error'))
                                ->body(
                                    __(
                                        'You can not attach this sender to multiple companies',
                                    ),
                                )
                                ->danger()
                                ->icon('heroicon-o-exclamation-circle')
                                ->send();

                            return;
                        }

                        try {
                            $this->record
                                ->companies()
                                ->attach($data['company_id'], [
                                    'expired_at' => $data['expired_at'],
                                ]);

                            Notification::make()
                                ->title(__('Success'))
                                ->body(
                                    __(
                                        'The sender has been attached to the company',
                                    ),
                                )
                                ->success()
                                ->icon('heroicon-o-check-circle')
                                ->send();
                        } catch (Exception) {
                            Notification::make()
                                ->title(__('Error'))
                                ->body(
                                    __(
                                        'The sender could not be attached to the company',
                                    ),
                                )
                                ->danger()
                                ->send();

                            return;
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\DetachAction::make(),
                Tables\Actions\Action::make('edit-expiration')
                    ->label('Edit Expiration')
                    ->translateLabel()
                    ->form([
                        DatePicker::make('expired_at')
                            ->label('Expired At')
                            ->translateLabel(),
                    ])
                    ->action(function (array $data, Company $record): void {
                        $record
                            ->senders()
                            ->updateExistingPivot($this->record->id, [
                                'expired_at' => $data['expired_at'],
                            ]);

                        Notification::make()
                            ->title(__('Success'))
                            ->body(
                                __(
                                    'The Expiration has been updated to the company',
                                ),
                            )
                            ->success()
                            ->icon('heroicon-o-check-circle')
                            ->send();
                    }),
            ])
            ->bulkActions([Tables\Actions\DetachBulkAction::make()]);
    }
}
