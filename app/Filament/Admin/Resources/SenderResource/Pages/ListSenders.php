<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\SenderResource\Pages;

use App\Filament\Admin\Resources\SenderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListSenders extends ListRecords
{
    protected static string $resource = SenderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
