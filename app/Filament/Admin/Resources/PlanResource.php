<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources;

use App\Enums\PeriodicityType;
use App\Filament\Admin\Resources\PlanResource\Pages;
use App\Models\Feature;
use App\Models\Plan;
use DB;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\QueryException;

final class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('name')
                            ->label('Plan Name')
                            ->translateLabel()
                            ->required(),
                        Select::make('periodicity_type')
                            ->label('Periodicity Type')
                            ->translateLabel()
                            ->options([
                                PeriodicityType::Yearly->value => __('Yearly'),
                                PeriodicityType::Monthly->value => __('Monthly'),
                                PeriodicityType::Quarterly->value => __('Quarterly'),
                                PeriodicityType::Weekly->value => __('Weekly'),
                                PeriodicityType::Daily->value => __('Daily'),
                            ])
                            ->required(),
                        TextInput::make('periodicity_quantity')
                            ->label('Periodicity Quantity')
                            ->translateLabel()
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->helperText(
                                __(
                                    'The number of periodicity quantity, eg: if you set periodicity type yearly and periodicity quantity to 2, then the plan will be active for 2 years.',
                                ),
                            )
                            ->required(),
                        TextInput::make('price')
                            ->label('Price')
                            ->translateLabel()
                            ->numeric()
                            ->minValue(0)
                            ->required(),
                        TextInput::make('over_quota_price')
                            ->label('Over Quota Price')
                            ->translateLabel()
                            ->numeric()
                            ->minValue(0)
                            ->required(),
                    ]),
                    Repeater::make('features')
                        ->label('Features')
                        ->translateLabel()
                        ->relationship('features')
                        ->disableItemDeletion()
                        ->addable(false)
                        ->schema([
                            Select::make('feature_id')
                                ->label('Feature')
                                ->translateLabel()
                                ->options(Feature::pluck('name', 'id')),
                            TextInput::make('charges')
                                ->label('Charges')
                                ->translateLabel()
                                ->numeric(),
                        ])
                        ->itemLabel(function (array $state) {
                            /** @var Feature|null $feature */
                            $feature = Feature::find($state['feature_id']);
                            if ($feature) {
                                return $feature->name;
                            }

                            return __('New Feature');
                        }),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')
                        ->schema([
                            TagsInput::make('tags')
                                ->label('Tags')
                                ->translateLabel()
                                ->suggestions([
                                    'popular',
                                    'recommended',
                                    'new',
                                    'limited',
                                ]),
                            Select::make('status')
                                ->label('Status')
                                ->translateLabel()
                                ->options([
                                    'active' => __('Active'),
                                    'inactive' => __('Inactive'),
                                ])
                                ->default('inactive'),
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?Plan $record) => $record instanceof Plan && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Plan $record,
                                    ): string => $record instanceof Plan && $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ]),
                ])->columnSpan(1),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Plan Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('feature.name')
                    ->label('Features')
                    ->translateLabel(),
                TextColumn::make('features.charges')
                    ->label('Charges')
                    ->translateLabel(),
                TextColumn::make('price')
                    ->label('Price')
                    ->translateLabel()
                    ->sortable()
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 2).
                            ' '.
                            'د.ل',
                    ),
                TextColumn::make('over_quota_price')
                    ->label('Over Quota Price')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 3).
                            ' '.
                            'د.ل',
                    ),
                TextColumn::make('periodicity_type')
                    ->label('Periodicity Type')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state)),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make('delete')
                    ->using(function (Plan $record): void {
                        try {
                            DB::transaction(function () use ($record): void {
                                $record->delete();

                                Notification::make()
                                    ->title(__('Plan deleted successfully'))
                                    ->success()
                                    ->send();
                            });
                        } catch (QueryException) {
                            Notification::make()
                                ->title(__('Cannot delete plan'))
                                ->body(__('This plan is assigned to one or more subscriptions.'))
                                ->danger()
                                ->persistent()
                                ->send();
                        }
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Plans');
    }

    public static function getNavigationGroup(): string
    {
        return __('Balance and Subscriptions');
    }

    public static function getLabel(): string
    {
        return __('Plan');
    }
}
