<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use App\Models\Role;
use App\Models\User;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

final class ManageCompanyUsers extends ManageRelatedRecords
{
    protected static string $resource = CompanyResource::class;

    protected static string $relationship = 'users';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function getNavigationLabel(): string
    {
        return __('Users');
    }

    public function getTitle(): string
    {
        return __('Users');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make(__('User info'))
                        ->translateLabel()
                        ->schema([
                            TextInput::make('name')
                                ->label('Name')
                                ->translateLabel()
                                ->required(),
                            TextInput::make('email')
                                ->label('Email')
                                ->translateLabel()
                                ->email()
                                ->unique(ignoreRecord: true)
                                ->required(),
                        ]),
                    Section::make(__('Auth Info'))->schema([
                        TextInput::make('password')
                            ->label('Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->dehydrated(
                                fn (?string $state): bool => filled($state),
                            )
                            ->required(
                                fn (string $operation): bool => $operation ===
                                    'create',
                            )
                            ->confirmed(),
                        TextInput::make('password_confirmation')
                            ->label('Confirm Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->dehydrated(false)
                            ->required(
                                fn (string $operation): bool => $operation ===
                                    'create',
                            ),
                    ]),
                    Section::make(__('Roles'))->schema([
                        Select::make('roles')
                            ->nullable()
                            ->label('Roles')
                            ->translateLabel()
                            ->relationship('roles', 'name')
                            ->multiple()
                            ->options(Role::all()->pluck('name', 'id')),
                    ]),
                ])->columnSpan(
                    fn (?User $record): int => $record instanceof User ? 2 : 3,
                ),
                Group::make([
                    Section::make('')->schema([
                        Placeholder::make('created_at')
                            ->label('Created At')
                            ->translateLabel()
                            ->content(
                                fn (?User $record) => $record instanceof User && $record->created_at
                                    ? $record->created_at->diffForHumans()
                                    : '-',
                            ),
                        Placeholder::make('updated_at')
                            ->label('Updated At')
                            ->translateLabel()
                            ->content(
                                fn (?User $record): string => $record instanceof User && $record->updated_at
                                    ? $record->updated_at->diffForHumans()
                                    : '-',
                            ),
                    ]),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?User $record): bool => $record instanceof User,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public function table(Table $table): Table
    {
        /** @var Company $company */
        $company = $this->getOwnerRecord();

        return $table
            ->recordTitleAttribute('name')
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('id')
                    ->label('Default User')
                    ->translateLabel()
                    ->formatStateUsing(function (
                        User $record,
                    ): HtmlString|string {
                        /** @var Company $company */
                        $company = $this->getOwnerRecord();
                        if ($company->default_user_id === $record->id) {
                            return new HtmlString(
                                svg(
                                    'heroicon-o-check-badge',
                                    'h-5 text-primary-600'.
                                        (app()->environment('testing')
                                            ? ' default-user-icon'
                                            : ''),
                                )->toHtml(),
                            );
                        }

                        return '';
                    }),
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel(),
            ])
            ->filters([Tables\Filters\TrashedFilter::make()])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add User')
                    ->translateLabel(),
                Tables\Actions\AttachAction::make(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Impersonate::make()
                        ->redirectTo('/company/'.$company->id),
                    Tables\Actions\Action::make('set_default')
                        ->label('Default User')
                        ->translateLabel()
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-badge')
                        ->action(function (User $record): void {
                            /** @var Company $company */
                            $company = $this->getOwnerRecord();
                            if (
                                empty($company->default_user_id) ||
                                $company->default_user_id !== $record->id
                            ) {
                                $company->default_user_id = $record->id;
                                $company->save();

                                Notification::make()
                                    ->title(__('User Successfully Assigned'))
                                    ->body(
                                        __(
                                            'You have successfully assigned user',
                                        ),
                                    )
                                    ->success()
                                    ->send();
                            }

                        })
                        ->visible(function (User $record): bool {
                            $company = $this->getOwnerRecord();

                            return empty($company->default_user_id) ||
                                $record->id !== $company->default_user_id;
                        }),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DetachAction::make()->visible(function (
                        User $record,
                    ): bool {
                        /** @var Company $company */
                        $company = $this->getOwnerRecord();

                        return $company->default_user_id !== $record->id;
                    }),
                    Tables\Actions\DeleteAction::make()->visible(function (
                        User $record,
                    ): bool {
                        /** @var Company $company */
                        $company = $this->getOwnerRecord();

                        return $company->default_user_id !== $record->id;
                    }),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ]),
            ])
            /*->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])*/
            ->modifyQueryUsing(
                fn (Builder $query) => $query->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]),
            )
            ->emptyStateHeading(__('No users found'));
    }
}
