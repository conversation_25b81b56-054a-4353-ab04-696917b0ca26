<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditCompany extends EditRecord
{
    protected static string $resource = CompanyResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Company');
    }

    protected function getHeaderActions(): array
    {
        return [Actions\DeleteAction::make()];
    }
}
