<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class ListCompanyTransaction extends ManageRelatedRecords
{
    protected static string $resource = CompanyResource::class;

    protected static string $relationship = 'transactions';

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function getNavigationLabel(): string
    {
        return __('Transactions');
    }

    public function getTitle(): string
    {
        return __('Transactions');
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->emptyStateHeading(__('No transactions found'))
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('amount')
                    ->label('Amount')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 2).
                            ' '.
                            'د.ل',
                    )
                    ->sortable(),
                TextColumn::make('action_type')
                    ->label('Action Type')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'deposit' => 'success',
                            'withdraw' => 'danger',
                            'charge' => 'warning',
                            default => 'primary',
                        },
                    ),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'completed' => 'success',
                            'rejected' => 'danger',
                            default => 'warning',
                        },
                    ),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->filters([
                //
            ]);
    }
}
