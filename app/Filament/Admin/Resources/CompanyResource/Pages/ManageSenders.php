<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use App\Models\Sender;
use Exception;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

/**
 * @property Company $record
 */
final class ManageSenders extends ManageRelatedRecords
{
    protected static string $resource = CompanyResource::class;

    protected static string $relationship = 'senders';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function getNavigationLabel(): string
    {
        return __('Senders');
    }

    public function getTitle(): string
    {
        return __('Senders');
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('sender')
            ->emptyStateHeading(__('No sending numbers found'))
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'pending' => 'warning',
                            'active' => 'success',
                            'rejected' => 'danger',
                            default => 'secondary',
                        },
                    ),
                TextColumn::make('provider.provider.name')
                    ->label('Providers')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('type')
                    ->label('Type of Use')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(fn (string $state): string => __($state)),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form([
                        Select::make('sender_id')
                            ->label('Sender')
                            ->translateLabel()
                            ->options(Sender::pluck('sender', 'id')),
                        DatePicker::make('expired_at')
                            ->label('Expired At')
                            ->translateLabel(),
                    ])
                    ->action(
                        /** @param array<string, mixed> $data */
                        function (array $data): void {
                            try {
                                /** @var Sender $sender */
                                $sender = Sender::findOrFail($data['sender_id']);

                                if ($sender->type !== 'multi') {
                                    Notification::make()
                                        ->title(__('Error'))
                                        ->body(
                                            __(
                                                'You can not attach this sender to multiple companies',
                                            ),
                                        )
                                        ->danger()
                                        ->icon('heroicon-o-exclamation-circle');

                                    return;
                                }

                                $this->record
                                    ->senders()
                                    ->attach($data['sender_id'], [
                                        'expired_at' => $data['expired_at'],
                                    ]);

                                Notification::make()
                                    ->title(__('Success'))
                                    ->body(
                                        __(
                                            'The sender has been attached to the company',
                                        ),
                                    )
                                    ->send()
                                    ->success()
                                    ->icon('heroicon-o-check-circle');
                            } catch (Exception) {
                                Notification::make()
                                    ->title(__('Error'))
                                    ->body(
                                        __(
                                            'The sender could not be attached to the company',
                                        ),
                                    )
                                    ->send()
                                    ->danger();
                            }
                        }),
            ])
            ->actions([Tables\Actions\DetachAction::make()])
            ->bulkActions([Tables\Actions\DetachBulkAction::make()]);
    }
}
