<?php

declare(strict_types=1);

namespace App\Filament\Admin\Widgets;

use App\Models\MessageReceipt;
use Carbon\Carbon;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TotalSMS extends ChartWidget
{
    public ?string $filter = 'all';

    protected int|string|array $columnSpan = 'full';

    protected static ?string $maxHeight = '380px';

    protected static ?string $pollingInterval = null;

    private int $days = 15;

    public function getDescription(): string|Htmlable|null
    {
        return __('Total Messages in the last 15 days');
    }

    public function getHeading(): string|Htmlable|null
    {
        return __('Total Messages');
    }

    protected function getFilters(): ?array
    {
        return [
            'all' => __('All Statuses'),
            'pending' => __('pending'),
            'sent' => __('sent'),
            'delivered' => __('delivered'),
            'failed' => __('failed'),
        ];
    }

    protected function getData(): array
    {
        $startDate = now()->subDays($this->days - 1)->startOfDay();
        $endDate = now()->endOfDay();

        $activeFilter = $this->filter;

        $statuses = $activeFilter === 'all' ? ['pending', 'sent', 'delivered', 'failed'] : [$activeFilter];

        $colors = [
            'pending' => '#FFC107',
            'sent' => '#8BC34A',
            'delivered' => '#64B5F6',
            'failed' => '#EF5350',
        ];

        // Generate the date labels for the past $this->days
        /** @var array<int, string> $dates */
        $dates = collect(range(0, $this->days - 1))
            ->map(fn (int $dayIndex) => now()->subDays(($this->days - 1) - $dayIndex)->format('Y-m-d'))
            ->toArray();

        /** @var Collection<int, object{status: string, date: ?string, count: string}> $messageReceipts */
        $messageReceipts = MessageReceipt::query()
            ->selectRaw('status, DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', $statuses)
            ->groupBy(DB::raw('DATE(created_at)'), 'status')
            ->get();

        // Structure data as [status][date] = count
        /** @var array<string, array<string, int>> $dataMatrix */
        $dataMatrix = [];

        foreach ($statuses as $status) {
            foreach ($dates as $date) {
                $dataMatrix[$status][$date] = 0;
            }
        }

        foreach ($messageReceipts as $messageReceipt) {
            $dataMatrix[$messageReceipt->status][$messageReceipt->date] = (int) $messageReceipt->count;
        }

        $datasets = [];

        foreach ($statuses as $status) {
            $datasets[] = [
                'label' => __($status),
                'data' => array_values($dataMatrix[$status]),
                'backgroundColor' => $colors[$status],
                'borderColor' => '#fff',
            ];
        }

        return [
            'labels' => array_map(fn (string $d): string => Carbon::parse($d)->translatedFormat('d M'), $dates),
            'datasets' => $datasets,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): RawJs
    {
        return RawJs::make(<<<'JS'
        (() => {
            let delayed;

            return {
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const label = context.dataset.label;
                                return `${value} ${label}`;
                            }
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false
                },
                scales: {
                    x: {
                        stacked: true
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }

                    }
                },
                animation: {
                    onComplete: () => {
                        delayed = true;
                    },
                    delay: (context) => {
                        let delay = 0;
                        if (context.type === 'data' && context.mode === 'default' && !delayed) {
                            delay = context.dataIndex * 300 + context.datasetIndex * 100;
                        }
                        return delay;
                    }
                }
            };
        })()
    JS);
    }
}
