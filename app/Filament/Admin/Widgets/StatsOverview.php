<?php

declare(strict_types=1);

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\MessageReceipt;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class StatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        // Get total number of companies (customers)
        $totalCustomers = Company::count();

        // Get message statistics for the last 30 days
        $thirtyDaysAgo = Carbon::now()->subDays(30)->startOfDay();

        // Get total messages
        $totalMessages = MessageReceipt::where('created_at', '>=', $thirtyDaysAgo)->count();

        // Get delivered messages
        $deliveredMessages = MessageReceipt::where('created_at', '>=', $thirtyDaysAgo)
            ->where('status', 'delivered')
            ->count();

        // Get pending messages
        $pendingMessages = MessageReceipt::where('status', 'pending')->count();

        // Get failed messages
        $failedMessages = MessageReceipt::where('created_at', '>=', $thirtyDaysAgo)
            ->where('status', 'failed')
            ->count();

        // Calculate success rate
        $successRate = $totalMessages > 0 ? round(($deliveredMessages / $totalMessages) * 100, 1) : 0;

        // Get message trend for the last 7 days
        /** @var array<int, float> $messageTrend */
        $messageTrend = MessageReceipt::select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', Carbon::now()->subDays(7)->startOfDay())
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->pluck('count')
            ->map(fn (mixed $value): float => is_numeric($value) ? (float) $value : 0.0)
            ->toArray();

        return [
            Stat::make(__('Total Customers'), number_format($totalCustomers))
                ->description(__('Active companies'))
                ->descriptionIcon('heroicon-m-building-office')
                ->color('primary'),

            Stat::make(__('Success Rate'), $successRate.'%')
                ->description(__('Delivered messages'))
                ->descriptionIcon('heroicon-m-check-circle')
                ->chart($messageTrend)
                ->color($successRate >= 90 ? 'success' : ($successRate >= 70 ? 'warning' : 'danger')),

            Stat::make(__('Pending Messages'), number_format($pendingMessages))
                ->description(__('Awaiting delivery'))
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make(__('Failed Messages'), number_format($failedMessages))
                ->description(__('In the last 30 days'))
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),
        ];
    }
}
