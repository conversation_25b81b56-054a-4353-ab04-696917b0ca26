<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Enums\MessageTemplateParameterType;
use App\Enums\MessageTemplateStatus;
use App\Enums\MessageTemplateType;
use App\Filament\Company\Resources\MessageTemplateResource\Pages;
use App\Models\Company;
use App\Models\MessageTemplate;
use Filament\Facades\Filament;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class MessageTemplateResource extends Resource
{
    protected static ?string $model = MessageTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function form(Form $form): Form
    {
        /** @var Company $company */
        $company = Filament::getTenant();

        return $form
            ->schema([
                Section::make('')->schema([
                    TextInput::make('display_name')
                        ->label('Display Name')
                        ->translateLabel()
                        ->live(onBlur: true)
                        ->afterStateUpdated(function (
                            string $operation,
                            string $state,
                            Set $set,
                        ): void {
                            $set('short_name', Str::slug($state));
                        })
                        ->required(),
                    TextInput::make('short_name')
                        ->label('Short Name')
                        ->translateLabel()
                        ->helperText(__('This name will be used to identify the template in the Api.'))
                        ->required(),
                    Select::make('project_id')
                        ->label('Project')
                        ->translateLabel()
                        ->options(
                            fn (): array => $company->projects
                                ->where('status', 'active')
                                ->pluck('name', 'id')
                                ->all(),
                        )
                        ->required(),
                    Textarea::make('content')
                        ->label('Message')
                        ->translateLabel()
                        ->live(onBlur: true)
                        ->helperText(__('You can use parameters in the message by enclosing them in double curly braces, e.g. {{name}}.'))
                        ->afterStateUpdated(function (string $state, Set $set): void {

                            $set('parameters', []);

                            preg_match_all('/{{(.*?)}}/', $state, $matches);
                            $parameters = [];
                            foreach ($matches[1] as $match) {
                                $parameters[] = [
                                    'name' => $match,
                                    'type' => MessageTemplateParameterType::string->value,
                                    'max_limit' => 10,
                                ];
                            }
                            $set('parameters', $parameters);
                        })
                        ->required(),
                    Select::make('type')
                        ->label('Type')
                        ->translateLabel()
                        ->options([
                            MessageTemplateType::marketing->value => __(MessageTemplateType::marketing->value),
                            MessageTemplateType::transactional->value => __(MessageTemplateType::transactional->value),
                        ])
                        ->required(),
                    Repeater::make('parameters')
                        ->label('Parameters')
                        ->translateLabel()
                        ->relationship('parameters')
                        ->reorderable(false)
                        ->collapsible()
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('name')
                                        ->label('Name')
                                        ->translateLabel()
                                        ->live(onBlur: true)
                                        ->afterStateUpdated(function (
                                            string $state,
                                            Set $set,
                                        ): void {
                                            $set('name', Str::slug($state));
                                        })
                                        ->rules(function (Get $get): array {
                                            /** @var array<int, array{name: string|null}> $items */
                                            $items = $get('../../parameters');

                                            $slugs = collect($items)->pluck('name')->map(fn (string $name) => Str::slug($name));

                                            if ($slugs->duplicates()->isNotEmpty()) {
                                                return [
                                                    /**
                                                     * @param  callable(string): void  $fail
                                                     */
                                                    function (string $attribute, mixed $value, callable $fail): void {
                                                        $fail(__('Each parameter name must be unique.'));
                                                    },
                                                ];
                                            }

                                            return [];
                                        })
                                        ->required(),
                                    TextInput::make('max_limit')
                                        ->label('Max Limit')
                                        ->translateLabel()
                                        ->helperText(__('The maximum number of characters allowed for this parameter is 100.'))
                                        ->default(1)
                                        ->numeric()
                                        ->minValue(1)
                                        ->maxValue(100)
                                        ->required(),
                                    Select::make('type')
                                        ->label('Type')
                                        ->translateLabel()
                                        ->options([
                                            MessageTemplateParameterType::string->value => __(MessageTemplateParameterType::string->value),
                                            MessageTemplateParameterType::number->value => __(MessageTemplateParameterType::number->value),
                                            MessageTemplateParameterType::date->value => __(MessageTemplateParameterType::date->value),
                                        ])
                                        ->required(),
                                ])
                                ->columns(3),
                        ]),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('project.name')
                    ->label('Project')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('display_name')
                    ->label('Display Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('short_name')
                    ->label('Short Name')
                    ->translateLabel()
                    ->copyable()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            MessageTemplateStatus::active->value => 'success',
                            MessageTemplateStatus::inactive->value, MessageTemplateStatus::rejected->value => 'danger',
                            MessageTemplateStatus::pending->value => 'warning',
                            default => 'primary',
                        },
                    ),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessageTemplates::route('/'),
            'create' => Pages\CreateMessageTemplate::route('/create'),
            //            'edit' => Pages\EditMessageTemplate::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Message Templates');
    }

    public static function getLabel(): string
    {
        return __('Message Template');
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }
}
