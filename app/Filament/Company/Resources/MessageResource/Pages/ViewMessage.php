<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageResource\Pages;

use App\Filament\Company\Resources\MessageResource;
use App\Models\Message;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;

final class ViewMessage extends Page implements HasTable
{
    use InteractsWithTable;

    public Message $record;

    protected static string $resource = MessageResource::class;

    protected static string $view = 'filament.company.resources.message-resource.pages.view-message';

    public function messageInfolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->record)
            ->schema([

                Section::make(__('Message Info'))->schema([
                    Split::make([
                        TextEntry::make('sender.sender')
                            ->label('Sender')
                            ->translateLabel(),
                        TextEntry::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->badge()
                            ->formatStateUsing(fn (string $state): string => __($state)),
                    ]),
                    TextEntry::make('short_message')
                        ->label('Message')
                        ->translateLabel(),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table->query($this->record->messages()->getQuery())->columns([
            TextColumn::make('contact.name')
                ->label('Name')
                ->translateLabel()
                ->searchable()
                ->sortable(),
            TextColumn::make('number')
                ->label('Phone')
                ->translateLabel()
                ->searchable(),
            TextColumn::make('sent_at')
                ->label('Sent at')
                ->translateLabel()
                ->sortable(),
            TextColumn::make('delivered_at')
                ->label('Delivered at')
                ->translateLabel()
                ->sortable(),
            TextColumn::make('status')
                ->label('Status')
                ->icon(
                    fn (string $state): string => match ($state) {
                        'pending', 'enroute', 'scheduled' => 'heroicon-o-clock',
                        'sent', 'delivered' => 'heroicon-o-check-circle',
                        'failed',
                        'undeliverable',
                        'deleted',
                        'accepted',
                        'expired' => 'heroicon-o-x-circle',
                        default => '',
                    },
                )
                ->iconColor(
                    fn (string $state): string => match ($state) {
                        'pending', 'enroute', 'scheduled' => 'warning',
                        'sent' => 'info',
                        'delivered' => 'success',
                        'failed',
                        'undeliverable',
                        'deleted',
                        'expired' => 'danger',
                        default => 'gray',
                    },
                )
                ->formatStateUsing(fn (string $state): string => __($state))
                ->translateLabel(),
        ]);
    }

    public function getHeading(): string
    {
        return __('View Message');
    }

    public function getTitle(): string
    {
        return __('View Message');
    }
}
