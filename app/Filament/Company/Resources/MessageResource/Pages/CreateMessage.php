<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageResource\Pages;

use App\Enums\FeatureConsumptionType;
use App\Filament\Company\Resources\MessageResource;
use App\Jobs\MessageJobCreator;
use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\Message;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Transaction;
use App\Services\SmsService;
use Exception;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Exceptions\Halt;
use Filament\Support\Facades\FilamentView;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

use function Filament\Support\is_app_url;

final class CreateMessage extends CreateRecord
{
    protected static string $resource = MessageResource::class;

    public function getSMSParts(string $message): int
    {
        $result = SmsService::calculateSmsParts($message); // Adjust as per your SmsService implementation

        return $result['parts'];
    }

    /**
     * Check balance based on balance type.
     *
     * @param  Collection<int, array{id: string, number: string}>  $receivers
     *
     * @throws Halt
     */
    public function checkBalance(int $parts, Collection $receivers, Company $company, string $type, ?Project $project = null): void
    {
        if ($type === 'balance') {
            try {
                $balance = $company->balance();

                if ($balance < $parts * $receivers->count()) {
                    throw new Exception(__('Insufficient balance'));
                }
            } catch (Exception $e) {

                Notification::make()
                    ->title(__('Error'))
                    ->body($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();

            }
        } elseif ($type === 'subscription') {
            try {
                if (! $project instanceof Project) {
                    throw new Exception(__('Project not found'));
                }

                $balance = $project->balance('SMS');

                if ($balance < $parts * $receivers->count()) {
                    throw new Exception('Insufficient balance');
                }
            } catch (Exception $e) {

                Notification::make()
                    ->title(__('Error'))
                    ->body($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();
            }
        }
    }

    /**
     * Check Project is active.
     *
     * @throws Halt
     */
    public function checkProject(?Project $project = null): void
    {
        if (! $project instanceof Project || $project->status !== 'active') {
            try {
                if (! $project instanceof Project) {
                    throw new Exception(__('Project not found'));
                }

                throw new Exception(__('Project is not active'));
            } catch (Exception $e) {

                Notification::make()
                    ->title(__('Error'))
                    ->body($e->getMessage())
                    ->danger()
                    ->send();

                $this->halt();
            }
        }
    }

    /**
     * @return array{'id':int, 'number':int}
     */
    public function getNumberFromContact(string $contact_group_id): array
    {
        $contactGroup = ContactGroup::findOrFail($contact_group_id);

        /** @var array{'id':int, 'number':int} $numbers */
        $numbers = $contactGroup->contacts()->select(
            'id',
            'phone AS number',
        )->get()->toArray();

        return $numbers;
    }

    /**
     * @throws Halt|Throwable
     */
    public function create(bool $another = false): void
    {
        $this->authorizeAccess();

        try {
            $this->beginDatabaseTransaction();

            $this->callHook('beforeValidate');

            $data = $this->form->getState();

            $this->callHook('afterValidate');

            // @phpstan-ignore-next-line
            $data = $this->mutateFormDataBeforeCreate($data);

            $this->callHook('beforeCreate');

            // @phpstan-ignore-next-line
            $this->record = $this->handleRecordCreation($data);

            // $this->form->model($this->getRecord())->saveRelationships();

            $this->callHook('afterCreate');

            $this->commitDatabaseTransaction();
        } catch (Halt $exception) {
            // @codeCoverageIgnoreStart
            $exception->shouldRollbackDatabaseTransaction() ?
                $this->rollBackDatabaseTransaction() :
                $this->commitDatabaseTransaction();
            // @codeCoverageIgnoreEnd

            return;
        } catch (Throwable $exception) {
            $this->rollBackDatabaseTransaction();

            throw $exception;
        }

        $this->rememberData();

        $this->getCreatedNotification()?->send();

        // @codeCoverageIgnoreStart
        if ($another) {
            // Ensure that the form record is anonymized so that relationships aren't loaded.
            assert($this->getRecord() instanceof Model);
            $this->form->model($this->getRecord()::class);
            $this->record = null;

            $this->fillForm();

            return;
        }
        // @codeCoverageIgnoreEnd

        $redirectUrl = $this->getRedirectUrl();

        $this->redirect($redirectUrl, navigate: FilamentView::hasSpaMode() && is_app_url($redirectUrl));
    }

    /**
     * @param array{
     *     sender_id: string,
     *     short_message: string,
     *     balance_type: string,
     *     send_type: string,
     *     select_all?: bool,
     *     messages: array{
     *       id: string,
     *       number: string
     *     }[],
     *     contact_group_id: ?string,
     *     destination_addr: array{
     *       number: string
     *     }[]|array{
     *       contact_id: string,
     *       number: string
     *     }[],
     *   } $data
     * @return array<mixed>
     */
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate message consumption (e.g., number of SMS parts)
        $data['message_consumption'] = $this->getSMSParts(
            $data['short_message'],
        );

        // get destination number
        if ($data['select_all'] ?? false) {
            assert($data['contact_group_id'] !== null);
            $data['messages'] = $this->getNumberFromContact($data['contact_group_id']);

        } else {

            // Map destination addresses to message format
            $data['messages'] = collect($data['destination_addr'])
                ->map(fn (array $destination): array => [
                    'number' => $destination['number'], 'id' => $destination['contact_id'] ?? '',
                ])
                ->toArray();

            unset($data['destination_addr']); // Remove unneeded key
        }

        return $data;
    }

    /**
     * @param array{
     *     sender_id: string,
     *     short_message: string,
     *     select_all: bool,
     *     balance_type: string,
     *     messages: array{
     *       id: string,
     *       number: string
     *     }[],
     *     contact_group_id: ?string,
     *     destination_addr: array{
     *       number: string
     *     }[]|array{
     *       contact_id: string,
     *       number: string
     *     }[],
     *   } $data
     *
     * @throws Halt
     * @throws Throwable
     */
    protected function handleRecordCreation(array $data): Model
    {
        /** @var Company $company */
        $company = Filament::getTenant();
        $project = null;

        // Validate and process input data
        $parts = $this->getSMSParts($data['short_message']);

        if ($company->verified_at) {
            $receivers = collect($this->removeDuplicateRecipients($data['messages']));
        } else {
            $receivers = collect([
                [
                    'id' => '',
                    'number' => $company->phone,
                ],
            ]);
        }

        // Handle subscription balance type
        if ($data['balance_type'] === 'subscription') {
            $project = $this->validateAndFindProject($data);
        }

        // Check balance before proceeding
        $this->checkBalance($parts, $receivers, $company, $data['balance_type'], $project);

        // Check project is active
        if ($data['balance_type'] === 'subscription') {
            $this->checkProject($project);
        }

        // Perform database operations in a transaction
        return DB::transaction(function () use ($data, $company, $parts, $receivers, $project): Message {
            // Create the main Message entry
            $message = $this->createMessage($data, $project, $company, $parts, $receivers);

            // Handle balance consumption
            $this->handleBalanceConsumption($data, $project, $parts, $receivers, $message);

            // Get the JasminClient from the container and dispatch the job
            if ($company->auto_approve) {
                $message->update(['status' => 'approved']);

                $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
                MessageJobCreator::dispatch($message, $jasminClient);
            }

            return $message;
        });
    }

    /**
     * Validate and find the project for subscription balance type.
     *
     * @param  array<mixed>  $data
     *
     * @throws Halt
     */
    private function validateAndFindProject(array $data): Project
    {
        if (empty($data['project_id'])) {

            Notification::make()
                ->title(__('Error'))
                ->body('Project ID is required for subscription balance type.')
                ->danger()
                ->send();

            $this->halt();
        }

        $project = Project::find($data['project_id']);

        if (! $project instanceof Project) {
            Notification::make()
                ->title(__('Error'))
                ->body('Project not found.')
                ->danger()
                ->send();

            $this->halt();
        }

        assert($project instanceof Project);

        return $project;
    }

    /**
     * @param array{
     *     sender_id: string,
     *     short_message: string,
     *     select_all: bool,
     *     messages: array{
     *       id: string,
     *       number: string
     *     }[],
     *     message_type?: string,
     *     send_type?: string,
     *     contact_group_id: ?string,
     *     destination_addr: array{
     *       number: string
     *     }[]|array{
     *       contact_id: string,
     *       number: string
     *     }[],
     *     } $data
     * @param  Collection<int, array{id: string, number: string}>  $receivers
     *
     * Create the main Message entry.
     */
    private function createMessage(array $data, ?Project $project, Company $company, int $parts, Collection $receivers): Message
    {
        $message = Message::create([
            'short_message' => $data['short_message'],
            'sender_id' => $data['sender_id'],
            'company_id' => $company->id,
            'message_type' => $data['message_type'] ?? 'sms',
            'send_type' => $data['send_type'] ?? 'multiple',
            'contact_group_id' => null,
            'message_consumption' => $parts * $receivers->count(),
            'project_id' => $project->id ?? null,
            'transaction_id' => null,
        ]);

        $message->messages()->createMany($receivers->map(function (array $receiver): array {
            $r = [
                'number' => $receiver['number'],
                'status' => 'pending',
            ];
            if ($receiver['id'] !== '' && $receiver['id'] !== '0') {
                $r['contact_id'] = $receiver['id'];
            }

            return $r;
        }));

        return $message;
    }

    /**
     * @param array{
     *   sender_id: string,
     *   short_message: string,
     *   select_all: bool,
     *   balance_type: string,
     *   messages: array{
     *     id: string,
     *     number: string
     *   }[],
     *   contact_group_id: ?string,
     *   destination_addr: array{
     *     number: string
     *   }[]|array{
     *     contact_id: string,
     *     number: string
     *   }[],
     * } $data
     * @param  Collection<int, array{id: string, number: string}>  $receivers
     *
     * Handle balance consumption based on balance type.
     *
     * @throws Exception
     */
    private function handleBalanceConsumption(array $data, ?Project $project, int $parts, Collection $receivers, Message $message): void
    {
        if ($data['balance_type'] === 'subscription') {
            assert($project instanceof Project);
            $project->consume('SMS', $parts * $receivers->count(), FeatureConsumptionType::SMS->value);
        } elseif ($data['balance_type'] === 'balance') {
            $total = $this->messageTotalCost($parts, $receivers->count()) / 1000;

            $transaction = Transaction::create([
                'amount' => $total * -1,
                'action_type' => 'charge',
                'status' => 'completed',
                'company_id' => $message->company_id,
            ]);

            $message->update([
                'transaction_id' => $transaction->id,
            ]);
        }
    }

    /**
     * @param  array<array{'id':string, 'number':string}>  $receivers
     * @return array<array{'id':string, 'number':string}>
     */
    private function removeDuplicateRecipients(array $receivers): array
    {
        $result = [];
        foreach ($receivers as $receiver) {
            if (! in_array($receiver, $result)) {
                $result[] = $receiver;
            }
        }

        return $result;
    }

    /**
     * @throws Exception
     */
    private function messageTotalCost(int $smsParts, int $contactsCount): int
    {
        /** @var string|null $cost */
        $cost = Setting::where('key', 'single_sms_cost')->first()?->value;
        if (! ctype_digit((string) $cost)) {
            logger()->error('Single SMS cost not found');
            throw new Exception('Single SMS cost not found');
        }

        return (int) ($contactsCount * $smsParts * $cost);
    }
}
