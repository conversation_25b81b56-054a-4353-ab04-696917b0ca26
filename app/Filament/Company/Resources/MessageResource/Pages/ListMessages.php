<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageResource\Pages;

use App\Filament\Company\Resources\MessageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListMessages extends ListRecords
{
    protected static string $resource = MessageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
