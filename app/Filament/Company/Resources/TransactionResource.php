<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\TransactionResource\Pages;
use App\Models\Transaction;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

final class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-currency-dollar';

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['subscription.plan']))
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No transactions found'))
            ->columns([
                TextColumn::make('amount')
                    ->label('Amount')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (float $state): string => number_format($state, 2).
                            ' '.
                            'د.ل',
                    )
                    ->sortable(),
                TextColumn::make('action_type')
                    ->label('Action Type')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'deposit' => 'success',
                            'withdraw' => 'danger',
                            'charge' => 'warning',
                            default => 'primary',
                        },
                    ),
                TextColumn::make('subscription.plan.name')
                    ->label('Subscription')
                    ->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'pending' => 'warning',
                            'completed' => 'success',
                            'rejected' => 'danger',
                            default => 'info',
                        },
                    ),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->sortable()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ])
            ->filters([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'create' => Pages\CreateTransaction::route('/create'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Transactions');
    }

    public static function getNavigationGroup(): string
    {
        return __('Balance and Subscriptions');
    }

    public static function getLabel(): string
    {
        return __('Transaction');
    }
}
