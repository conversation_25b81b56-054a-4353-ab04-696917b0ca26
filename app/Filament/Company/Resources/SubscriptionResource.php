<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\SubscriptionResource\Pages;
use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-fire';

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No subscription found'))
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('project.name')
                    ->label('Project Name')
                    ->translateLabel(),
                TextColumn::make('plan.name')
                    ->label('Plan Name')
                    ->translateLabel(),
                TextColumn::make('started_at')
                    ->label('Started At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('expired_at')
                    ->label('Expired At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('id')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->color(function (Subscription $record): string {
                        if ($record->canceled_at) {
                            return 'danger';
                        }

                        if ($record->expired_at >= now()) {
                            return 'success';
                        }

                        return 'danger';
                    })
                    ->formatStateUsing(function (Subscription $record) {
                        if ($record->canceled_at) {
                            return __('Canceled');
                        }

                        if ($record->expired_at >= now()) {
                            return __('Active');
                        }

                        return __('Expired');
                    }),
                TextColumn::make('canceled_at')
                    ->label('Canceled At')
                    ->translateLabel()
                    ->date(),
                TextColumn::make('project_id')
                    ->label('Messages')
                    ->translateLabel()
                    ->formatStateUsing(function (Subscription $record): string {
                        /** @var Plan $plan */
                        $plan = $record->plan;
                        $project = Project::find($record->project_id);
                        /** @var Feature $feature */
                        $feature = $plan->feature()->first();

                        $balance = FeatureConsumption::where(
                            'feature_id',
                            $feature->id,
                        )
                            ->where('subscription_id', $record->id)
                            ->sum('consumption');

                        $total = $feature
                            ->featurePlans()
                            ->where('plan_id', $record->plan_id)
                            ->sum('charges');

                        return number_format((int) $total).
                            ' '.
                            '/ '.
                            number_format($total - $balance);

                    }),
            ])
            ->filters([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Subscriptions');
    }

    public static function getNavigationGroup(): string
    {
        return __('Balance and Subscriptions');
    }

    public static function getLabel(): string
    {
        return __('Subscription');
    }
}
