<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\ProjectResource\Pages;
use App\Models\Project;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class ProjectResource extends Resource
{
    protected static ?string $model = Project::class;

    protected static ?string $navigationIcon = 'heroicon-o-lifebuoy';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required(),
                        Select::make('type')
                            ->label('Budget type')
                            ->translateLabel()
                            ->live()
                            ->options([
                                'subscription' => __('subscription'),
                                'budget' => __('balance'),
                                'mixed' => __('mixed'),
                            ])
                            ->default('mixed')
                            ->required(),
                        TextInput::make('limit')
                            ->label('Limit')
                            ->translateLabel()
                            ->helperText(
                                __(
                                    'Budget limit for the project, specified in Libyan Dinar (LYD).',
                                ),
                            )
                            ->visible(
                                fn (Get $get): bool => $get('type') ===
                                    'budget' || $get('type') === 'mixed',
                            )
                            ->required(),
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->required()
                            ->options([
                                'active' => __('active'),
                                'inactive' => __('inactive'),
                            ])
                            ->default('active'),
                        RichEditor::make('description')
                            ->label('Notes')
                            ->translateLabel(),
                    ]),
                ])->columnSpan(
                    fn (?Project $record): int => $record instanceof Project
                        ? 2
                        : 3,
                ),
                Group::make([
                    Section::make('')
                        ->schema([
                            Forms\Components\Textarea::make('token')
                                ->label('Token')
                                ->translateLabel()
                                ->live()
                                ->extraAttributes([
                                    'dir' => 'ltr',
                                    'class' => 'font-bold',
                                ])
                                ->helperText(
                                    __(
                                        'This token will be used to access the project. if you lose it, you will not be able to access the project anymore.',
                                    ),
                                )
                                ->disabled(),
                        ])
                        ->visible(
                            fn (?Project $record): bool => $record instanceof Project,
                        ),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?Project $record) => $record instanceof Project && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Project $record,
                                    ): string => $record instanceof Project &&
                                    $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Project $record): bool => $record instanceof Project,
                        ),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?Project $record): bool => $record instanceof Project,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No projects found'))
            ->columns([
                TextColumn::make('name')->label('Project')->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->icon(
                        fn (string $state): string => match ($state) {
                            'active' => 'heroicon-o-check-circle',
                            'inactive' => 'heroicon-o-x-circle',
                            default => 'heroicon-o-check-circle',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            default => 'success',
                        },
                    ),
                TextColumn::make('type')
                    ->label('Budget type')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state): string => __($state)),
                TextColumn::make('limit')
                    ->label('Budget')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (Project $record): string => $record // TODO implement usage for projects
                            ->transactions()
                            ->sum('amount') /
                            1000 +
                            $record->limit.
                            ' '.
                            __('LYD').
                            ' / '.
                            $record->limit.
                            ' '.
                            __('LYD'),
                    ),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditProject::class,
            Pages\ManageContact::class,
            Pages\ProjectSubscription::class,
            Pages\ProjectConsumption::class,
            Pages\ListIpwhitelist::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProjects::route('/'),
            'create' => Pages\CreateProject::route('/create'),
            'edit' => Pages\EditProject::route('/{record}/edit'),
            'contacts' => Pages\ManageContact::route('/{record}/contacts'),
            'subscriptions' => Pages\ProjectSubscription::route(
                '/{record}/subscriptions',
            ),
            'consumptions' => Pages\ProjectConsumption::route(
                '/{record}/consumptions',
            ),
            'ip-whitelist' => Pages\ListIpwhitelist::route(
                '/{record}/ip-whitelist',
            ),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Projects');
    }

    public static function getLabel(): string
    {
        return __('Project');
    }
}
