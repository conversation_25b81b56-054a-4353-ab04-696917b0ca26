<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\SenderResource\Pages;

use App\Filament\Company\Resources\SenderResource;
use App\Models\Company;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;

final class CreateSender extends CreateRecord
{
    protected static string $resource = SenderResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $company = Filament::getTenant();
        assert($company instanceof Company);

        $data['type'] = 'private';
        $data['status'] = 'pending';
        $data['company_id'] = $company->id;

        return $data;
    }

    //    protected function afterCreate(): void
    //    {
    //        $sender = $this->record;
    //
    //        $sender->companies()->attach(Filament::getTenant()->id);
    //    }
}
