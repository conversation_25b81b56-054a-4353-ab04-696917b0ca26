<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\SenderResource\Pages;

use App\Filament\Company\Resources\SenderResource;
use Closure;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListSenders extends ListRecords
{
    protected static string $resource = SenderResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\CreateAction::make()];
    }

    protected function getTableRecordUrlUsing(): ?Closure
    {
        return null;
    }
}
