<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\SubscriptionResource\Pages;

use App\Enums\TransactionStatus;
use App\Filament\Company\Resources\SubscriptionResource;
use App\Models\Company;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Transaction;
use Carbon\CarbonImmutable;
use DB;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Throwable;

final class ListSubscriptions extends ListRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('subscribe_to')
                ->label('Subscribe')
                ->translateLabel()
                ->form($this->newSubscriptionForm())
                ->action(function (array $data): void {
                    $this->subscribeTo($data);
                }),
        ];
    }

    /**
     * Check if the project already has an active subscription with overlapping features.
     */
    private function hasDuplicateFeatures(Project $project, Plan $plan): bool
    {
        $plan->load('feature');

        $activeSubscriptions = $project
            ->subscription()
            ->where('expired_at', '>=', now())
            ->where('canceled_at', null)
            ->with('plan.feature')
            ->get();

        $selectedFeatureNames = $plan->feature->pluck('name');

        foreach ($activeSubscriptions as $subscription) {
            assert($subscription->plan instanceof Plan);
            $activeFeatureNames = $subscription->plan->feature->pluck('name');

            if (
                $selectedFeatureNames
                    ->intersect($activeFeatureNames)
                    ->isNotEmpty()
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Process the subscription and create a transaction.
     *
     * @throws Throwable
     */
    private function processSubscription(
        Company $company,
        Project $project,
        Plan $plan,
    ): void {
        DB::transaction(function () use ($company, $project, $plan): void {
            $subscription = $project->subscribeTo(
                $plan,
                CarbonImmutable::now(),
                $plan->periodicity_quantity,
            );

            Transaction::create([
                'amount' => $plan->price * -1,
                'action_type' => 'charge',
                'status' => TransactionStatus::Completed,
                'description' => 'Subscription to '.$plan->name,
                'company_id' => $company->id,
                'subscription_id' => $subscription->id,
            ]);

            Notification::make()
                ->title(__('Subscription Successful'))
                ->body(__('You have successfully subscribed to ').$plan->name)
                ->success()
                ->send();
        });
    }

    /** @return array<Component> */
    private function newSubscriptionForm(): array
    {
        /** @var Company $company */
        $company = Filament::getTenant();

        return [
            Select::make('plan_id')
                ->label('Plan')
                ->searchable()
                ->options(fn (): array => Plan::where('status', 'active')
                    ->get()
                    ->mapWithKeys(
                        fn (Plan $plan) => [
                            $plan->id => sprintf(
                                '%s - %s %s (%s)',
                                __($plan->periodicity_type),
                                number_format($plan->price, 2),
                                __('LYD'),
                                $plan->name,
                            ),
                        ],
                    )
                    ->toArray(),
                )
                ->required(),
            Select::make('project_id')
                ->label('Project')
                ->options(
                    Project::where('status', 'active')
                        ->where('company_id', $company->id)
                        ->pluck('name', 'id'),
                )
                ->required(),
        ];
    }

    /**
     * @param  array<mixed>  $data
     *
     * @throws Throwable
     */
    private function subscribeTo(array $data): void
    {
        /** @var Company $company */
        $company = Filament::getTenant();

        $project = Project::find($data['project_id']);
        assert($project instanceof Project);
        $plan = Plan::find($data['plan_id']);
        assert($plan instanceof Plan);

        // Check for duplicate features in active subscriptions
        if ($this->hasDuplicateFeatures($project, $plan)) {
            Notification::make()
                ->title(__('Duplicate Plan Features'))
                ->body(
                    __(
                        'You are already subscribed to a plan with similar features.',
                    ),
                )
                ->danger()
                ->send();

            return;
        }

        // Check if the company has sufficient balance
        if ($company->balance() < $plan->price) {
            Notification::make()
                ->title(__('Insufficient Balance'))
                ->body(
                    __(
                        'You do not have enough balance to subscribe to this plan.',
                    ),
                )
                ->danger()
                ->send();

            return;
        }

        $this->processSubscription($company, $project, $plan);
    }
}
