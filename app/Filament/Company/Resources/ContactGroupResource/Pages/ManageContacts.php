<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ContactGroupResource\Pages;

use App\Filament\Company\Resources\ContactGroupResource;
use App\Imports\ContactImport;
use App\Models\Contact;
use App\Models\ContactGroup;
use Exception;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

final class ManageContacts extends ManageRelatedRecords
{
    protected static string $resource = ContactGroupResource::class;

    protected static string $relationship = 'contacts';

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationLabel(): string
    {
        return __('Contacts');
    }

    public function getTitle(): string
    {
        return __('Contacts');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required(),
                        TextInput::make('phone')
                            ->label('Phone')
                            ->translateLabel()
                            ->required(),
                        Select::make('sex')
                            ->label('Sex')
                            ->translateLabel()
                            ->options([
                                'male' => __('male'),
                                'female' => __('female'),
                            ]),
                        TextInput::make('city')
                            ->label('City')
                            ->translateLabel(),
                    ]),
                ])->columnSpan(
                    fn (?Contact $record): int => $record instanceof Contact
                        ? 2
                        : 3,
                ),
                Group::make([
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?Contact $record) => $record instanceof Contact && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Contact $record,
                                    ): string => $record instanceof Contact && $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Contact $record): bool => $record instanceof Contact,
                        ),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?Contact $record): bool => $record instanceof Contact,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->emptyStateHeading(__('No contacts found'))
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Contact')
                    ->translateLabel(),
                Tables\Actions\Action::make('import')
                    ->label('Import file')
                    ->translateLabel()
                    ->form([
                        FileUpload::make('attachment')
                            ->label('Attachment')
                            ->translateLabel()
                            ->required(),
                    ])
                    ->action(function (array $data): void {
                        $this->importFile($data);
                    }),
            ])
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('births')
                    ->label('Births')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('sex')
                    ->label('Sex')
                    ->translateLabel()
                    ->badge()
                    ->color(
                        fn (string $state): string => match ($state) {
                            'male' => 'info',
                            'female' => 'warning',
                            default => 'info',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state)),
                TextColumn::make('city')
                    ->label('City')
                    ->translateLabel()
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * @param  array<mixed>  $data
     */
    public function importFile(array $data): void
    {
        $fileName = $data['attachment'];
        assert(is_string($fileName));

        try {
            // Use the 'local' disk and refer to the file by its filename only
            if (Storage::disk('local')->exists($fileName)) {
                // Get the full path to the file
                $filePath = Storage::disk('local')->path($fileName);

                // Import the file using its path
                assert($this->record instanceof ContactGroup);
                Excel::import(new ContactImport((string) $this->record->id), $filePath);

                // Delete file after import complete
                Storage::disk('local')->delete($fileName);

                Notification::make()
                    ->title(__('Import successful'))
                    ->body(__('The contacts have been imported successfully.'))
                    ->success()
                    ->send();
            } else {
                // Handle file not found
                Notification::make()
                    ->title(__('File not found'))
                    ->body(
                        __('The file you are trying to upload does not exist.'),
                    )
                    ->danger()
                    ->send();
            }
        } catch (Exception $e) {

            Log::error($e->getMessage());

            Notification::make()
                ->title(__('File upload failed'))
                ->body(__('The file you are trying to upload does not exist.'))
                ->danger()
                ->send();
        }
    }
}
