<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ContactGroupResource\Pages;

use App\Filament\Company\Resources\ContactGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListContactGroups extends ListRecords
{
    protected static string $resource = ContactGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
