<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ContactGroupResource\Pages;

use App\Filament\Company\Resources\ContactGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditContactGroup extends EditRecord
{
    protected static string $resource = ContactGroupResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Group Contacts');
    }

    protected function getHeaderActions(): array
    {
        return [Actions\DeleteAction::make()];
    }
}
