<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\MessageResource\Pages;
use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\Message;
use App\Models\Project;
use App\Models\Sender;
use App\Models\Transaction;
use App\Rules\PhoneNumberRule;
use App\Services\SmsService;
use Filament\Facades\Filament;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

final class MessageResource extends Resource
{
    protected static ?string $model = Message::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        $company = Filament::getTenant();
        assert($company instanceof Company);

        return $form
            ->schema([
                Group::make([
                    Section::make('')
                        ->schema([
                            Select::make('sender_id')
                                ->label('Sender')
                                ->translateLabel()
                                ->required()
                                ->options(
                                    Sender::whereRelation(
                                        'companies',
                                        'company_id',
                                        $company->id,
                                    )
                                        ->where('status', 'active')
                                        ->pluck('sender', 'id'),
                                )
                                ->columnSpan(2),
                            Textarea::make('short_message')
                                ->label('Message')
                                ->translateLabel()
                                ->required()
                                ->columnSpan(2),
                        ])
                        ->columns(2),

                    Section::make('')->schema([
                        Repeater::make('destination_addr')
                            ->label('Destinations')
                            ->translateLabel()
                            ->relationship('messages')
                            ->required()
                            ->live()
                            ->disabled(fn (Get $get): bool => $get('select_all') === true)
                            ->addable(
                                fn (Get $get): bool => $get('send_type') ===
                                    'multiple',
                            )
                            ->deletable(
                                fn (Get $get): bool => $get('send_type') ===
                                    'multiple',
                            )
                            ->schema(function (Get $get): array {
                                if (empty($get('contact_group_id'))) {
                                    return [
                                        TextInput::make('number')
                                            ->label('Phone Number')
                                            ->translateLabel()
                                            ->rule([new PhoneNumberRule()])
                                            ->required(
                                                fn (Get $get): bool => ! $get(
                                                    'select_all',
                                                ),
                                            ),
                                    ];
                                }

                                return [
                                    Group::make([
                                        Select::make('contact_id')
                                            ->label('Contact Name')
                                            ->translateLabel()
                                            ->live()
                                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                            ->afterStateUpdated(function (
                                                Get $get,
                                                Set $set,
                                            ): void {
                                                $contact = Contact::firstWhere(
                                                    ['id' => $get('contact_id')]
                                                );

                                                $set(
                                                    'number',
                                                    $contact?->phone,
                                                );
                                            })
                                            ->options(
                                                ContactGroup::firstWhere([
                                                    'id' => $get('contact_group_id'),
                                                ])
                                                    ?->contacts()
                                                    ->pluck('name', 'id') ?? [],
                                            ),
                                        TextInput::make('number')
                                            ->label('Phone Number')
                                            ->translateLabel()
                                            ->rule([new PhoneNumberRule()])
                                            ->live()
                                            ->required(
                                                fn (Get $get): bool => ! $get(
                                                    '../../select_all',
                                                ),
                                            ),
                                    ])->columns(2),
                                ];
                            }),
                    ]),
                ])->columnSpan(2),
                Group::make([
                    Section::make('')->schema([
                        Select::make('balance_type')
                            ->label('Balance Type')
                            ->translateLabel()
                            ->required()
                            ->live()
                            ->options([
                                'balance' => __('balance'),
                                'subscription' => __('subscription'),
                            ]),
                        Select::make('project_id')
                            ->label('Project')
                            ->translateLabel()
                            ->required()
                            ->options(fn () => Project::where(
                                'company_id',
                                $company->id,
                            )->where('status', '=', 'active')
                                ->pluck('name', 'id'))
                            ->visible(
                                fn (Get $get): bool => $get('balance_type') ===
                                    'subscription',
                            ),
                        Select::make('contact_group_id')
                            ->label('Contact Group')
                            ->translateLabel()
                            ->live()
                            // TODO: filter contact group by project global scope
                            ->options(ContactGroup::where(
                                'company_id',
                                $company->id,
                            )->pluck('name', 'id')),
                        Checkbox::make('select_all')
                            ->label('Select All Contacts in Group')
                            ->translateLabel()
                            ->live()
                            ->visible(
                                fn (Get $get): bool => $get(
                                    'contact_group_id',
                                ) !== null,
                            ),
                        Select::make('send_type')
                            ->label('Sending type')
                            ->translateLabel()
                            ->live()
                            ->disabled(fn (Get $get): bool => $get('select_all') === true)
                            ->afterStateUpdated(function (
                                Get $get,
                                Set $set,
                                string $state,
                            ): void {
                                if ($state === 'single') {
                                    // Clear the repeater and leave only one empty item
                                    $set('destination_addr', [
                                        [
                                            'contact_id' => null,
                                            'number' => null,
                                        ], // Add one empty item
                                    ]);
                                }
                            })
                            ->options([
                                'single' => __('Single contact'),
                                'multiple' => __('Multiple contacts'),
                            ])
                            ->default('multiple'),
                        Select::make('message_type')
                            ->label('Message Type')
                            ->translateLabel()
                            ->helperText(
                                __(
                                    'Tha way to display message in user interface',
                                ),
                            )
                            ->options([
                                'flash' => __('Flash'),
                                'sms' => __('SMS'),
                            ])
                            ->default('sms')
                            ->required(),
                    ]),
                ])->columnSpan(1),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No message found'))
            ->modifyQueryUsing(
                fn (Builder $query) => $query->with('transaction'),
            )
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('short_message')
                    ->label('Message')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(function (string $state): ?string {
                        $pattern = '/\b\d{4,6}\b/';

                        return preg_replace($pattern, '****', $state);
                    })
                    ->wrap(),
                TextColumn::make('message_type')
                    ->label('Message Type')
                    ->translateLabel()
                    ->sortable()
                    ->formatStateUsing(
                        fn (string $state) => mb_strtoupper($state),
                    ),
                TextColumn::make('sender.sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->searchable()
                    ->badge(),
                TextColumn::make('project.name')
                    ->label('Project')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('transaction_id')
                    ->label('Consumption')
                    ->translateLabel()
                    ->getStateUsing(function (Message $record) {
                        if ($record->transaction_id) {
                            return $record->transaction_id;
                        }
                        $parts = SmsService::calculateSmsParts(
                            $record->short_message,
                        );

                        return $record->messages()->count() * $parts['parts'];
                    })
                    ->formatStateUsing(function (
                        Message $record,
                        string $state,
                    ): string {
                        if ($state === $record->transaction_id) {
                            assert($record->transaction instanceof Transaction);

                            return abs($record->transaction->amount).
                                ' '.
                                __('LYD');
                        }

                        return $state.' '.__('Message');
                    }),
                TextColumn::make('created_at')
                    ->sortable()
                    ->label('Date')
                    ->translateLabel()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessages::route('/'),
            'create' => Pages\CreateMessage::route('/create'),
            'view' => Pages\ViewMessage::route('/{record}/view'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Messages');
    }

    public static function getLabel(): string
    {
        return __('Message');
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }
}
