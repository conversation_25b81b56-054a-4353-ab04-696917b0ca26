<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\UserResource\Pages;

use App\Filament\Company\Resources\UserResource;
use App\Mail\CompanyUserInvitation;
use App\Models\Company;
use App\Models\Invitation;
use DB;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Mail;

final class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('inviteUser')
                ->label('Invite User')
                ->translateLabel()
                ->form([
                    TextInput::make('email')
                        ->label(__('Email'))
                        ->translateLabel()
                        ->email()
                        ->required(),
                ])
                ->action(function (array $data): void {
                    $result = DB::Transaction(function () use ($data): bool {
                        /** @var Company $company */
                        $company = Filament::getTenant();

                        if (
                            $company->users()->where('email', $data['email'])->exists()
                        ) {
                            Notification::make('invitation-failed')
                                ->title(__('Invitation Failed'))
                                ->body(
                                    __(
                                        'The email is already a member of the company.',
                                    ),
                                )
                                ->danger()
                                ->send();

                            return false;
                        }

                        $invitation = Invitation::create([
                            'email' => $data['email'],
                            'company_id' => $company->id,
                            'expires_at' => now()->addDays(7),
                        ]);

                        Mail::to($invitation->email)->send(
                            new CompanyUserInvitation($invitation),
                        );

                        return true;
                    });

                    if (! $result) {
                        return;
                    }

                    Notification::make('invitedSuccess')
                        ->title(__('Invitation Sent'))
                        ->body(__('User invited successfully!'))
                        ->success()
                        ->send();
                }),
        ];
    }
}
