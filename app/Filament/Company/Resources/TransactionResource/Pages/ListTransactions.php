<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\TransactionResource\Pages;

use App\Filament\Company\Resources\TransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListTransactions extends ListRecords
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //            Actions\CreateAction::make(),
        ];
    }
}
