<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\CampaignMessageResource\Pages;
use App\Models\CampaignMessage;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

final class CampaignMessageResource extends Resource
{
    protected static ?string $model = CampaignMessage::class;

    protected static ?string $navigationIcon = 'heroicon-o-at-symbol';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        Forms\Components\Textarea::make('short_message')
                            ->label('Message')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),
                        Select::make('sex')
                            ->label('Sex')
                            ->translateLabel()
                            ->options([
                                'male' => __('male'),
                                'female' => __('female'),
                                'both' => __('both'),
                            ])
                            ->required()
                            ->default('both'),
                        Fieldset::make('Age_From_To')
                            ->schema([
                                TextInput::make('age_from')
                                    ->label('Age from')
                                    ->translateLabel()
                                    ->minValue(0)
                                    ->required()
                                    ->numeric(),
                                TextInput::make('age_to')
                                    ->label('Age to')
                                    ->minValue(0)
                                    ->translateLabel()
                                    ->required()
                                    ->numeric()
                                    ->gte('age_from'),
                            ])
                            ->label('Age')
                            ->translateLabel()
                            ->columns(2),
                        TextInput::make('quantity')
                            ->minValue(5000)
                            ->default(5000)
                            ->label('Quantity')
                            ->translateLabel()
                            ->required()
                            ->numeric(),
                        Select::make('city_id')
                            ->label('City')
                            ->translateLabel()
                            ->multiple()
                            ->required()
                            ->relationship('cities', 'name')
                            ->preload(),
                        Select::make('provider_id')
                            ->label('Provider')
                            ->translateLabel()
                            ->multiple()
                            ->required()
                            ->relationship('providers', 'name')
                            ->preload(),
                    ]),
                ])->columnSpan(
                    fn (?CampaignMessage $record): int => $record instanceof CampaignMessage
                        ? 2
                        : 3,
                ),
                Group::make([
                    Section::make('')
                        ->schema([
                            Placeholder::make('status')
                                ->label('Status')
                                ->translateLabel()
                                ->content(function (
                                    CampaignMessage $record,
                                ): HtmlString {
                                    $trans = __($record->status);

                                    // @codeCoverageIgnoreStart
                                    $colorClass = match ($record->status) {
                                        'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'rejected',
                                        'inactive' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                        default => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                    };
                                    // @codeCoverageIgnoreEnd

                                    return new HtmlString(
                                        "<span class='text-sm font-medium me-2 px-2.5 py-0.5 rounded $colorClass'>$trans</span>",
                                    );
                                }),
                        ])
                        ->visible(
                            fn (
                                ?CampaignMessage $record,
                            ): bool => $record instanceof CampaignMessage,
                        ),

                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?CampaignMessage $record,
                                    ) => $record instanceof CampaignMessage &&
                                    $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?CampaignMessage $record,
                                    ): string => $record instanceof CampaignMessage && $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (
                                ?CampaignMessage $record,
                            ): bool => $record instanceof CampaignMessage,
                        ),
                ]),
            ])
            ->columns(['lg' => 3]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No Campaign messages found'))
            ->columns([
                TextColumn::make('short_message')
                    ->label('Message')
                    ->wrap()
                    ->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->icon(
                        fn (string $state): string => match ($state) {
                            'active' => 'heroicon-o-check-circle',
                            'inactive', 'rejected' => 'heroicon-o-x-circle',
                            'pending' => 'heroicon-o-clock',
                            default => 'heroicon-o-check-circle',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'gray',
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            default => 'success',
                        },
                    ),
                TextColumn::make('age_from')
                    ->label('Age')
                    ->translateLabel()
                    ->numeric()
                    ->formatStateUsing(
                        fn (
                            CampaignMessage $record,
                        ): string => $record->age_from.
                            ' - '.
                            $record->age_to,
                    ),
                TextColumn::make('sex')
                    ->label('Sex')
                    ->formatStateUsing(
                        fn (CampaignMessage $record): string => __($record->sex),
                    )
                    ->translateLabel(),
                TextColumn::make('providers.name')
                    ->label('Providers')
                    ->badge()
                    ->translateLabel(),
                TextColumn::make('cities.name')
                    ->label('Cities')
                    ->badge()
                    ->translateLabel(),
                TextColumn::make('quantity')
                    ->label('Quantity')
                    ->translateLabel()
                    ->numeric()
                    ->formatStateUsing(
                        fn (CampaignMessage $record): int => $record->quantity,
                    ),
            ])
            ->filters([
                //
            ])
            ->actions([EditAction::make(), DeleteAction::make()])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaignMessage::route('/'),
            'create' => Pages\CreateCampaignMessage::route('/create'),
            'edit' => Pages\EditCampaignMessage::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('CampaignMessage');
    }

    public static function getLabel(): string
    {
        return __('CampaignMessage');
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }
}
