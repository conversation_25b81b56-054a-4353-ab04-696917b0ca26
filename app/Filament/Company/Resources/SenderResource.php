<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\SenderResource\Pages;
use App\Models\Provider;
use App\Models\Sender;
use Carbon\CarbonImmutable;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class SenderResource extends Resource
{
    protected static ?string $model = Sender::class;

    protected static ?string $navigationIcon = 'heroicon-o-swatch';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('sender')
                            ->label('Sender')
                            ->translateLabel()
                            ->maxLength(11)
                            ->minLength(3)
                            ->required(),
                        Select::make('category')
                            ->label('Category')
                            ->translateLabel()
                            ->options([
                                'SenderId' => __('SenderId'),
                                'ShortNumber' => __('ShortNumber'),
                            ])
                            ->required(),
                        RichEditor::make('notes')
                            ->label('Notes')
                            ->translateLabel(),
                        Forms\Components\Repeater::make('provider')
                            ->label('Providers')
                            ->translateLabel()
                            ->relationship()
                            ->schema([
                                Select::make('provider_id')
                                    ->label('Provider')
                                    ->translateLabel()
                                    ->options(
                                        fn (): array => Provider::pluck(
                                            'name',
                                            'id',
                                        )->all(),
                                    )
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->required(),
                            ])
                            ->itemLabel(function (array $state) {
                                if (! empty($state['id'])) {
                                    $provider = Provider::find(
                                        $state['provider_id'],
                                    );

                                    return $provider->name ??
                                        __('New provider');
                                }

                                return __('Select provider');
                            }),
                    ]),
                ])->columnSpan(
                    fn (?Sender $record): int => $record instanceof Sender
                        ? 2
                        : 3,
                ),
                Group::make([
                    Section::make('')
                        ->schema([
                            Placeholder::make('status')
                                ->label('Status')
                                ->translateLabel()
                                ->content(
                                    fn (?Sender $record) => $record instanceof Sender
                                        ? __($record->status)
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Sender $record): bool => $record instanceof Sender,
                        ),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (?Sender $record) => $record instanceof Sender && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?Sender $record,
                                    ): string => $record instanceof Sender &&
                                    $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (?Sender $record): bool => $record instanceof Sender,
                        ),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?Sender $record): bool => $record instanceof Sender,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        $company = Filament::getTenant();
        assert($company instanceof \App\Models\Company);

        return $table
            ->emptyStateHeading(__('No senders found'))
            ->query(
                Sender::query()
                    ->whereRelation('companies', 'company_id', $company->id)
                    ->with('companies'),
            )
            ->columns([
                TextColumn::make('sender')
                    ->label('Sender')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'pending' => 'warning',
                            'active' => 'success',
                            'rejected' => 'danger',
                            default => 'info',
                        },
                    ),
                TextColumn::make('type')
                    ->label('Type of Use')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => __($state)),
                TextColumn::make('provider.provider.name')
                    ->label('Providers')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('category')
                    ->label('Category')
                    ->translateLabel()
                    ->searchable()
                    ->formatStateUsing(fn (string $state): string => __($state)),
                TextColumn::make('id')
                    ->label('Expired At')
                    ->translateLabel()
                    ->formatStateUsing(function (Sender $record): string {
                        /** @var string $expired_at */
                        $expired_at = $record->companies
                            ->first()
                            ?->pivot?->getAttributeValue('expired_at');
                        if (! $expired_at) {
                            return '-';
                        }

                        return CarbonImmutable::parse($expired_at)->format(
                            'Y-m-d',
                        );
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->disabled(
                    fn (Sender $record): bool => ! (
                        $record->status === 'pending' &&
                        $record->company_id === $company->id
                    ),
                ),
            ])
            ->bulkActions([
                /*Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ])*/
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSenders::route('/'),
            'create' => Pages\CreateSender::route('/create'),
            //            'edit' => Pages\EditSender::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Sending numbers');
    }

    public static function getLabel(): string
    {
        return __('Sending number');
    }
}
