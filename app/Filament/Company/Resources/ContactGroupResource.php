<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\ContactGroupResource\Pages;
use App\Models\ContactGroup;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

final class ContactGroupResource extends Resource
{
    protected static ?string $model = ContactGroup::class;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('')->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required(),
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->options([
                                'active' => __('Active'),
                                'inactive' => __('Inactive'),
                            ])
                            ->default('inactive'),
                        TextInput::make('reference')
                            ->label('Reference')
                            ->translateLabel(),
                        Textarea::make('description')
                            ->label('Description')
                            ->translateLabel(),
                    ]),
                ])->columnSpan(
                    fn (?ContactGroup $record): int => $record instanceof ContactGroup
                        ? 2
                        : 3,
                ),
                Group::make([
                    Section::make('')
                        ->schema([
                            Placeholder::make('')
                                ->label('Status')
                                ->translateLabel()
                                ->content(function (
                                    ContactGroup $record,
                                ): HtmlString {
                                    $trans = __($record->status);

                                    return new HtmlString(
                                        "<span class='bg-green-100 text-green-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300'>$trans</span>",
                                    );
                                }),
                        ])
                        ->visible(
                            fn (
                                ?ContactGroup $record,
                            ): bool => $record instanceof ContactGroup,
                        ),
                    Section::make('')
                        ->schema([
                            Placeholder::make('created_at')
                                ->label('Created At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?ContactGroup $record,
                                    ) => $record instanceof ContactGroup && $record->created_at
                                        ? $record->created_at->diffForHumans()
                                        : '-',
                                ),
                            Placeholder::make('updated_at')
                                ->label('Updated At')
                                ->translateLabel()
                                ->content(
                                    fn (
                                        ?ContactGroup $record,
                                    ): string => $record instanceof ContactGroup && $record->updated_at
                                        ? $record->updated_at->diffForHumans()
                                        : '-',
                                ),
                        ])
                        ->visible(
                            fn (
                                ?ContactGroup $record,
                            ): bool => $record instanceof ContactGroup,
                        ),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?ContactGroup $record): bool => $record instanceof ContactGroup,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No contact groups found'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->icon(
                        fn (string $state): string => match ($state) {
                            'active' => 'heroicon-o-check-circle',
                            'inactive' => 'heroicon-o-x-circle',
                            default => 'heroicon-o-check-circle',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            default => 'success',
                        },
                    ),
                TextColumn::make('reference')
                    ->label('Reference')
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->date(),
            ])
            ->filters([
                //
            ])
            ->actions([Tables\Actions\EditAction::make()])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditContactGroup::class,
            Pages\ManageContacts::class,
        ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContactGroups::route('/'),
            'create' => Pages\CreateContactGroup::route('/create'),
            'edit' => Pages\EditContactGroup::route('/{record}/edit'),
            'contacts' => Pages\ManageContacts::route('/{record}/contacts'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return __('Messages');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Contacts');
    }

    public static function getLabel(): string
    {
        return __('Contacts');
    }
}
