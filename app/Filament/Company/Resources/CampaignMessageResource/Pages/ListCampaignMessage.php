<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\CampaignMessageResource\Pages;

use App\Filament\Company\Resources\CampaignMessageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListCampaignMessage extends ListRecords
{
    protected static string $resource = CampaignMessageResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\CreateAction::make()];
    }
}
