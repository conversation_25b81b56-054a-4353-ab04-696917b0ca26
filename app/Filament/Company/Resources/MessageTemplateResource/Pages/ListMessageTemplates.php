<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageTemplateResource\Pages;

use App\Filament\Company\Resources\MessageTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMessageTemplates extends ListRecords
{
    protected static string $resource = MessageTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
