<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageTemplateResource\Pages;

use App\Enums\MessageTemplateStatus;
use App\Filament\Company\Resources\MessageTemplateResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMessageTemplate extends CreateRecord
{
    protected static string $resource = MessageTemplateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['status'] = MessageTemplateStatus::pending->value;

        return $data;
    }
}
