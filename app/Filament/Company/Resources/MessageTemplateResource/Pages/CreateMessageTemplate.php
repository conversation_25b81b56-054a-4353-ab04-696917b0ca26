<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\MessageTemplateResource\Pages;

use App\Enums\MessageTemplateStatus;
use App\Filament\Company\Resources\MessageTemplateResource;
use App\Services\AI\TemplateGenerator;
use Exception;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class CreateMessageTemplate extends CreateRecord
{
    protected static string $resource = MessageTemplateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['status'] = MessageTemplateStatus::pending->value;

        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('generate')
                ->label('Generate Template')
                ->translateLabel()
                ->form([
                    Select::make('type')
                        ->label('Type')
                        ->translateLabel()
                        ->options([
                            'welcome' => __('Welcome Message'),
                            'verification' => __('Verification Code'),
                            'notification' => __('Notification'),
                            'reminder' => __('Reminder'),
                        ])
                        ->required(),
                    Select::make('language')
                        ->label('Language')
                        ->translateLabel()
                        ->options([
                            'ar' => __('Arabic'),
                            'en' => __('English'),
                        ])
                        ->required(),
                    Textarea::make('description')
                        ->label('Description')
                        ->translateLabel()
                        ->required(),
                ])
                ->slideOver()
                ->closeModalByClickingAway()
                ->closeModalByEscaping()

                ->action(function (array $data): void {
                    $this->generateTemplate($data);
                }),
        ];
    }

    private function generateTemplate(array $data): void
    {
        /** @var array{type: string, description: string, language: string} $data */
        $templateType = $data['type'];
        $description = $data['description'];
        $language = $data['language'];

        try {
            $generator = new TemplateGenerator(
                $templateType,
                $description,
                $language
            );

            $result = $generator->generate();

            if ($result['success']) {

                $this->dispatch('template-generated', [
                    'content' => $result['content'],
                    'parameters' => $result['parameters'] ?? [],
                ]);

                $this->form->fill([
                    'content' => $result['content'],
                ]);

            } else {
                /** @var array{error: string|null} $result */
                Notification::make()
                    ->title(__('Error'))
                    ->body($result['error'] ?? __('Failed to generate template'))
                    ->danger()
                    ->send();

                $this->addError('generation', $result['error'] ?? __('Failed to generate template'));
            }

        } catch (Exception $e) {
            Notification::make()
                ->title(__('Error'))
                ->body($e->getMessage())
                ->danger()
                ->send();
            Log::error('Failed to generate template: '.$e->getMessage());
        } catch (GuzzleException $e) {

            Notification::make()
                ->title(__('Error'))
                ->body(__('Failed to connect to AI service'))
                ->danger()
                ->send();

            Log::error('Failed to connect to AI service: '.$e->getMessage());
        }
    }
}
