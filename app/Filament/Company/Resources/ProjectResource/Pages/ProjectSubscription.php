<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Filament\Company\Resources\ProjectResource;
use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\Plan;
use App\Models\Subscription;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class ProjectSubscription extends ManageRelatedRecords
{
    protected static string $resource = ProjectResource::class;

    protected static string $relationship = 'subscription';

    protected static ?string $navigationIcon = 'heroicon-o-fire';

    public static function getNavigationLabel(): string
    {
        return __('Subscriptions');
    }

    public function getTitle(): string
    {
        return __('Subscriptions');
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No subscriptions found'))
            ->columns([
                TextColumn::make('plan.name')
                    ->label('Plan Name')
                    ->translateLabel(),
                TextColumn::make('started_at')
                    ->label('Started At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('expired_at')
                    ->label('Expired At')
                    ->translateLabel()
                    ->sortable()
                    ->date(),
                TextColumn::make('id')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->color(function (Subscription $record): string {
                        if ($record->canceled_at) {
                            return 'danger';
                        }

                        if ($record->expired_at >= now()) {
                            return 'success';
                        }

                        return 'danger';
                    })
                    ->formatStateUsing(function (Subscription $record) {
                        if ($record->canceled_at) {
                            return __('Canceled');
                        }

                        if ($record->expired_at >= now()) {
                            return __('Active');
                        }

                        return __('Expired');
                    }),
                TextColumn::make('canceled_at')
                    ->label('Canceled At')
                    ->translateLabel()
                    ->date(),
                TextColumn::make('project_id')
                    ->label('Messages')
                    ->translateLabel()
                    ->formatStateUsing(function (Subscription $record): string {
                        /** @var Plan $plan */
                        $plan = $record->plan;

                        /** @var Feature $feature */
                        $feature = $plan->feature()->first();

                        $balance = FeatureConsumption::where(
                            'feature_id',
                            $feature->id,
                        )
                            ->where('subscription_id', $record->id)
                            ->sum('consumption');

                        $total = $feature
                            ->featurePlans()
                            ->where('plan_id', $record->plan_id)
                            ->sum('charges');

                        return number_format((int) $total).
                            ' '.
                            '/ '.
                            number_format((int) $total - (int) $balance);

                    }),
            ])
            ->filters([
                //
            ]);
    }
}
