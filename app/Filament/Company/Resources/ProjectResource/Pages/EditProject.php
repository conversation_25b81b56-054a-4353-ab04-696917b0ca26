<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Filament\Company\Resources\ProjectResource;
use App\Models\Project;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

final class EditProject extends EditRecord
{
    protected static string $resource = ProjectResource::class;

    public static function getNavigationLabel(): string
    {
        return __('Edit Project');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()->disabled(
                fn (Project $record): bool => (bool) $record->subscription,
            ),
            Actions\Action::make('new-token')
                ->label('New Token')
                ->translateLabel()
                ->icon('heroicon-o-key')
                ->requiresConfirmation()
                ->modalHeading(__('Create a new token'))
                ->modalDescription(
                    __(
                        'Are you sure you want to create a new token? the old one will be deleted.',
                    ),
                )
                ->modalSubmitActionLabel(__('Yes, create a new token'))
                ->action(function (Project $record): void {
                    // delete all tokens
                    $record->tokens()->delete();

                    // create a new token
                    $token = $record->createToken('projects');
                    $record->token = $token->plainTextToken;
                    $record->save();

                    Notification::make()
                        ->title(__('New Token'))
                        ->body(__('New token generated for the project'))
                        ->icon('heroicon-o-key')
                        ->success()
                        ->send();

                }),
        ];
    }
}
