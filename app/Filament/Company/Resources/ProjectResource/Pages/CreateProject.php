<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Filament\Company\Resources\ProjectResource;
use App\Models\Company;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;

final class CreateProject extends CreateRecord
{
    protected static string $resource = ProjectResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $company = Filament::getTenant();
        assert($company instanceof Company);
        $data['company_id'] = $company->id;

        return $data;
    }
}
