<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Filament\Company\Resources\ProjectResource;
use App\Models\IpWhitelist;
use App\Models\Project;
use Filament\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ListIpwhitelist extends ManageRelatedRecords
{
    protected static string $resource = ProjectResource::class;

    protected static string $relationship = 'ipWhitelists';

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    public static function getNavigationLabel(): string
    {
        return __('IP Whitelist');
    }

    public function getTitle(): string
    {
        return __('IP Whitelist');
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No IP Whitelist found'))
            ->columns([
                TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->translateLabel(),
                TextColumn::make('description')
                    ->label('Description')
                    ->translateLabel(),
            ])
            ->actions([
                Action::make('delete')
                    ->label('Delete')
                    ->translateLabel()
                    ->requiresConfirmation()
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    ->action(fn (IpWhitelist $record) => $record->delete()),
            ])
            ->filters([
                //
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('new-ipwhitelist')
                ->label('New IP Whitelist')
                ->translateLabel()
                ->form($this->newIpWhitelistForm())
                ->action(function (array $data, Project $record): void {
                    /** @var array{ip_address: string, description: string} $data */
                    $this->newIpWhitelist($data, $record);
                }),
        ];
    }

    /** @return array<Component> */
    private function newIpWhitelistForm(): array
    {
        return [
            TextInput::make('ip_address')
                ->label('IP Address')
                ->ip()
                ->validationMessages([
                    'ip' => __('The :attribute must be a valid IP address.'),
                ])
                ->translateLabel()
                ->required(),
            TextInput::make('description')
                ->label('Description')
                ->translateLabel(),
        ];
    }

    /** @param  array{ip_address: string, description: string}  $data */
    private function newIpWhitelist(array $data, Project $record): void
    {
        $project = $record;

        IpWhitelist::create([
            'ip_address' => $data['ip_address'],
            'description' => $data['description'],
            'project_id' => $project->id,
        ]);

        Notification::make()
            ->title(__('IP Whitelist Successful'))
            ->body(__('You have successfully IP Whitelist to ').$project->name)
            ->success()
            ->send();
    }
}
