<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Enums\FeatureConsumptionType;
use App\Filament\Company\Resources\ProjectResource;
use App\Models\Plan;
use App\Models\Subscription;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

final class ProjectConsumption extends ManageRelatedRecords
{
    protected static string $resource = ProjectResource::class;

    protected static string $relationship = 'consumption';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public static function getNavigationLabel(): string
    {
        return __('Consumption');
    }

    public function getTitle(): string
    {
        return __('Subscriptions');
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No consumptions found'))
            ->columns([
                TextColumn::make('consumption')
                    ->label('Consumption')
                    ->translateLabel(),
                TextColumn::make('type')
                    ->badge()
                    ->label('Type')
                    ->color(
                        fn (string $state): string => match ($state) {
                            FeatureConsumptionType::SMS->value => 'primary',
                            FeatureConsumptionType::OTP->value => 'info',
                            default => 'secondary',
                        },
                    )
                    ->translateLabel(),
                TextColumn::make('subscription_id')
                    ->label('Subscription')
                    ->translateLabel()
                    ->formatStateUsing(function (string $state) {
                        $subscription = Subscription::find($state);
                        assert($subscription instanceof Subscription);
                        assert($subscription->plan instanceof Plan);

                        return $subscription->plan->name;
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime(),
            ]);
    }
}
