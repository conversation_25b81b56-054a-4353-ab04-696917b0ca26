<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources\ProjectResource\Pages;

use App\Filament\Company\Resources\ProjectResource;
use App\Models\ContactGroup;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

final class ManageContact extends ManageRelatedRecords
{
    protected static string $resource = ProjectResource::class;

    protected static string $relationship = 'contact_groups';

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationLabel(): string
    {
        return __('Contacts');
    }

    public function getTitle(): string
    {
        return __('Contacts');
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->emptyStateHeading(__('No contacts found'))
            ->modifyQueryUsing(fn (Builder $query) => $query->withCount('contacts'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->icon(
                        fn (string $state): string => match ($state) {
                            'active' => 'heroicon-o-check-circle',
                            'inactive' => 'heroicon-o-x-circle',
                            default => '',
                        },
                    )
                    ->formatStateUsing(fn (string $state): string => __($state))
                    ->color(
                        fn (string $state): string => match ($state) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            default => 'info',
                        },
                    ),
                TextColumn::make('reference')
                    ->label('Reference')
                    ->translateLabel(),
                TextColumn::make('contacts_count')
                    ->label('Contacts')
                    ->translateLabel()
                    ->formatStateUsing(
                        fn (ContactGroup $record): string => $record->contacts_count.
                            ' '.
                            __('contacts'),
                    ),
            ])
            ->headerActions([Tables\Actions\AttachAction::make()])
            ->actions([Tables\Actions\DetachAction::make()])
            ->bulkActions([Tables\Actions\DetachBulkAction::make()]);
    }
}
