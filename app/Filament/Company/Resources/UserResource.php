<?php

declare(strict_types=1);

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\UserResource\Pages;
use App\Models\Company;
use App\Models\Role;
use App\Models\User;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

final class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function form(Form $form): Form
    {
        /** @var Company $tenant */
        $tenant = Filament::getTenant();

        return $form
            ->schema([
                Group::make([
                    Section::make(__('User info'))
                        ->translateLabel()
                        ->schema([
                            TextInput::make('name')
                                ->label('Name')
                                ->translateLabel()
                                ->required(),
                            TextInput::make('email')
                                ->label('Email')
                                ->translateLabel()
                                ->email()
                                ->required(),
                        ]),
                    Section::make(__('Auth Info'))->schema([
                        TextInput::make('password')
                            ->label('Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->dehydrated(
                                fn (?string $state): bool => filled($state),
                            )
                            ->required(
                                fn (string $operation): bool => $operation ===
                                    'create',
                            )
                            ->confirmed(),
                        TextInput::make('password_confirmation')
                            ->label('Confirm Password')
                            ->translateLabel()
                            ->password()
                            ->revealable()
                            ->dehydrated(false)
                            ->required(
                                fn (string $operation): bool => $operation ===
                                    'create',
                            ),
                    ]),
                    Section::make(__('Roles'))->schema([
                        Select::make('roles')
                            ->nullable()
                            ->label('Roles')
                            ->translateLabel()
                            ->relationship('roles', 'name')
                            ->multiple()
                            ->preload()
                            ->options(
                                Role::whereRelation(
                                    'company',
                                    'company_id',
                                    $tenant->id,
                                )->pluck('name', 'id'),
                            ),
                    ]),
                ])->columnSpan(
                    fn (?User $record): int => $record instanceof User ? 2 : 3,
                ),
                Group::make([
                    Section::make('')->schema([
                        Placeholder::make('created_at')
                            ->label('Created At')
                            ->translateLabel()
                            ->content(
                                fn (?User $record) => $record instanceof User && $record->created_at
                                    ? $record->created_at->diffForHumans()
                                    : '-',
                            ),
                        Placeholder::make('updated_at')
                            ->label('Updated At')
                            ->translateLabel()
                            ->content(
                                fn (?User $record): string => $record instanceof User && $record->updated_at
                                    ? $record->updated_at->diffForHumans()
                                    : '-',
                            ),
                    ]),
                ])
                    ->columnSpan(1)
                    ->visible(
                        fn (?User $record): bool => $record instanceof User,
                    ),
            ])
            ->columns([
                'lg' => 3,
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with('company'))
            ->columns([
                TextColumn::make('id')
                    ->label('Default User')
                    ->translateLabel()
                    ->formatStateUsing(function (
                        User $record,
                    ): HtmlString|string {
                        $company = Filament::getTenant();
                        if (
                            ! empty($company->default_user_id) &&
                            $company->default_user_id === $record->id
                        ) {
                            return new HtmlString(
                                svg(
                                    'heroicon-o-check-badge',
                                    'h-5 text-primary-600',
                                )->toHtml(),
                            );
                        }

                        return '';
                    }),
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel(),
            ])
            ->filters([TrashedFilter::make()])
            ->actions([
                Tables\Actions\EditAction::make(),
                ForceDeleteAction::make(),
                RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getPluralModelLabel(): string
    {
        return __('Users');
    }

    public static function getModelLabel(): string
    {
        return __('User');
    }

    public static function getNavigationGroup(): string
    {
        return __('filament-shield::filament-shield.nav.group');
    }

    public static function getLabel(): string
    {
        // @codeCoverageIgnoreStart
        return __('User');
        // @codeCoverageIgnoreEnd
    }
}
