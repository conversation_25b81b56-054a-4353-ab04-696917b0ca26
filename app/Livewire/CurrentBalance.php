<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Company;
use Filament\Facades\Filament;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Livewire\Component;

final class CurrentBalance extends Component
{
    public bool $isVisible = false;

    public int|float $balance = 0;

    public function mount(): void
    {
        /** @var ?Company $company */
        $company = Filament::getTenant();

        if (! empty($company)) {
            $this->isVisible = true;
            $this->balance = $company->balance();
        }
    }

    public function render(): Factory|View|\Illuminate\View\View
    {
        return view('livewire.current-balance');
    }
}
