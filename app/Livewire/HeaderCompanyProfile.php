<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Filament\Company\Pages\Profile;
use App\Models\Company;
use Filament\Facades\Filament;
use Illuminate\View\View;
use Livewire\Component;

class HeaderCompanyProfile extends Component
{
    public bool $isVisible = false;

    public string $url = '';

    public function mount(): void
    {
        /** @var ?Company $company */
        $company = Filament::getTenant();

        if (! empty($company) && $company->verified_at === null) {
            $this->isVisible = true;
            $this->url = Profile::getUrl();
        }
    }

    public function render(): View
    {
        return view('livewire.header-company-profile');
    }
}
