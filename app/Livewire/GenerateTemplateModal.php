<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\AI\TemplateGenerator;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class GenerateTemplateModal extends Component
{
    public string $templateType = '';
    public string $description = '';
    public string $language = 'ar';
    public bool $isGenerating = false;

    protected $listeners = [
        'reset-form' => 'resetForm',
    ];

    protected $rules = [
        'templateType' => 'required|string|in:welcome,verification,notification,reminder',
        'description' => 'required|string|min:10|max:500',
        'language' => 'required|string|in:ar,en',
    ];

    protected $messages = [
        'templateType.required' => 'Please select a template type.',
        'templateType.in' => 'Please select a valid template type.',
        'description.required' => 'Please provide a description.',
        'description.min' => 'Description must be at least 10 characters.',
        'description.max' => 'Description must not exceed 500 characters.',
        'language.required' => 'Please select a language.',
        'language.in' => 'Please select a valid language.',
    ];

    public function generateTemplate()
    {
        $this->validate();

        $this->isGenerating = true;

        try {
            $generator = new TemplateGenerator(
                $this->templateType,
                $this->description,
                $this->language
            );

            $result = $generator->generate();

            if ($result['success']) {
                $this->dispatch('template-generated', [
                    'content' => $result['content'],
                    'parameters' => $result['parameters'] ?? [],
                ]);

                $this->dispatch('close-modal');
                
                session()->flash('template-success', __('Template generated successfully!'));
            } else {
                $this->addError('generation', $result['error'] ?? __('Failed to generate template'));
            }

        } catch (\Exception $e) {
            Log::error('GenerateTemplateModal: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->addError('generation', __('An error occurred while generating the template'));
        } finally {
            $this->isGenerating = false;
        }
    }

    public function resetForm()
    {
        $this->templateType = '';
        $this->description = '';
        $this->language = 'ar';
        $this->resetErrorBag();
    }

    public function render()
    {
        return view('livewire.generate-template-modal');
    }
}
