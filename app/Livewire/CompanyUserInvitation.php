<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Invitation;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\SimplePage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\Rules\Password;

/**
 * @property Form $form
 */
final class CompanyUserInvitation extends SimplePage
{
    use InteractsWithFormActions;
    use InteractsWithForms;

    public string $invitation;

    /** @var array{name: string, email: string, password: string, password_confirmation: string}|null */
    public ?array $data = null;

    protected static string $view = 'livewire.company-user-invitation';

    public function mount(): void
    {
        $invitationModel = Invitation::findOrFail($this->invitation);

        // Check if the user already a member of the company
        if (
            $invitationModel->company
                ?->users()
                ->where('email', $invitationModel->email)
                ->exists()
        ) {

            $invitationModel->delete();

            abort(404);
        }

        $this->form->fill([
            'email' => $invitationModel->email,
        ]);
    }

    public function form(Form $form): Form
    {
        $action = $this->action();

        if ($action === 'register') {
            return $form
                ->schema([
                    TextInput::make('name')->label(__('Name'))->required(),
                    TextInput::make('email')
                        ->label(__('Email'))
                        ->email()
                        ->disabled(),
                    TextInput::make('password')
                        ->label(__('Password'))
                        ->password()
                        ->required()
                        ->rule(Password::default())
                        ->dehydrateStateUsing(
                            fn (string $state): string => Hash::make($state),
                        )
                        ->same('password_confirmation'),
                    TextInput::make('password_confirmation')
                        ->label(__('Confirm Password'))
                        ->password()
                        ->required()
                        ->dehydrated(false),
                ])
                ->statePath('data');
        }
        if ($action === 'accept') {
            return $form->schema([])->statePath('data');
        }
        if ($action === 'logout') {
            return $form->schema([])->statePath('data');
        }

        return $form
            ->schema([
                TextInput::make('email')
                    ->label(__('Email'))
                    ->email()
                    ->disabled(),
                TextInput::make('password')
                    ->label(__('Password'))
                    ->password()
                    ->required(),
            ])
            ->statePath('data');
    }

    public function acceptInvitation(): void
    {
        $action = $this->action();

        if ($action === 'register') {
            $this->register();
        } elseif ($action === 'accept') {
            $this->accept();
        } elseif ($action === 'logout') {
            $this->logout();
        } else {
            $this->login();
        }
    }

    /** @return array<Action> */
    public function getFormActions(): array
    {
        $label = match ($this->action()) {
            'register' => __('Register'),
            'accept' => __('Accept'),
            'logout' => __('Logout'),
            default => __('Login'),
        };

        return [Action::make('accept')->label($label)->submit('accept')];
    }

    public function getHeading(): string
    {
        return __('Accept Invitation');
    }

    public function hasLogo(): bool
    {
        return false;
    }

    private function action(): string
    {
        $invitationModel = Invitation::findOrFail($this->invitation);
        $authenticatedUser = Auth::user();
        $user = User::where('email', $invitationModel->email)->first();

        if (
            $authenticatedUser &&
            (! $user || $authenticatedUser->id !== $user->id)
        ) {
            return 'logout';
        }
        if (! $user) {
            return 'register';
        }
        if ($authenticatedUser && $authenticatedUser->id === $user->id) {
            return 'accept';
        }

        return 'login';
    }

    private function register(): void
    {
        $invitationModel = Invitation::findOrFail($this->invitation);

        $user = User::create([
            'name' => $this->form->getState()['name'],
            'email' => $invitationModel->email,
            'password' => $this->form->getState()['password'],
        ]);

        $this->applyInvitation($invitationModel, $user);

        Auth::login($user);

        $this->redirect('/company/'.$invitationModel->company?->id);
    }

    private function accept(): void
    {
        $invitationModel = Invitation::findOrFail($this->invitation);

        $user = User::where('email', $invitationModel->email)->firstOrFail();

        $this->applyInvitation($invitationModel, $user);

        redirect('/company/'.$invitationModel->company?->id);
    }

    private function logout(): void
    {
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();
        $this->redirect(
            URL::signedRoute('invitation.accept', [
                'invitation' => $this->invitation,
            ]),
        );
    }

    private function login(): void
    {
        $invitationModel = Invitation::findOrFail($this->invitation);

        $user = User::where('email', $invitationModel->email)->firstOrFail();

        // Validate the user's credentials
        if (
            ! Auth::attempt([
                'email' => $invitationModel->email,
                'password' => $this->form->getState()['password'],
            ])
        ) {
            $this->addError(
                'data.password',
                __('The provided credentials are incorrect.'),
            );

            return;
        }

        $this->applyInvitation($invitationModel, $user);

        $this->redirect('/company/'.$invitationModel->company?->id);
    }

    private function applyInvitation(Invitation $invitation, User $user): void
    {
        $invitation->company?->users()->attach($user);

        if (! empty($invitation->role)) {
            $user->assignRole($invitation->role);
            $invitation->company?->update(['default_user_id' => $user->id]);
        }

        // Delete all invitations for the user after they have accepted one for the company
        Invitation::where('email', $user->email)
            ->where('company_id', $invitation->company?->id)
            ->delete();
    }
}
