<?php

declare(strict_types=1);

namespace App\Rules;

use App\Models\Provider;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Translation\PotentiallyTranslatedString;

final class PhoneNumberRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  Closure(string): PotentiallyTranslatedString  $fail
     */
    public function validate(
        string $attribute,
        mixed $value,
        Closure $fail,
    ): void {
        if (! is_string($value)) {
            $fail('The :attribute must be a string.');

            return;
        }
        if (! $this->passes($value)) {
            $fail($this->message());
        }
    }

    /**
     * Determine if the validation rule passes.
     */
    private function passes(string $value): bool
    {
        $providers = Provider::where('status', 'active')->get();

        foreach ($providers as $provider) {
            if (preg_match("/{$provider->pattern}/", $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the validation error message.
     */
    private function message(): string
    {
        return __('The :attribute must be valid in system providers.');
    }
}
