<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\FAQ;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FAQResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if (! $this->resource) {
            return [];
        }

        /** @var FAQ $resource */
        $resource = $this->resource;

        return [
            'question' => $resource->question,
            'answer' => $resource->answer,
        ];
    }
}
