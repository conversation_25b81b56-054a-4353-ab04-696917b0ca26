<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\FeaturePlan;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if (! $this->resource) {
            return [];
        }

        /** @var Plan $resource */
        $resource = $this->resource;

        // Calculate periodicity in the past months
        $periodicity_quantity = (int) $resource->periodicity_quantity;

        $periodicity = match ($resource->periodicity_type) {
            'Quarterly' => 3 * $periodicity_quantity,
            'Yearly' => 12 * $periodicity_quantity,
            'Weekly' => ((1 / 4) * $periodicity_quantity), // Approximately 1/4 of a month
            'Daily' => ((1 / 30) * $periodicity_quantity),  // Approximately 1/30 of a month
            default => $periodicity_quantity,
        };

        // Initialize default values
        $charges = 0;
        $price_per_message = 0;
        $feature_name = null;

        // Check if features exist
        if ($resource->features->isNotEmpty()) {
            /** @var FeaturePlan $firstFeature */
            $firstFeature = $resource->features->first();

            // Safely get the charge value
            if ($firstFeature->charges !== null) {

                $charges = (int) $firstFeature->charges;

                if ($charges !== 0) {
                    $price_per_message = (float) $resource->price / $charges;
                }

            }

            // Get feature name safely
            if ($firstFeature->feature) {
                $feature_name = $firstFeature->feature->name;
            }
        }

        return [
            'name' => $resource->name,
            'duration' => round($periodicity, 2),
            'quantity' => $charges,
            'price' => $resource->price,
            'price_per_message' => round($price_per_message, 3),
            'type' => $feature_name,
            'tags' => $resource->tags,
        ];
    }
}
