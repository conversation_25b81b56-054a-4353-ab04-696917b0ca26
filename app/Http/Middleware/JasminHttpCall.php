<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class JasminHttpCall
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $clientIp = App::isProduction() ?
            // Get the client IP address from the CF-Connecting-IP header
            $request->header('cf-connecting-ip') :
            // Get the client IP address from the request
            $request->ip();

        /** @var array<string> $jasmin_ips */
        $jasmin_ips = config('jasmin_client.ips');
        if (! in_array($clientIp, $jasmin_ips)) {
            Log::info('Invalid IP address', ['ip' => $clientIp]);

            return new \Illuminate\Http\Response('Invalid IP address', 400);
        }

        return $next($request);
    }
}
