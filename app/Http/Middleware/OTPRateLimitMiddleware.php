<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Project;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

final class OTPRateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $project = Project::where('token', $request->bearerToken())->first();

        if (! $project) {
            return response()->json(
                ['message' => 'Invalid or missing token.'],
                401,
            );
        }

        /** @var string $phoneNumber */
        $phoneNumber = $request->input('receiver');

        if (! $phoneNumber) {
            return response()->json(
                ['message' => 'Receiver is required.'],
                400,
            );
        }

        $cacheKey = "otp_rate_limit:{$project->id}:{$phoneNumber}";

        $maxRequests = 10; // Maximum OTP requests allowed
        /** @var int $timeWindow */
        $timeWindow = 60 * 60; // Time window in seconds (1 hour)

        // Get current request count from cache
        /** @var int $requestCount */
        $requestCount = Cache::get($cacheKey, 0);

        if ($requestCount >= $maxRequests) {
            return response()
                ->json(
                    [
                        'message' => 'Too many OTP requests. Please try again later.',
                    ],
                    429,
                )
                ->header('Retry-After', (string) $timeWindow);
        }

        // Increment request count and set cache expiration
        Cache::put($cacheKey, $requestCount + 1, $timeWindow);

        // Proceed with the request
        return $next($request);
    }
}
