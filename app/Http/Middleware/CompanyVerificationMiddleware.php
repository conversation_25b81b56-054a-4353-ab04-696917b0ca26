<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Company;
use App\Models\Project;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * CompanyVerificationMiddleware
 *
 * This middleware checks if a company's verified_at field is null.
 * If it is null (company is not verified):
 * - For single/bulk SMS: redirects messages to the company's phone number
 * - For contacts_send: returns an error message blocking the feature
 *
 * This ensures that unverified companies can only send SMS messages to
 * their own phone number for testing/verification purposes and cannot
 * use advanced features like contact groups.
 */
final class CompanyVerificationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * Check if company verified_at is null and redirect SMS to the company phone if so.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the project from the bearer token
        $project = Project::where('token', $request->bearerToken())->with('company')->first();

        if (! $project) {
            return response()->json(
                ['message' => 'Invalid or missing token.'],
                401,
            );
        }

        /** @var Company $company */
        $company = $project->company;

        // Check if company verified_at is null
        if (is_null($company->verified_at)) {
            // For contacts_send endpoint, return an error message instead of redirecting
            if ($request->has('contact_group_id')) {
                return response()->json([
                    'message' => 'You can not use this feature until verify your company',
                ], 403);
            }

            // For other endpoints, modify the request to set receiver to company phone
            $this->redirectToCompanyPhone($request, $company->phone);
        }

        return $next($request);
    }

    /**
     * Redirect SMS receiver to company phone number
     */
    private function redirectToCompanyPhone(Request $request, string $companyPhone): void
    {
        // Handle a single receiver (send, send_template endpoints)
        if ($request->has('receiver')) {
            $request->merge(['receiver' => $companyPhone]);
        }

        // Handle bulk receivers (bulk_send endpoint)
        if ($request->has('receivers')) {
            $request->merge(['receivers' => [$companyPhone]]);
        }
    }
}
