<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RestrictFrontendAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var string $frontendDomain */
        $frontendDomain = config('app.frontend_url');

        if (in_array($request->getHost(), ['', '0'], true) || $request->schemeAndHttpHost() !== $frontendDomain) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}
