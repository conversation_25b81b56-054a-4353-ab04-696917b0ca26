<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\IpWhitelist;
use App\Models\Project;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class IPWhitelistMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $clientIp = App::isProduction() ?
            // Get the client IP address from the CF-Connecting-IP header
            $request->header('cf-connecting-ip') :
            // Get the client IP address from the request
            $request->ip();

        $project = Project::where('token', $request->bearerToken())->firstOrFail();

        $whiteListedIps = IpWhitelist::select('ip_address')
            ->where('project_id', $project->id)->get()->toArray();

        $flattenedIps = array_column($whiteListedIps, 'ip_address');

        if (! in_array($clientIp, $flattenedIps)) {
            return response()->json(['message' => 'Unauthorized IP address.'], 401);
        }

        return $next($request);
    }
}
