<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\CampaignMessage;
use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\Message;
use App\Models\MessageTemplate;
use App\Models\Project;
use Closure;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApplyTenantScopes
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var Company $tenant */
        $tenant = Filament::getTenant();

        Project::addGlobalScope(
            'tenant',
            fn (Builder $query) => $query->where('company_id', $tenant->id)
        );

        Message::addGlobalScope(
            'tenant',
            fn (Builder $query) => $query->where('company_id', $tenant->id)
        );

        CampaignMessage::addGlobalScope(
            'tenant',
            fn (Builder $query) => $query->where('company_id', $tenant->id)
        );

        MessageTemplate::addGlobalScope(
            'tenant',
            fn (Builder $query) => $query->where('company_id', $tenant->id)
        );

        ContactGroup::addGlobalScope(
            'tenant',
            fn (Builder $query) => $query->where('company_id', $tenant->id)
        );

        return $next($request);
    }
}
