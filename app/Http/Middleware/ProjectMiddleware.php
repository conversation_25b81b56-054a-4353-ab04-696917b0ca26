<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Project;
use Closure;
use Illuminate\Http\Request;

final class ProjectMiddleware
{
    public function handle(Request $request, Closure $next): mixed
    {
        $project = Project::where('token', $request->bearerToken())->first();

        if (! $project) {
            return response()->json(
                ['message' => 'Invalid or missing token.'],
                401,
            );
        }

        if ($project->status === 'inactive') {
            return response()->json(['message' => 'Project is inactive.'], 401);
        }

        return $next($request);
    }
}
