<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\PlanResource;
use App\Models\Plan;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PlanController
{
    /**
     * @api {get} /api/plans Get Plans list
     *
     * @unauthenticated
     *
     * @response json
     *
     * @group Plan
     */
    public function index(): AnonymousResourceCollection
    {
        /** @var Plan $plans */
        $plans = Plan::query()
            ->where('status', '=', 'active')
            ->with(['features.feature'])
            ->get();

        return PlanResource::collection($plans);
    }
}
