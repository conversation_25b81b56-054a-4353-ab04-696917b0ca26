<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\FeatureConsumption;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

final class ProjectController
{
    /**
     * @api {get} /api/project/details Get project details
     *
     * @response json
     *
     * @group Project
     */
    public function details(Request $request): JsonResponse
    {
        /** @var Project $project */
        $project = $request->user();
        assert($project->company instanceof Company);

        $subscriptions = $project
            ->subscription()
            ->where('expired_at', '>=', now())
            ->with('plan')
            ->get();

        $subscriptions->each(function (Subscription $subscription): void {
            assert($subscription->plan instanceof Plan);
            $subscription->setAttribute('plan_name', $subscription->plan->name);
            $subscription->makeHidden(
                'id',
                'plan_id',
                'project_id',
                'deleted_at',
                'updated_at',
            );
            $subscription->makeHidden('plan');
        });

        return response()->json([
            'project_name' => $project->name,
            'description' => $project->description,
            'company' => $project->company->name,
            'status' => $project->status,
            'type' => $project->type,
            'subscription' => $subscriptions,
        ]);
    }

    /**
     * @api {get} /api/project/balance Get project balance
     *
     * @response json
     *
     * @group Project
     *
     * @return array{SMS: array{balance: mixed, limit:mixed}, OTP: array{balance: mixed, limit: mixed}}
     */
    public function balance(Request $request): array
    {
        /** @var Project $project */
        $project = $request->user();

        return [
            'SMS' => [
                'balance' => $this->safeCall(fn () => $project->balance('SMS')),
                'limit' => $this->safeCall(fn () => $project->featureLimit('SMS')),
            ],
            'OTP' => [
                'balance' => $this->safeCall(fn () => $project->balance('OTP')),
                'limit' => $this->safeCall(fn () => $project->featureLimit('OTP')),
            ],
        ];
    }

    /**
     * @api {get} /api/project/contacts/group-id Get project contacts
     *
     * @bodyParam page int nullable
     * @bodyParam per_page int nullable
     * @bodyParam from date nullable
     * @bodyParam to date nullable
     *
     * @response json
     *
     * @group Project
     */
    public function contacts(Request $request): JsonResponse
    {
        /** @var array{'page': int|null, 'per_page': int|null, 'from':Carbon|null, 'to': Carbon|null, 'group_id': null|string} $validatedData */
        $validatedData = Validator::make(
            [
                'page' => $request->page,
                'per_page' => $request->per_page,
                'from' => $request->from,
                'to' => $request->to,
                'group_id' => $request->group_id,
            ],
            [
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1',
                'from' => 'nullable|date',
                'to' => 'nullable|date',
                'group_id' => 'nullable|uuid',
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();

        /** @var ?ContactGroup $contactGroup */
        $contactGroup = $project
            ->contact_groups()
            ->where('id', $validatedData['group_id'])
            ->first();

        if ($contactGroup === null) {
            return response()->json(
                [
                    'message' => 'Contact group not found',
                ],
                404,
            );
        }

        $contactsQuery = $contactGroup->contacts();

        if ($validatedData['from']) {
            $contactsQuery->where('created_at', '>=', $validatedData['from']);
        }

        if ($validatedData['to']) {
            $contactsQuery->where('created_at', '<=', $validatedData['to']);
        }

        $contactsQuery->orderBy('created_at', 'desc');

        $contacts = $contactsQuery->paginate($validatedData['per_page'] ?? 10);

        $contacts->each(function (Contact $contact): void {
            $contact->makeHidden(
                'created_at',
                'updated_at',
                'contact_group_id',
                'id',
            );
            $contact->makeHidden('group');
        });

        return response()->json([
            'data' => $contacts,
        ]);
    }

    /**
     * @api {get} /api/project/consumptions Get project consumptions
     *
     * @bodyParam page int nullable
     * @bodyParam per_page int nullable
     * @bodyParam from date nullable
     * @bodyParam to date nullable
     *
     * @response json
     *
     * @group Project
     */
    public function consumptions(Request $request): JsonResponse
    {
        /** @var array{'page':int|null, 'per_page':int|null, 'from':Carbon|null, 'to':Carbon|null} $validatedData */
        $validatedData = Validator::make(
            [
                'page' => $request->page,
                'per_page' => $request->per_page,
                'from' => $request->from,
                'to' => $request->to,
            ],
            [
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1',
                'from' => 'nullable|date',
                'to' => 'nullable|date',
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();

        $consumptionQuery = $project->consumption();

        if ($validatedData['from']) {
            $consumptionQuery->where(
                'feature_consumptions.created_at',
                '>=',
                $validatedData['from'],
            );
        }

        if ($validatedData['to']) {
            $consumptionQuery->where('feature_consumptions.created_at', '<=', $validatedData['to']);
        }

        $consumptionQuery->orderBy('feature_consumptions.created_at', 'desc');

        $consumption = $consumptionQuery->paginate(
            $validatedData['per_page'] ?? 10,
        );

        $consumption->each(function (FeatureConsumption $cons): void {
            $cons->makeHidden(
                'id',
                'feature_id',
                'subscription_id',
                'laravel_through_key',
            );
        });

        return response()->json([
            'data' => $consumption,
        ]);
    }

    private function safeCall(callable $callback): mixed
    {
        try {
            return $callback();
        } catch (Exception) {
            // Return null or any other default value in case of an exception.
            return 0; // Or log the error if needed.
        }
    }
}
