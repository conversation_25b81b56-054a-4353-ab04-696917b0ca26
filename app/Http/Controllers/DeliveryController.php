<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\MessageReceipt;
use App\Services\Jasmin\JasminClient;
use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class DeliveryController
{
    public function __construct(private readonly JasminClient $jasmin) {}

    /**
     * Handle the incoming deliver report request from jasmin.
     *
     * @hideFromAPIDocumentation
     */
    public function index(Request $request, string $id): Response
    {
        Log::info('Delivery report', $request->all());

        return $this->jasmin->receiveDlrCallback($request, function (DeliveryCallback $dlr) use ($id): true {
            $messageReceipt = MessageReceipt::where('id', $id)->firstOrFail();
            $status = match ($dlr->messageStatus) {
                'ESME_ROK' => 'sent',
                'DELIVRD' => 'delivered',
                'EXPIRED' => 'expired',
                'DELETED' => 'deleted',
                'UNDELIV' => 'undeliverable',
                'ACCEPTD' => 'accepted',
                'UNKNOWN' => 'unknown',
                'REJECTD' => 'rejected',
                default => 'failed',
            };
            $doneDate = $dlr->doneDate !== null && $dlr->doneDate !== '' && $dlr->doneDate !== '0' ? CarbonImmutable::createFromFormat('ymdHi', $dlr->doneDate) : null;
            $messageReceipt->update([
                'smpp_message_id' => $dlr->smscId,
                'status' => $status,
                'delivered_at' => $doneDate,
                'delivery_report' => json_encode($dlr),
            ]);

            return true;
        });

    }
}
