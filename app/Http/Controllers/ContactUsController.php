<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\ContactUs;
use App\Rules\PhoneNumberRule;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContactUsController
{
    /**
     * @api {post} /api/contact-us Create a contact us request
     *
     * @bodyParam name required string
     * @bodyParam phone required string
     * @bodyParam subject required string
     * @bodyParam email required string
     * @bodyParam message required string
     *
     * @response json {"message": "string"}
     *
     * @group Contact Us
     */
    public function store(Request $request): JsonResponse
    {
        /** @var array{'name':string,'phone':string,'subject':string,'email':string,'message':string} $validatedData */
        $validatedData = $request->validate([
            'name' => 'required|string',
            'phone' => ['required', new PhoneNumberRule()],
            'subject' => 'required|string',
            'email' => 'required|email',
            'message' => 'required|string',
        ]);

        ContactUs::create($validatedData);

        return response()->json([
            'message' => __('Thank you for contacting us. We will get back to you soon.'),
        ]);
    }
}
