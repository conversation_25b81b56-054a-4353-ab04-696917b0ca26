<?php

declare(strict_types=1);

namespace App\Imports;

use App\Models\Contact;
use App\Rules\PhoneNumberRule;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

final class ContactImport implements SkipsOnFailure, ToModel, WithHeadingRow, WithValidation
{
    use SkipsFailures;

    public function __construct(public string $group_id) {}

    /**
     * @param  array<string, mixed>  $row
     */
    public function model(array $row): Contact
    {
        return new Contact([
            'contact_group_id' => $this->group_id,
            'name' => $row['name'],
            'phone' => $row['phone'],
            'sex' => $row['sex'],
            'births' => $row['births'],
            'city' => $row['city'],
        ]);
    }

    /**
     * @return array<string, list<mixed>>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'phone' => ['required', new PhoneNumberRule()],
        ];
    }

    public function chunkSize(): int
    {
        return 200;
    }
}
