<?php

declare(strict_types=1);

namespace App\Actions;

use App\Mail\ApprovedRegistration;
use App\Models\Company;
use App\Models\Invitation;
use App\Models\RegistrationRequest;
use App\Models\Setting;
use DB;
use Illuminate\Support\Facades\Mail;
use Throwable;

class ApproveCustomers
{
    private bool $isFreePlan = false;

    public function __construct()
    {
        $setting = Setting::where('key', 'is_free_plan')->first();

        if ($setting) {
            $this->isFreePlan = (bool) $setting->value;
        }
    }

    /**
     * @throws Throwable
     */
    public function execute(RegistrationRequest $registration_request): void
    {
        DB::transaction(function () use ($registration_request): void {
            /** @var Company $company */
            $company = Company::create([
                'name' => $registration_request->company,
                'phone' => $registration_request->phone,
                'email' => $registration_request->email,
                'category' => $registration_request->category,
                'status' => 'active',
            ]);

            $project = $company->projects()->create([
                'name' => "{$company->name} Free Project",
                'description' => 'Main Project',
                'status' => 'active',
                'type' => 'subscription',
                'limit' => 50,
            ]);

            $invitation = Invitation::create([
                'email' => $registration_request->email,
                'company_id' => $company->id,
                'expires_at' => now()->addDays(7),
                'role' => 'company_owner',
            ]);

            if ($this->isFreePlan) {
                $activeFreePlan = new ActiveFreePlan();
                $activeFreePlan->activateFreePlans($project);
            }

            $registration_request->status = 'approved';
            $registration_request->save();

            Mail::to($registration_request->email)->queue(
                new ApprovedRegistration($invitation),
            );
        });
    }
}
