<?php

declare(strict_types=1);

namespace App\Actions;

use App\Models\Plan;
use App\Models\Project;
use App\Models\Setting;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Collection;

class ActiveFreePlan
{
    /**
     * @return Collection<int, Plan>|Plan|null
     */
    public function getFreePlans(): Collection|Plan|null
    {
        /** @var string|null $setting */
        $setting = Setting::where('key', 'free_plan')->first()?->value;

        if (! $setting) {
            return null;
        }

        return Plan::whereIn('id', $setting)->get();
    }

    public function activateFreePlans(Project $project): void
    {
        /** @var Collection<int, Plan>|null $plans */
        $plans = $this->getFreePlans();

        if (! $plans) {
            return;
        }

        foreach ($plans as $plan) {
            $project->subscribeTo($plan, CarbonImmutable::now());
        }
    }
}
