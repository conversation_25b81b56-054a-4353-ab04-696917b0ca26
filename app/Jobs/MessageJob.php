<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Facades\EncodingService;
use App\Facades\PhoneProvider;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Provider;
use App\Models\Sender;
use App\Services\Jasmin\Exceptions\JasminClientException;
use App\Services\Jasmin\JasminClient;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Throwable;

class MessageJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(private readonly MessageReceipt $messageReceipt, private readonly JasminClient $jasmin) {}

    /**
     * Execute the job.
     *
     * @throws JasminClientException
     */
    public function handle(): void
    {
        $provider = PhoneProvider::detectPhoneNumberProvider($this->messageReceipt->number);

        if ($provider === 'Unknown') {
            $this->fail('Unknown provider');
        }

        /** @var Message $message */
        $message = $this->messageReceipt->message;

        if (EncodingService::isGSM7Message($message->short_message)) {
            $content = $message->short_message;
            $coding = 0;
        } else {
            $content = EncodingService::encodeToUCS2($message->short_message);
            $coding = 8;
        }

        if ($message->message_type === 'flash') {
            $coding += 16;
        }

        /** @var Sender $sender */
        $sender = $message->sender;

        // TODO: fix, we query the provider in detectPhoneNumberProvider
        $providerModel = Provider::where('name', $provider)->firstOrFail();
        $number = PhoneProvider::prepareForSMSC($providerModel, $this->messageReceipt->number);

        try {
            /** @var string $dlrUrl */
            $dlrUrl = Config::get('app.url');
            $dlrUrl .= '/api/delivery/'.$this->messageReceipt->id;

            $messageSent = $this->jasmin->message()
                ->from($sender->sender)
                ->to($number)
                ->content($content)
                ->coding($coding)
                ->via('http')
                ->tags($provider)
                ->dlrCallbackUrl($dlrUrl)
                ->send();

            if ($messageSent->isSent()) {
                $this->messageReceipt->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                    'smpp_message_id' => $messageSent->messageId,
                ]);
            } else {

                $this->messageReceipt->update([
                    'status' => 'failed',
                ]);
                $this->fail('Failed to send message');
            }
        } catch (Exception $e) {

            // Handle timeout (JasminClientException) - always retry
            if ($e instanceof JasminClientException) {
                Log::error('Timeout sending message to Jasmin');
                $this->messageReceipt->update([
                    'status' => 'pending',
                ]);
                // For timeouts, we don't check attempts - always retry
                throw $e;
            }

            $this->messageReceipt->update([
                'status' => 'failed',
            ]);
            $this->fail('Failed to send message');
        }
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public function backoff(): array
    {
        return [1, 5, 10];
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception = null): void
    {
        // Ensure the message receipt is marked as failed
        $this->messageReceipt->update([
            'status' => 'failed',
        ]);

        // Log the failure
        Log::error("Message job failed permanently for receipt ID: {$this->messageReceipt->id}", [
            'exception' => $exception instanceof Throwable ? $exception->getMessage() : 'Unknown error',
            'receipt_id' => $this->messageReceipt->id,
            'number' => $this->messageReceipt->number,
        ]);
    }

    public function getMessageReceipt(): MessageReceipt
    {
        return $this->messageReceipt;
    }

    public function getJasmin(): JasminClient
    {
        return $this->jasmin;
    }
}
