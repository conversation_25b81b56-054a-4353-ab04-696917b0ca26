<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Message;
use App\Services\Jasmin\JasminClient;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class MessageJobCreator implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly Message $message,
        private readonly JasminClient $jasmin
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach ($this->message->messages as $message) {
            MessageJob::dispatch($message, $this->jasmin);
        }
    }
}
