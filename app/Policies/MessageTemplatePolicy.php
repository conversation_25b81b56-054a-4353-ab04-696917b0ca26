<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

final class MessageTemplatePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->can('view_any_message::template');
    }

    public function view(User $user): bool
    {
        return $user->can('view_message::template');
    }

    public function create(User $user): bool
    {
        return $user->can('create_message::template');
    }

    public function update(User $user): bool
    {
        return $user->can('update_message::template');
    }

    public function delete(User $user): bool
    {
        return $user->can('delete_message::template');
    }

    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_message::template');
    }
}
