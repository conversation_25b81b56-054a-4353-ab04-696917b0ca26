<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\CampaignMessage;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Auth\Access\HandlesAuthorization;

final class CampaignMessagePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_campaign::message');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user): bool
    {
        return $user->can('view_campaign::message');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_campaign::message');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CampaignMessage $campaignMessage): bool
    {
        if (
            Filament::getTenant() !== null &&
            $campaignMessage->status !== 'pending'
        ) {
            return false;
        }

        return $user->can('update_campaign::message');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CampaignMessage $campaignMessage): bool
    {
        if (
            Filament::getTenant() !== null &&
            $campaignMessage->status !== 'pending'
        ) {
            return false;
        }

        return $user->can('delete_campaign::message');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_campaign::message');
    }
}
