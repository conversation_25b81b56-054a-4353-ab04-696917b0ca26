<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\RegistrationRequest;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RegistrationRequestPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_registration_request');
    }

    /**
     * Determine whether the user can view the model.
     */

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, RegistrationRequest $registrationRequest): bool
    {
        if ($registrationRequest->status !== 'approved') {
            return $user->can('update_registration_request');
        }

        return false;
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_registration_request');
    }
}
