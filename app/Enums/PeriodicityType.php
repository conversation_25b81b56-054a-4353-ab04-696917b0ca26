<?php

declare(strict_types=1);

namespace App\Enums;

use Carbon\CarbonImmutable;
use Closure;
use InvalidArgumentException;

enum PeriodicityType: string
{
    case Yearly = 'Yearly';

    case Monthly = 'Monthly';

    case Quarterly = 'Quarterly';

    case Weekly = 'Weekly';

    case Daily = 'Daily';

    /**
     * Get the expiration date based on periodicity type.
     *
     * @return Closure(): CarbonImmutable
     */
    public static function getExpirationDateModifier(
        string $type,
        CarbonImmutable $startDate,
        int $quantity,
    ): Closure {
        return match ($type) {
            self::Yearly->value => fn () => $startDate->addYears($quantity),
            self::Monthly->value => fn () => $startDate->addMonths($quantity),
            self::Quarterly->value => fn () => $startDate->addMonths(3 * $quantity),
            self::Weekly->value => fn () => $startDate->addWeeks($quantity),
            self::Daily->value => fn () => $startDate->addDays($quantity),
            default => throw new InvalidArgumentException('Invalid periodicity type.'),
        };
    }

    /**
     * @return array<string>
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }
}
