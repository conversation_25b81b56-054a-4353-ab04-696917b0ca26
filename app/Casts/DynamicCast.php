<?php

declare(strict_types=1);

namespace App\Casts;

use App\Models\Setting;
use Illuminate\Database\Eloquent\Model;

class DynamicCast
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        /** @var Setting $model */
        if ($model->key === 'free_plan') {
            /** @var string $value */
            return json_decode($value, true);
        }

        if ($model->key === 'is_free_plan') {
            return (bool) $value;
        }

        return $value ?? null;
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        /** @var Setting $model */
        if ($model->key === 'free_plan') {
            return json_encode($value);
        }

        if ($model->key === 'is_free_plan') {
            /** @var bool $value */
            return (int) $value;
        }

        return $value ?? null;
    }
}
