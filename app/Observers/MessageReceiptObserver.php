<?php

declare(strict_types=1);

namespace App\Observers;

// use App\Jobs\QueuingMessage;
// use App\Models\MessageReceipt;
// use App\Facades\PhoneProvider;

final readonly class MessageReceiptObserver
{
    //    public function __construct(private PhoneProvider $phoneProvider) {}
    //
    //    public function created(MessageReceipt $message): void
    //    {
    //        $queue = null;
    //
    //        if ($this->phoneProvider->detectPhoneNumberProvider($message->number) === 'Almadar') {
    //            $queue = 'almadar';
    //        } elseif ($this->phoneProvider->detectPhoneNumberProvider($message->number) === 'Libyana') {
    //            $queue = 'libyana';
    //        }
    //
    //        QueuingMessage::dispatch($message->toJson())->onQueue($queue);
    //
    //    }
}
