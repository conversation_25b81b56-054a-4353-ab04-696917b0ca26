<?php

declare(strict_types=1);

use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Project;
use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    $this->setPreserveGlobalState(false);
    $this->setRunTestInSeparateProcess(true);
    // Use Laravel's built-in mock for direct mocking of JasminClient
    $this->mock = Mockery::mock('App\Services\Jasmin\JasminClient')->makePartial();
    app()->instance(App\Services\Jasmin\JasminClient::class, $this->mock);
});

// Make sure to clean up after each test
afterEach(function () {
    Mockery::close();
});

test('it handles DELIVRD status correctly', function () {
    // Create a project for valid references
    $project = Project::factory(['status' => 'active'])->create();

    // Create a message for our message receipt
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);

    // Create a message receipt
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
        'smpp_message_id' => null,
        'delivered_at' => null,
    ]);

    // Mock the JasminClient::receiveDlrCallback method directly
    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            // Create a delivery callback object
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'DELIVRD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',  // April 10, 2023 12:00
                doneDate: '2304101210',       // April 10, 2023 12:10
                submittedCount: 1,
                deliveredCount: 1,
                error: 0,
                text: 'Delivered'
            );

            // Run the callback function
            $callback($dlr);

            // Return a mock response
            return new Response('ACK/Jasmin', 200, ['Content-Type' => 'text/plain']);
        });

    // Create a request with delivery data
    $request = new Request();

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    // Assert the response is correct
    expect($response->getStatusCode())->toBe(200);
    expect($response->getContent())->toBe('ACK/Jasmin');

    // Refresh the message receipt from the database
    $messageReceipt->refresh();

    // Assert the message receipt was updated correctly
    expect($messageReceipt->status)->toBe('delivered');
    expect($messageReceipt->smpp_message_id)->toBe('smsc-123456');
    expect($messageReceipt->delivered_at)->toBeInstanceOf(CarbonImmutable::class);
    expect($messageReceipt->delivered_at->format('y-m-d H:i'))->toBe('23-04-10 12:10');
});

test('it handles EXPIRED status correctly', function () {
    // Create required data
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    // Mock the JasminClient::receiveDlrCallback method directly
    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'EXPIRED',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Expired'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200, ['Content-Type' => 'text/plain']);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    expect($response->getStatusCode())->toBe(200);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('expired');
    expect($messageReceipt->smpp_message_id)->toBe('smsc-123456');
    expect($messageReceipt->delivered_at)->toBeInstanceOf(CarbonImmutable::class);
});

test('it handles DELETED status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'DELETED',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Deleted'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    expect($response->getStatusCode())->toBe(200);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('deleted');
    expect($messageReceipt->smpp_message_id)->toBe('smsc-123456');
});

test('it handles UNDELIV status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'UNDELIV',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Undeliverable'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('undeliverable');
});

test('it handles ACCEPTD status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'ACCEPTD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Accepted'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('accepted');
});

test('it handles UNKNOWN status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'UNKNOWN',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Unknown'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('unknown');
});

test('it handles REJECTD status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'REJECTD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Rejected'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('rejected');
});

test('it handles default (failed) status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'SOME_UNKNOWN_STATUS',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Failed'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('failed');
});

test('it handles empty doneDate correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'DELIVRD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '',  // Empty doneDate
                submittedCount: 1,
                deliveredCount: 1,
                error: 0,
                text: 'Delivered'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('delivered');
    expect($messageReceipt->delivered_at)->toBeNull();
});

test('it handles null doneDate correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'DELIVRD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: null,  // Null doneDate
                submittedCount: 1,
                deliveredCount: 1,
                error: 0,
                text: 'Delivered'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('delivered');
    expect($messageReceipt->delivered_at)->toBeNull();
});

test('it handles zero doneDate correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'DELIVRD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '0',  // Zero doneDate
                submittedCount: 1,
                deliveredCount: 1,
                error: 0,
                text: 'Delivered'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('delivered');
    expect($messageReceipt->delivered_at)->toBeNull();
});

test('it throws an exception when message receipt not found', function () {
    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                // dummy data
                id: 'test-id',
                messageStatus: 'DELIVRD',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '0',  // Zero doneDate
                submittedCount: 1,
                deliveredCount: 1,
                error: 0,
                text: 'Delivered'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/123', [
        'id' => '123',
        'message_status' => 'DELIVRD',
        'level' => 1,
        'connector' => 'jasmin-connector',
        'id_smsc' => '123456',
        'subdate' => '2304101200',
        'donedate' => '2304101210',
        'sub' => 1,
        'dlvrd' => 1,
        'err' => 0,
        'text' => 'Delivered',
    ], ['Content-Type' => 'application/json', 'Accept' => 'application/json']);

    expect($response->getStatusCode())->toBe(404);
});

test('it handles ESME_ROK status correctly', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $message = Message::factory()->create([
        'project_id' => $project->id,
        'message_type' => 'sms',
    ]);
    $messageReceipt = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $this->mock->shouldReceive('receiveDlrCallback')
        ->once()
        ->andReturnUsing(function (Request $request, callable $callback) {
            $dlr = new DeliveryCallback(
                id: 'test-id',
                messageStatus: 'ESME_ROK',
                level: 1,
                connector: 'jasmin-connector',
                smscId: 'smsc-123456',
                submittedDate: '2304101200',
                doneDate: '2304101210',
                submittedCount: 1,
                deliveredCount: 0,
                error: 0,
                text: 'Rejected'
            );

            $callback($dlr);

            return new Response('ACK/Jasmin', 200);
        });

    $response = $this->post('/api/delivery/'.$messageReceipt->id, [], ['Content-Type' => 'application/json']);

    $messageReceipt->refresh();
    expect($messageReceipt->status)->toBe('sent');
});
