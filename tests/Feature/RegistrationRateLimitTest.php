<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Cache;

use function Pest\Laravel\seed;

beforeEach(function () {
    // Clear cache before each test
    Cache::flush();

    // Set the frontend URL for the RestrictFrontendAccessMiddleware
    config(['app.frontend_url' => 'http://localhost']);

    seed();
});

it('applies rate limiting to registration endpoint', function () {
    // First request should succeed
    $response = $this->postJson('http://localhost/api/registration-request', [
        'phone' => '00218911253207',
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    // The request might fail due to validation or other business logic,
    // but it should not be blocked by rate limiting (status should not be 429)
    expect($response->status())->not->toBe(429);
});

it('applies rate limiting to resend endpoint', function () {
    // First request should not be rate limited
    $response = $this->postJson('http://localhost/api/registration-request/resend', [
        'phone' => '00218911253207',
    ]);

    // The request might fail due to validation or other business logic,
    // but it should not be blocked by rate limiting (status should not be 429)
    expect($response->status())->not->toBe(429);
});

it('blocks requests when rate limit is exceeded', function () {
    // Set cache to simulate rate limit exceeded
    Cache::put('registration_rate_limit_ip:127.0.0.1', 20, 3600);

    $response = $this->postJson('http://localhost/api/registration-request', [
        'phone' => '00218911253207',
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    expect($response->status())->toBe(429)
        ->and($response->json('message'))
        ->toBe('Too many registration requests from this IP. Please try again later.');
});

it('blocks requests when phone rate limit is exceeded', function () {
    // Set cache to simulate phone rate limit exceeded
    Cache::put('registration_rate_limit_phone:00218911253207', 5, 3600);

    $response = $this->postJson('http://localhost/api/registration-request', [
        'phone' => '00218911253207',
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    expect($response->status())->toBe(429)
        ->and($response->json('message'))
        ->toBe('Too many registration requests for this phone number. Please try again later.');
});

it('returns error when phone is missing', function () {
    $response = $this->postJson('http://localhost/api/registration-request', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    expect($response->status())->toBe(422)
        ->and($response->json('message'))
        ->toBe('The phone field is required.');
});

it('verify endpoint is not rate limited', function () {
    // The verify endpoint should not have rate limiting applied
    $response = $this->postJson('http://localhost/api/registration-request/verify', [
        'phone' => '00218911253207',
        'otp' => '123456',
    ]);

    // Should not be blocked by rate limiting
    expect($response->status())->not->toBe(429);
});
