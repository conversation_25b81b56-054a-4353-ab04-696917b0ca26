<?php

declare(strict_types=1);

use App\Http\Resources\PlanResource;
use App\Models\Feature;
use App\Models\FeaturePlan;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Illuminate\Testing\Fluent\AssertableJson;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('returns a list of active plans', function () {
    Plan::truncate();

    // Arrange: Create active and inactive plans
    $activePlan = Plan::factory()->create(['status' => 'active']);
    Plan::factory()->create(['status' => 'inactive']);

    // We need to mock the middleware for testing
    $this->withoutMiddleware(App\Http\Middleware\RestrictFrontendAccessMiddleware::class);

    // Act: Make a GET request to the endpoint
    $response = $this->getJson('/api/plans');

    // Assert: Check the response
    $response->assertStatus(200)
        ->assertJson(fn (AssertableJson $json) => $json->has('data', 1)
            ->has('data.0', fn (AssertableJson $json) => $json->where('name', $activePlan->name)
                ->has('duration')
                ->has('quantity')
                ->has('price')
                ->has('price_per_message')
                ->has('type')
                ->etc()
            )
        );
});

it('returns unauthorized when accessing from a restricted host', function () {
    Plan::truncate();

    // Arrange: Create active and inactive plans
    Plan::factory()->create(['status' => 'active']);
    Plan::factory()->create(['status' => 'inactive']);

    // Configure the frontend URL in the config
    config(['app.frontend_url' => 'http://frontend.test']);

    // Force a different URL to simulate access from a different domain
    URL::forceRootUrl('http://subdomain.domain.test');

    // Act: Make a GET request to the endpoint
    $response = $this->getJson('/api/plans');

    // Assert: Check the response
    $response->assertStatus(401)
        ->assertJson(['message' => 'Unauthorized']);
});

it('returns an empty array when resource is null', function () {
    $resource = new PlanResource(null);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toBeArray()->toBeEmpty();
});

it('transforms the resource into an array with correct structure', function () {
    $plan = Plan::factory()->make([
        'name' => 'Test Plan',
        'periodicity_type' => 'Monthly',
        'periodicity_quantity' => 1,
        'price' => 100.0,
    ]);

    $resource = new PlanResource($plan);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toBeArray()
        ->toHaveKeys(['name', 'duration', 'quantity', 'price', 'price_per_message', 'type'])
        ->name->toBe('Test Plan')
        ->duration->toBe(1.0) // monthly with quantity 1
        ->price->toBe(100.0);
});

it('calculates duration correctly based on periodicity type', function () {
    // Test different periodicity types
    $periodicityTypes = [
        'Monthly' => (float) 2,     // 2 months
        'Quarterly' => (float) 6,   // 2 quarters = 6 months
        'Yearly' => (float) 24,     // 2 years = 24 months
        'Weekly' => 0.5,      // 2 weeks = 0.5 months (rounded to 0)
        'Daily' => 0.07,       // 2 days = 0.06 months (rounded to 0)
    ];

    foreach ($periodicityTypes as $type => $expectedDuration) {
        $plan = Plan::factory()->make([
            'periodicity_type' => $type,
            'periodicity_quantity' => 2, // Using 2 for all to test multiplication
        ]);

        $resource = new PlanResource($plan);
        $request = Request::create('/');
        $result = $resource->toArray($request);

        expect($result['duration'])->toBe($expectedDuration, "Failed for type: {$type}");
    }
});

it('handles plans with features correctly', function () {
    // Create a plan with a feature
    $plan = Plan::factory()->create([
        'name' => 'Feature Plan',
        'price' => 1000.0,
    ]);

    $feature = Feature::factory()->create(['name' => 'SMS']);

    // Create the feature plan relationship with charges
    $featurePlan = FeaturePlan::create([
        'plan_id' => $plan->id,
        'feature_id' => $feature->id,
        'charges' => 500, // 500 messages
    ]);

    // Reload the plan with its relationships
    $plan = Plan::with(['features.feature'])->find($plan->id);

    $resource = new PlanResource($plan);
    $request = Request::create('/');
    $result = $resource->toArray($request);

    // Verify the feature data is correctly included
    expect($result)->toBeArray()
        ->toHaveKeys(['name', 'duration', 'quantity', 'price', 'price_per_message', 'type'])
        ->quantity->toBe(500) // Should match the charges
        ->type->toBe('SMS') // Should match the feature name
        ->price_per_message->toBe(2.0); // 1000 / 500 = 2.0
});

it('calculates price_per_message as 0 when charges are 0', function () {
    // Create a plan with a feature that has 0 charges
    $plan = Plan::factory()->create([
        'name' => 'Zero Charge Plan',
        'price' => 1000.0,
    ]);

    $feature = Feature::factory()->create(['name' => 'SMS']);

    // Create the feature plan relationship with 0 charges
    $featurePlan = FeaturePlan::create([
        'plan_id' => $plan->id,
        'feature_id' => $feature->id,
        'charges' => 0, // 0 messages
    ]);

    // Reload the plan with its relationships
    $plan = Plan::with(['features.feature'])->find($plan->id);

    $resource = new PlanResource($plan);
    $request = Request::create('/');
    $result = $resource->toArray($request);

    // Verify price_per_message is 0 when charges are 0
    expect($result)->toBeArray()
        ->price_per_message->toBe(0.0);
});

it('handles plans without features correctly', function () {
    // Create a plan without any features
    $plan = Plan::factory()->create([
        'name' => 'No Feature Plan',
        'price' => 1000.0,
    ]);

    // Ensure no features are attached
    $plan = Plan::with(['features.feature'])->find($plan->id);

    $resource = new PlanResource($plan);
    $request = Request::create('/');
    $result = $resource->toArray($request);

    // Verify default values are used when no features exist
    expect($result)->toBeArray()
        ->quantity->toBe(0) // Default charges
        ->type->toBeNull() // No feature name
        ->price_per_message->toBe(0.0); // Default price per message
});
