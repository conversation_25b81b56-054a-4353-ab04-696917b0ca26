<?php

declare(strict_types=1);

use App\Http\Middleware\JasminHttpCall;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

test('middleware allows request from allowed IP address', function () {
    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Create a request with an allowed IP
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');

    // Create a response closure that we'll pass to the middleware
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is allowed (not blocked)
    expect($response->getStatusCode())->toBe(200);
    expect($response->getContent())->toBe('OK');
});

test('middleware blocks request from unknown IP address', function () {
    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Mock the Log facade to verify it gets called
    Log::shouldReceive('info')
        ->once()
        ->with('Invalid IP address', ['ip' => '***********']);

    // Create a request with a non-allowed IP
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');

    // Create a response closure that should not be called
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is blocked
    expect($response->getStatusCode())->toBe(400);
    expect($response->getContent())->toBe('Invalid IP address');
});

test('middleware uses CF-Connecting-IP header in production', function () {
    // Mock production environment
    App::shouldReceive('isProduction')
        ->once()
        ->andReturn(true);

    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Create a request with allowed IP in CF header but different remote IP
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********'); // This would normally be blocked
    $request->headers->set('CF-Connecting-IP', '***********'); // But this is allowed

    // Create a response closure that we'll pass to the middleware
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is allowed based on the CF header
    expect($response->getStatusCode())->toBe(200);
    expect($response->getContent())->toBe('OK');
});

test('middleware blocks request with invalid CF-Connecting-IP header in production', function () {
    // Mock production environment
    App::shouldReceive('isProduction')
        ->once()
        ->andReturn(true);

    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Mock the Log facade to verify it gets called
    Log::shouldReceive('info')
        ->once()
        ->with('Invalid IP address', ['ip' => '***********']);

    // Create a request with disallowed IP in CF header
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********'); // Not relevant in production
    $request->headers->set('CF-Connecting-IP', '***********'); // This is not allowed

    // Create a response closure that should not be called
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is blocked
    expect($response->getStatusCode())->toBe(400);
    expect($response->getContent())->toBe('Invalid IP address');
});

test('middleware uses request IP in non-production', function () {
    // Mock non-production environment
    App::shouldReceive('isProduction')
        ->once()
        ->andReturn(false);

    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Create a request with allowed remote IP but different CF header
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '********'); // This is allowed
    $request->headers->set('CF-Connecting-IP', '***********'); // This would be blocked in production

    // Create a response closure that we'll pass to the middleware
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is allowed based on the remote IP
    expect($response->getStatusCode())->toBe(200);
    expect($response->getContent())->toBe('OK');
});

test('middleware handles empty config IP list', function () {
    // Set up config with empty IP list
    Config::set('jasmin_client.ips', []);

    // Mock the Log facade to verify it gets called
    Log::shouldReceive('info')
        ->once()
        ->with('Invalid IP address', ['ip' => '***********']);

    // Create a request with any IP
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');

    // Create a response closure that should not be called
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is blocked (no IPs are allowed)
    expect($response->getStatusCode())->toBe(400);
    expect($response->getContent())->toBe('Invalid IP address');
});

test('middleware handles missing CF-Connecting-IP header in production', function () {
    // Mock production environment
    App::shouldReceive('isProduction')
        ->once()
        ->andReturn(true);

    // Set up config for test
    Config::set('jasmin_client.ips', ['***********', '********']);

    // Mock the Log facade to verify it gets called - null IP will be logged
    Log::shouldReceive('info')
        ->once()
        ->with('Invalid IP address', ['ip' => null]);

    // Create a request with no CF header
    $request = Request::create('/test-url', 'GET');
    $request->server->set('REMOTE_ADDR', '***********'); // This would be allowed in non-prod
    // No CF-Connecting-IP header set

    // Create a response closure that should not be called
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new JasminHttpCall();
    $response = $middleware->handle($request, $next);

    // Assert that the response is blocked
    expect($response->getStatusCode())->toBe(400);
    expect($response->getContent())->toBe('Invalid IP address');
});
