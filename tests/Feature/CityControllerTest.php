<?php

declare(strict_types=1);

use App\Http\Resources\CityResource;
use App\Models\City;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\getJson;
use function Pest\Laravel\seed;

uses(RefreshDatabase::class);

beforeEach(function () {
    seed();
});

it('returns all cities', function () {
    City::factory()->count(5)->create();

    $response = getJson('/api/cities');

    $response->assertStatus(200);
    $response->assertJsonStructure(['data']);
    $response->assertJsonCount(5, 'data');
});

it('returns empty array when no cities exist', function () {
    $response = getJson('/api/cities');

    $response->assertStatus(200);
    $response->assertJson(['data' => []]);
});

it('returns cities with correct structure', function () {
    $city = City::factory()->create([
        'name' => 'Test City',
        'description' => 'Test Description',
    ]);

    $response = getJson('/api/cities');

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'data' => [
            '*' => [
                'name',
                'description',
            ],
        ],
    ]);

    $response->assertJsonFragment([
        'name' => 'Test City',
        'description' => 'Test Description',
    ]);
});

it('returns multiple cities in correct format', function () {
    $cities = City::factory()->count(3)->create();

    $response = getJson('/api/cities');

    $response->assertStatus(200);
    $response->assertJsonCount(3, 'data');

    foreach ($cities as $city) {
        $response->assertJsonFragment([
            'name' => $city->name,
            'description' => $city->description,
        ]);
    }
});

it('uses CityResource for transformation', function () {
    $city = City::factory()->create();

    $response = getJson('/api/cities');

    $resource = new CityResource($city);
    $expectedData = $resource->toArray(request());

    $response->assertStatus(200);
    $response->assertJsonFragment($expectedData);
});
