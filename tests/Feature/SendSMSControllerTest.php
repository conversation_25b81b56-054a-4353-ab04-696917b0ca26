<?php

declare(strict_types=1);

use App\Enums\FeatureConsumptionType;
use App\Http\Controllers\SMSController;
use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\IpWhitelist;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Project;
use Illuminate\Support\Carbon;
use Illuminate\Testing\Fluent\AssertableJson;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

test('send sms using wallet', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms when company have auto approve', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)->create();

    $project->company->update(['auto_approve' => true]);

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms using subscription', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms company not found', function () {
    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
    );

    $response->assertStatus(401);
});

test('send sms using wallet with no balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Insufficient balance']);
});

test('send sms no sender', function () {
    $project = Project::factory(['status' => 'active'])
        ->withBalance(100_000)
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
    $response->assertJson(['message' => 'Sender not found']);
});

test('send sms receiver not from the same provider', function () {
    $project = Project::factory(['status' => 'active'])
        ->withBalance(100_000)
        ->withSenderId('Lamah', ['libyana'])
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'The receiver is not from the same provider']);
});

test('send bulk sms using wallet', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)
        ->create();

    $project->company->update(['auto_approve' => true]);

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/bulk',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receivers' => [
                '00218911234567',
                '00218911234568',
                '00218911234569',
                '00218911234570',
                '00218911234571',
                '00218911234572',
                '00218911234573',
                '00218911234574',
                '00218911234575',
                '00218911234576',
                '00218911234577',
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send bulk sms using wallet with no balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/bulk',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receivers' => [
                '00218911234567',
                '00218911234568',
                '00218911234569',
                '00218911234570',
                '00218911234571',
                '00218911234572',
                '00218911234573',
                '00218911234574',
                '00218911234575',
                '00218911234576',
                '00218911234577',
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Insufficient balance']);
});

test('send bulk sms using wallet with no sender', function () {
    $project = Project::factory(['status' => 'active'])
        ->withBalance(100_000)
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/bulk',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receivers' => [
                '00218911234567',
                '00218911234568',
                '00218911234569',
                '00218911234570',
                '00218911234571',
                '00218911234572',
                '00218911234573',
                '00218911234574',
                '00218911234575',
                '00218911234576',
                '00218911234577',
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
    $response->assertJson(['message' => 'Sender not found']);
});

test('send bulk sms using wallet with receiver not from the same provider', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah', ['libyana'])
        ->withBalance(100_000)
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/bulk',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receivers' => [
                '00218911234567',
                '00218911234568',
                '00218911234569',
                '00218911234570',
                '00218911234571',
                '00218911234572',
                '00218911234573',
                '00218911234574',
                '00218911234575',
                '00218911234576',
                '00218911234577',
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'The sender and receivers are not compatible.']);
});

test('send bulk sms using subscription', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/bulk',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receivers' => [
                '00218911234567',
                '00218911234568',
                '00218911234569',
                '00218911234570',
                '00218911234571',
                '00218911234572',
                '00218911234573',
                '00218911234574',
                '00218911234575',
                '00218911234576',
                '00218911234577',
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms using wallet with contact group', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms using subscription with contact group', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send sms using subscription with contact group not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $projectNew = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    $contact_group = $projectNew->contact_groups()->firstOrFail();

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Contact group not found']);
});

test('sender not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withContactGroup()
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $company = $project->company;
    assert($company instanceof Company);

    $company->senders()->detach();

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
});

test('sender provider not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah', ['libyana'])
        ->withContactGroup(['almadar'])
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
});

test('no wallet balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'contact_group_id' => ContactGroup::firstOrFail()->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
});

test('no subscription balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withContactGroup()
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $project->consume(
        FeatureConsumptionType::SMS->value,
        500,
        FeatureConsumptionType::SMS->value,
    );

    $response = postJson(
        '/api/sms/messages/contacts',
        [
            'message' => 'Hello World',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'contact_group_id' => $contact_group->id,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
});

test('retrieves paginated messages', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create test messages
    Message::factory()->count(15)->create([
        'message_type' => 'sms',
        'project_id' => $project->id,
        'created_at' => Carbon::now()->subDays(1),
    ]);

    $response = getJson('/api/sms/messages?page=1&per_page=10',
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ]);

    $response->assertStatus(200)
        ->assertJson(fn (AssertableJson $json) => $json->has('data.data', 10) // Ensure 10 messages are returned
            ->has('data')
            ->etc()
        );
});

test('filters messages by date range', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create test messages
    Message::factory()->create([
        'message_type' => 'sms',
        'project_id' => $project->id,
        'created_at' => Carbon::now()->subDays(5),
    ]);

    Message::factory()->create([
        'message_type' => 'sms',
        'project_id' => $project->id,
        'created_at' => Carbon::now()->subDays(2),
    ]);

    $response = getJson(
        '/api/sms/messages?from='.Carbon::now()->subDays(3)->toDateString().
        '&to='.Carbon::now()->subDays(1)->toDateString(),
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ]);

    $response->assertStatus(200)
        ->assertJson(fn (AssertableJson $json) => $json->has('data.data', 1) // Ensure only 1 message is returned
            ->has('data')
            ->etc()
        );
});

it('returns a message when a valid message_id is provided', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $message = Message::factory()->create([
        'message_type' => 'sms',
        'project_id' => $project->id,
        'company_id' => $project->company_id,
    ]);

    $receipts = MessageReceipt::factory()->count(5)->create([
        'message_id' => $message->id,
    ]);

    $response = getJson('/api/sms/messages/'.$message->id, [
        'Authorization' => 'Bearer '.$project->token,
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'REMOTE_ADDR' => '***********',
    ]);

    $response->assertStatus(200)
        ->assertJson([
            [
                'short_message' => $message->short_message,
                'message_type' => $message->message_type,
                'send_type' => $message->send_type,
                'message_consumption' => $message->message_consumption,
                'receiver' => $receipts->map(fn (MessageReceipt $receipt) => [
                    'number' => $receipt->number,
                    'status' => $receipt->status,
                ])->toArray(),
            ],
        ]);
});

it('returns 404 when the message is not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $message = Message::factory()->create([
        'message_type' => 'sms',
        'project_id' => $project->id,
    ]);

    $response = getJson('/api/sms/messages/'.$message->id, [
        'Authorization' => 'Bearer '.$project->token,
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'REMOTE_ADDR' => '***********',
    ]);

    // Assert: Check the response
    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Message not found',
        ]);
});

test('throws exception when wallet balance is insufficient', function () {
    $company = Company::factory()->create();
    $project = Project::factory()->create([
        'company_id' => $company->id,
        'limit' => 100,
    ]);

    $receivers = collect(['1234567890', '0987654321']);

    $controller = new SMSController(
        new App\Services\SenderHelper()
    );

    // Act & Assert
    expect(fn () => $controller->checkBalance(
        parts: 2,
        receivers: $receivers,
        company: $company,
        type: 'wallet',
        project: $project
    ))->toThrow(Exception::class, 'Insufficient balance');
});

test('throws exception when project limit is exceeded', function () {
    $company = Company::factory()->create();
    $project = Project::factory([
        'status' => 'active',
        'limit' => 1,
        'type' => 'budget',
    ])->withSenderId('Lamah')
        ->withContactGroup()
        ->withbalance(1000_000)
        ->create();

    $receivers = collect(
        [
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
        ]);

    $controller = new SMSController(
        new App\Services\SenderHelper()
    );

    // Act & Assert
    expect(fn () => $controller->checkBalance(
        parts: 2,
        receivers: $receivers,
        company: $company,
        type: 'wallet',
        project: $project
    ))->toThrow(Exception::class, 'project limit exceeded');
});

test('throws exception when monthly project transactions exceed limit', function () {
    $company = Company::factory()->create();
    $project = Project::factory([
        'status' => 'active',
        'limit' => 10,
        'type' => 'budget',
    ])->withSenderId('Lamah')
        ->withContactGroup()
        ->withbalance(10_000)
        ->create();

    // Create past transactions that almost reach the limit
    $project->transactions()->create([
        'company_id' => $company->id,
        'amount' => -99999,
        'action_type' => 'charge',
        'status' => 'completed',
        'created_at' => now()->subDays(15),
    ]);

    $receivers = collect(
        [
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
            '00218911234567',
        ]);

    $controller = new SMSController(
        new App\Services\SenderHelper()
    );

    // Act & Assert
    expect(fn () => $controller->checkBalance(
        parts: 2,
        receivers: $receivers,
        company: $company,
        type: 'wallet',
        project: $project
    ))->toThrow(Exception::class, 'Insufficient balance for the project');
});

it('send sms using template', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200)->assertJsonStructure([
        'message_id',
        'cost',
    ]);
});

it('returns template not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => 'template_id',
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    'name' => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404)->assertJson(['message' => 'Template not found']);
});

it('returns template is not active', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'inactive',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );
    $response->assertStatus(404)->assertJson(['message' => 'Template not found']);
});

it('parameter missing', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    'name' => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400)->assertJson(['message' => 'Missing parameter: '.$template->parameters->first()->name]);
});

it('sender not fund in template', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah2',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404)->assertJson(['message' => 'Sender not found']);
});

it('sender provider not found in template', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah', ['libyana'])
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => 'name',
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400)->assertJson(['message' => 'The receiver is not from the same provider']);
});

it('max limit of parameter exceeded', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => str_repeat('a', 200),
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400)->assertJson(['message' => 'Parameter "'.$template->parameters->first()->name.'" exceeds max limit of '.$template->parameters->first()->max_limit]);
});

it('max limit of message exceeded', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('SMS Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $template = App\Models\MessageTemplate::factory()->withParameters(1)->create([
        'project_id' => $project->id,
        'company_id' => $project->company_id,
        'content' => 'Hello {name}',
        'status' => 'active',
    ]);

    $response = postJson(
        '/api/sms/messages/template',
        [
            'template_id' => $template->short_name,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
            'params' => [
                [
                    $template->parameters->first()->name => str_repeat('a', 1),
                ],
            ],
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400)->assertJson(['message' => 'Insufficient balance']);
});
