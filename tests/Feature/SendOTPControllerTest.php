<?php

declare(strict_types=1);

use App\Models\Company;
use App\Models\IpWhitelist;
use App\Models\OneTimePassword;
use App\Models\Project;
use App\Models\Setting;

use function Pest\Laravel\postJson;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

test('send otp using wallet', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('send otp using subscription', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('no wallet balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $company = $project->company;
    assert($company instanceof Company);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
});

test('no subscription balance', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $project->consume('OTP', 200, 'OTP');

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
});

test('too many OTP requests', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // send 10 OTP requests
    for ($i = 0; $i < 10; $i++) {
        postJson(
            '/api/otp/initiate',
            [
                'lang' => 'ar',
                'length' => 4,
                'expiration' => 1,
                'sender' => 'Lamah',
                'message_type' => 'sms',
                'payment_type' => 'wallet',
                'receiver' => '00218911234567',
            ],
            [
                'Authorization' => 'Bearer '.$project->token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'REMOTE_ADDR' => '***********',
            ],
        );
    }

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(429);
});

test('verify otp', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['request_id', 'cost']);
    /** @var array{request_id:string, cost:string} $json */
    $json = $response->json();

    $requestId = $json['request_id'];

    $otp = OneTimePassword::where('id', $requestId)->firstOrFail();

    $response1 = postJson(
        '/api/otp/verify',
        [
            'request_id' => $requestId,
            'code' => $otp->code,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response1->assertStatus(200);
});

test('OTP already verified', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['request_id', 'cost']);
    /** @var array{request_id:string, cost:string} $json */
    $json = $response->json();

    $requestId = $json['request_id'];

    $otp = OneTimePassword::where('id', $requestId)->firstOrFail();

    $response1 = postJson(
        '/api/otp/verify',
        [
            'request_id' => $requestId,
            'code' => $otp->code,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response1->assertStatus(200);

    $response2 = postJson(
        '/api/otp/verify',
        [
            'request_id' => $requestId,
            'code' => $otp->code,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response2->assertStatus(400);
});

test('verify otp with wrong code', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['request_id', 'cost']);
    /** @var array{request_id:string, cost:string} $json */
    $json = $response->json();

    $requestId = $json['request_id'];

    $otp = OneTimePassword::where('id', $requestId)->firstOrFail();

    $response1 = postJson(
        '/api/otp/verify',
        [
            'request_id' => $requestId,
            'code' => '1'.$otp->code,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response1->assertStatus(401);
});

test('verify otp with expired otp', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['request_id', 'cost']);
    /** @var array{request_id:string, cost:string} $json */
    $json = $response->json();

    $requestId = $json['request_id'];

    $otp = OneTimePassword::where('id', $requestId)->firstOrFail();

    $otp->update(['created_at' => now()->subDay()]);
    $otp->save();

    $response1 = postJson(
        '/api/otp/verify',
        [
            'request_id' => $requestId,
            'code' => $otp->code,
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response1->assertStatus(401);
});

test('verify otp with otp not found', function () {
    $project = Project::factory(['status' => 'active'])->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/verify',
        [
            'request_id' => Str::uuid(),
            'code' => '1234',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
});

it('returns Sender not found', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah2',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
    $response->assertJson(['message' => 'Sender not found']);
});

it('returns The receiver is not from the same provider', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah', ['libyana'])
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'The receiver is not from the same provider']);
});

it('returns You have already sent an OTP to this number', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'You have already sent an OTP to this number']);
});

it('send flash otp', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'flash',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

it('returns Single SMS cost not found', function () {

    Setting::where('key', 'single_sms_cost')->delete();

    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Single SMS cost not found']);
});

it('returns Insufficient balance for the project', function () {
    $project = Project::factory(['status' => 'active', 'limit' => 0])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Insufficient balance for the project']);
});

it('returns Single SMS cost not found error', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    Setting::where('key', 'single_sms_cost')->update(['value' => 'not a number']);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(400);
    $response->assertJson(['message' => 'Single SMS cost not found']);
});

it('send en otp', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withSubscription('OTP Starter 1')
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'en',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'subscription',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

test('request from non-whitelisted IP address when send otp using wallet', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSenderId('Lamah')
        ->withBalance(100_000)
        ->create();

    $response = postJson(
        '/api/otp/initiate',
        [
            'lang' => 'ar',
            'length' => 4,
            'expiration' => 1,
            'sender' => 'Lamah',
            'message_type' => 'sms',
            'payment_type' => 'wallet',
            'receiver' => '00218911234567',
        ],
        [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
    );

    $response->assertStatus(401);
});
