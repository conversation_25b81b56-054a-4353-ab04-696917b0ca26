<?php

declare(strict_types=1);

use App\Models\ContactGroup;
use App\Models\IpWhitelist;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\getJson;
use function Pest\Laravel\seed;

uses(RefreshDatabase::class);

beforeEach(function () {
    seed();
});

test('get project details', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = getJson(
        '/api/project/details',
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'project_name',
        'description',
        'company',
        'status',
        'type',
        'subscription',
    ]);
});

test('get project balance', function () {
    $project = Project::factory(['status' => 'active'])->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = getJson(
        '/api/project/balance',
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'SMS' => ['balance', 'limit'],
        'OTP' => ['balance', 'limit'],
    ]);
});

test('get project contacts', function () {
    $project = Project::factory(['status' => 'active'])->create();
    /** @var ContactGroup $contact_group */
    $contact_group = ContactGroup::factory()->withContacts()->create();
    $project->contact_groups()->attach($contact_group);

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = getJson(
        '/api/project/contacts/'.$contact_group->id,
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['data']);
});

test('get project consumptions', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $project->subscribeTo(
        App\Models\Plan::where('name', 'like', '%SMS%')->firstOrFail(),
        Carbon\CarbonImmutable::now(),
        1
    );

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    for ($i = 0; $i < 20; $i++) {
        $project->consume('SMS', rand(1, 20), 'SMS');
    }

    $response = getJson(
        '/api/project/consumptions',
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
    $response->assertJsonStructure(['data']);
});

it('returns Contact group not found', function () {
    $project = Project::factory(['status' => 'active'])->create();

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $response = getJson(
        '/api/project/contacts/'.str()->uuid(),
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(404);
    $response->assertJson(['message' => 'Contact group not found']);
});

it('get contact using from - to', function () {
    $project = Project::factory(['status' => 'active'])->withContactGroup()->create();
    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    $contact_group = $project->contact_groups()->firstOrFail();

    $response = getJson(
        '/api/project/contacts/'.$contact_group->id.'?from='.Carbon\CarbonImmutable::now()->subDays(1)->toDateString().
        '&to='.Carbon\CarbonImmutable::now()->toDateString(),
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

it('get consumption using from - to', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSubscription('SMS Starter 1')->create();
    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    for ($i = 0; $i < 20; $i++) {
        $project->consume('SMS', rand(1, 20), 'SMS');
    }

    $response = getJson(
        '/api/project/consumptions?from='.Carbon\CarbonImmutable::now()->subDays(1)->toDateString().
        '&to='.Carbon\CarbonImmutable::now()->toDateString(),
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'REMOTE_ADDR' => '***********',
        ],
    );

    $response->assertStatus(200);
});

it('request from non-whitelisted IP address', function () {
    $project = Project::factory(['status' => 'active'])
        ->withSubscription('SMS Starter 1')
        ->withContactGroup()
        ->create();

    $response = getJson(
        '/api/project/details',
        headers: [
            'Authorization' => 'Bearer '.$project->token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
    );

    $response->assertStatus(401);
});
