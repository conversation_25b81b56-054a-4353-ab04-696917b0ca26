<?php

declare(strict_types=1);

use App\Http\Resources\FAQResource;
use App\Models\FAQ;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\getJson;
use function Pest\Laravel\seed;

uses(RefreshDatabase::class);

beforeEach(function () {
    seed();
});

it('returns all active FAQs', function () {
    FAQ::factory()->count(5)->create();

    $response = getJson('/api/faq');

    $response->assertStatus(200);
    $response->assertJsonStructure(['data']);
});

it('returns only empty array for no FAQs', function () {
    $response = getJson('/api/faq');

    $response->assertStatus(200);
    $response->assertJson(['data' => []]);
});

it('it transforms faq to array', function () {
    // Create a FAQ model instance
    $faq = FAQ::factory()->create([
        'question' => 'Test Question',
        'answer' => 'Test Answer',
        'is_active' => true,
    ]);

    // Create a resource instance
    $resource = new FAQResource($faq);

    // Transform to array
    $array = $resource->toArray(request());

    // Assert the array has the expected structure and values
    $this->assertEquals([
        'question' => 'Test Question',
        'answer' => 'Test Answer',
    ], $array);
});

it('it handles null value', function () {

    // Create a resource with null
    $resource = new FAQResource(null);

    // Transform to array
    $array = $resource->toArray(request());

    // Assert the array is empty when resource is null
    $this->assertEquals([], $array);
});
