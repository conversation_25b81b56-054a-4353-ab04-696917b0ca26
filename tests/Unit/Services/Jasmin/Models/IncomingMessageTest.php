<?php

declare(strict_types=1);

use App\Services\Jasmin\Models\IncomingMessage;

describe('IncomingMessage', function () {
    it('can be constructed with all properties', function () {
        $msg = new IncomingMessage(
            id: 'abc123',
            from: '+1234567890',
            to: '+0987654321',
            originConnector: 'http-connector',
            priority: '1',
            coding: 0,
            validity: '3600',
            content: 'Hello World',
            binary: ''
        );

        expect($msg->id)->toBe('abc123');
        expect($msg->from)->toBe('+1234567890');
        expect($msg->to)->toBe('+0987654321');
        expect($msg->originConnector)->toBe('http-connector');
        expect($msg->priority)->toBe('1');
        expect($msg->coding)->toBe(0);
        expect($msg->validity)->toBe('3600');
        expect($msg->content)->toBe('Hello World');
        expect($msg->binary)->toBe('');
    });

    it('can be created from array', function () {
        $data = [
            'id' => 'xyz789',
            'from' => '+1111111111',
            'to' => '+2222222222',
            'origin-connector' => 'http-2',
            'priority' => '2',
            'coding' => 8,
            'validity' => '7200',
            'content' => 'Test Message',
            'binary' => '010203',
        ];
        $msg = IncomingMessage::fromArray($data);

        expect($msg->id)->toBe('xyz789');
        expect($msg->from)->toBe('+1111111111');
        expect($msg->to)->toBe('+2222222222');
        expect($msg->originConnector)->toBe('http-2');
        expect($msg->priority)->toBe('2');
        expect($msg->coding)->toBe(8);
        expect($msg->validity)->toBe('7200');
        expect($msg->content)->toBe('Test Message');
        expect($msg->binary)->toBe('010203');
    });
});
