<?php

declare(strict_types=1);

use App\Services\Jasmin\JasminResponse;
use App\Services\Jasmin\Models\SentMessage;
use Illuminate\Http\Client\Response;

// Helper to create a JasminResponse mock
function makeJasminResponse(string $body, int $status = 200): JasminResponse
{
    $mock = Mockery::mock(Response::class);
    $mock->shouldReceive('status')->andReturn($status);
    $mock->shouldReceive('body')->andReturn($body);
    $mock->shouldReceive('ok')->andReturn($status >= 200 && $status < 300);

    // Pass null for $data, so constructor uses ->body()
    return new JasminResponse($mock, $status, null);
}

describe('SentMessage', function () {
    it('constructs with default nulls', function () {
        $msg = new SentMessage();
        expect($msg->status)->toBeNull();
        expect($msg->messageId)->toBeNull();
        expect($msg->rawResponse)->toBeNull();
    });

    it('constructs with values', function () {
        $msg = new SentMessage('Success', 'abc123', 'Success "abc123"');
        expect($msg->status)->toBe('Success');
        expect($msg->messageId)->toBe('abc123');
        expect($msg->rawResponse)->toBe('Success "abc123"');
    });

    it('parses a successful response', function () {
        $response = makeJasminResponse('Success "abc123"');
        $msg = SentMessage::fromResponse($response);
        expect($msg->status)->toBe('Success');
        expect($msg->messageId)->toBe('abc123');
        expect($msg->rawResponse)->toBe('Success "abc123"');
        expect($msg->isSent())->toBeTrue();
    });

    it('parses a failed response', function () {
        $response = makeJasminResponse('Error "something"');
        $msg = SentMessage::fromResponse($response);
        expect($msg->status)->toBeNull();
        expect($msg->messageId)->toBeNull();
        expect($msg->isSent())->toBeFalse();
    });

    it('throws on empty response', function () {
        $response = makeJasminResponse('');
        expect(fn () => SentMessage::fromResponse($response))->toThrow(RuntimeException::class, 'Invalid response from jasmin');
    });

    it('isSent returns false for empty messageId', function () {
        $msg = new SentMessage('Success', '');
        expect($msg->isSent())->toBeFalse();
    });

    it('isSent returns false for non-success status', function () {
        $msg = new SentMessage('Error', 'abc123');
        expect($msg->isSent())->toBeFalse();
    });
});
