<?php

declare(strict_types=1);

use App\Services\Jasmin\Exceptions\JasminClientException;
use App\Services\Jasmin\Models\Message;
use App\Services\Jasmin\Models\SentMessage;

beforeEach(function () {
    $this->message = new Message();
});

afterEach(function () {
    Mockery::close();
});

it('can set and get destination address', function () {
    $this->message->to('218912345678');
    expect($this->message->to)->toBe('218912345678');
});

it('can set and get sender address', function () {
    $this->message->from('TestSender');
    expect($this->message->from)->toBe('TestSender');
});

it('can set and get content', function () {
    $this->message->content('Hello world');
    expect($this->message->content)->toBe('Hello world');
});

it('can set DLR options and callback URL', function () {
    $this->message->trackDelivery(true);
    $this->message->dlrCallbackUrl('https://callback.test/dlr');
    expect($this->message->dlr)->toBeTrue()
        ->and($this->message->dlrUrl)->toBe('https://callback.test/dlr');
});

it('can set coding, tags, priority, validity', function () {
    $this->message->coding(8)->tags('test')->priority(2)->validityPeriod(60);
    expect(true)->toBeTrue(); // No public getters, just ensure no exceptions
});

it('can set coding, tags, priority, validity, sdt, asBinary, via, withCredentials', function () {
    $this->message->coding(8)
        ->tags('test')
        ->priority(2)
        ->validityPeriod(60)
        ->sdt('2025-04-15T12:00:00Z')
        ->asBinary(true)
        ->via('http', 'user', 'pass')
        ->withCredentials('user2', 'pass2', 'http://example.com');
    expect(true)->toBeTrue(); // No public getters, just ensure no exceptions
});

it('toArray includes all set fields', function () {
    $this->message->to('218912345678')
        ->from('TestSender')
        ->content('Test')
        ->dlrCallbackUrl('https://cb')
        ->tags('tag')
        ->coding(8)
        ->priority(3)
        ->validityPeriod(120)
        ->sdt('2025-04-15T12:00:00Z')
        ->asBinary(true)
        ->via('http', 'user', 'pass')
        ->withCredentials('user2', 'pass2', 'http://example.com');
    $arr = $this->message->toArray();
    expect($arr['to'])->toBe('218912345678')
        ->and($arr['from'])->toBe('TestSender')
        ->and($arr['content'])->toBe('Test')
        ->and($arr['tags'])->toBe('tag')
        ->and($arr['dlr'])->toBe('yes');
});

it('throws if required fields are missing on send', function () {
    $this->expectException(JasminClientException::class);
    $this->message->send();
});

it('throws ValidationException if required fields are missing on send', function () {
    $this->expectException(JasminClientException::class);
    $this->message->send();
});

it('throws JasminClientException if sendMessage returns unsuccessful response', function () {
    $mockResponse = Mockery::mock('App\\Services\\Jasmin\\JasminResponse')->makePartial();
    $mockResponse->shouldReceive('isSuccessful')->andReturn(false);
    $mock = Mockery::mock('App\\Services\\Jasmin\\HttpService')->makePartial();
    $mock->shouldReceive('sendMessage')->andReturn($mockResponse);
    $msg = new Message();
    $msg->to('218912345678')->from('TestSender')->content('Test');
    $msg->withCredentials('user', 'pass', 'http://localhost');
    expect(fn () => $msg->send())->toThrow(JasminClientException::class);
});

it('throws JasminClientException if sendMessage returns unsuccessful response (coverage for throw line)', function () {
    $mockResponse = Mockery::mock(App\Services\Jasmin\JasminResponse::class);
    $mockResponse->shouldReceive('isSuccessful')->andReturn(false);
    $mock = Mockery::mock(App\Services\Jasmin\HttpService::class);
    $mock->shouldReceive('sendMessage')->andReturn($mockResponse);
    $msg = new Message();
    $msg->to('218912345678')->from('TestSender')->content('Test');
    $msg->setHttpService($mock);
    expect(fn () => $msg->send())->toThrow(JasminClientException::class, 'Failed to send message');
});

it('logs and rethrows JasminClientException in send', function () {
    $mock = Mockery::mock(App\Services\Jasmin\HttpService::class);
    $exception = new JasminClientException('Jasmin error');
    $mock->shouldReceive('sendMessage')->andThrow($exception);
    $msg = new Message();
    $msg->to('218912345678')->from('TestSender')->content('Test');
    $msg->setHttpService($mock);
    // Optionally, mock Log::error if you want to assert logging
    expect(fn () => $msg->send())->toThrow(JasminClientException::class);
});

it('can convert to array for sending', function () {
    $this->message->to('218912345678')->from('TestSender')->content('Test')->dlrCallbackUrl('https://cb')->tags('tag');
    $arr = $this->message->toArray();
    expect($arr['to'])->toBe('218912345678')
        ->and($arr['from'])->toBe('TestSender')
        ->and($arr['content'])->toBe('Test')
        ->and($arr['tags'])->toBe('tag');
});

it('calls HttpService and returns SentMessage on send', function () {
    $mockResponse = Mockery::mock('App\\Services\\Jasmin\\JasminResponse')->makePartial();
    $mockResponse->shouldReceive('isSuccessful')->andReturn(true);
    $mockResponse->data = 'Success "msgid-1"';
    $mock = Mockery::mock('App\\Services\\Jasmin\\HttpService')->makePartial();
    $mock->shouldReceive('sendMessage')->andReturn($mockResponse);
    $msg = new Message();
    $msg->to('218912345678')->from('TestSender')->content('Test')->dlrCallbackUrl('https://cb')->tags('tag');
    $msg->withCredentials('user', 'pass', 'http://localhost');
    $msg->setHttpService($mock); // Inject the mock service
    $result = $msg->send();
    expect($result)->toBeInstanceOf(SentMessage::class);
});
