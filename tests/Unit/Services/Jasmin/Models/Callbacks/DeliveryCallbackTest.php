<?php

declare(strict_types=1);

use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;

it('constructs with all fields', function () {
    $callback = new DeliveryCallback(
        id: 'abc-123',
        messageStatus: 'DELIVRD',
        level: 1,
        connector: 'smpp1',
        smscId: 'smsc-42',
        submittedDate: '2025-04-16 12:00:00',
        doneDate: '2025-04-16 12:01:00',
        submittedCount: 1,
        deliveredCount: 1,
        error: 0,
        text: 'Hello world!'
    );
    expect($callback->getMessageId())->toBe('abc-123');
    expect($callback->messageStatus)->toBe('DELIVRD');
    expect($callback->level)->toBe(1);
    expect($callback->connector)->toBe('smpp1');
    expect($callback->smscId)->toBe('smsc-42');
    expect($callback->submittedDate)->toBe('2025-04-16 12:00:00');
    expect($callback->doneDate)->toBe('2025-04-16 12:01:00');
    expect($callback->submittedCount)->toBe(1);
    expect($callback->deliveredCount)->toBe(1);
    expect($callback->error)->toBe(0);
    expect($callback->text)->toBe('Hello world!');
});

it('returns correct validation rules', function () {
    $rules = DeliveryCallback::rules();
    expect($rules)->toBeArray();
    expect($rules['id'])->toContain('required');
    expect($rules['message_status'])->toContain('required');
    expect($rules['level'])->toContain('integer');
    expect($rules['connector'])->toContain('required');
    expect($rules['text'])->toContain('max:25');
});
