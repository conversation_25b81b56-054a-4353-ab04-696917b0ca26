<?php

declare(strict_types=1);

use App\Services\Jasmin\JasminResponse;
use Illuminate\Http\Client\Response;

it('constructs with explicit status and data', function () {
    $mock = Mockery::mock(Response::class);
    $response = new JasminResponse($mock, 201, ['foo' => 'bar']);
    expect($response->status)->toBe(201);
    expect($response->data)->toBe(['foo' => 'bar']);
});

it('constructs with response defaults', function () {
    $mock = Mockery::mock(Response::class);
    $mock->shouldReceive('status')->andReturn(200);
    $mock->shouldReceive('body')->andReturn('ok');
    $response = new JasminResponse($mock);
    expect($response->status)->toBe(200);
    expect($response->data)->toBe('ok');
});

it('from() creates instance from response', function () {
    $mock = Mockery::mock(Response::class);
    $mock->shouldReceive('status')->andReturn(202);
    $mock->shouldReceive('body')->andReturn('accepted');
    $jasminResponse = JasminResponse::from($mock);
    expect($jasminResponse)->toBeInstanceOf(JasminResponse::class);
    expect($jasminResponse->status)->toBe(202);
    expect($jasminResponse->data)->toBe('accepted');
});

it('isSuccessful returns true for ok response', function () {
    $mock = Mockery::mock(Response::class);
    $mock->shouldReceive('ok')->andReturn(true);
    $mock->shouldReceive('status')->andReturn(200);
    $mock->shouldReceive('body')->andReturn('ok');
    $response = new JasminResponse($mock);
    expect($response->isSuccessful())->toBeTrue();
});

it('isSuccessful returns false for non-ok response', function () {
    $mock = Mockery::mock(Response::class);
    $mock->shouldReceive('ok')->andReturn(false);
    $mock->shouldReceive('status')->andReturn(400);
    $mock->shouldReceive('body')->andReturn('error');
    $response = new JasminResponse($mock);
    expect($response->isSuccessful())->toBeFalse();
});
