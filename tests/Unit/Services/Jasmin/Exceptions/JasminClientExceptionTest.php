<?php

declare(strict_types=1);

use App\Services\Jasmin\Exceptions\JasminClientException;

describe('JasminClientException', function () {
    it('can be instantiated directly', function () {
        $e = new JasminClientException('error', 123);
        expect($e)->toBeInstanceOf(JasminClientException::class);
        expect($e->getMessage())->toBe('error');
        expect($e->getCode())->toBe(123);
    });

    it('can be created from another Exception', function () {
        $original = new Exception('fail', 456);
        $e = JasminClientException::from($original);
        expect($e)->toBeInstanceOf(JasminClientException::class);
        expect($e->getMessage())->toBe('fail');
        expect($e->getCode())->toBe(456);
        expect($e->getPrevious())->toBe($original);
    });

    it('can be created from a Throwable', function () {
        $throwable = new Error('throwable error', 789);
        $e = JasminClientException::from($throwable);
        expect($e)->toBeInstanceOf(JasminClientException::class);
        expect($e->getMessage())->toBe('throwable error');
        expect($e->getCode())->toBe(789);
        expect($e->getPrevious())->toBe($throwable);
    });
});
