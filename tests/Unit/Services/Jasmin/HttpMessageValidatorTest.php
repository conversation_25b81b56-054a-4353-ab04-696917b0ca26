<?php

declare(strict_types=1);

use App\Services\Jasmin\Validators\HttpMessageValidator;

describe('HttpMessageValidator', function () {
    it('validates required fields and passes with valid data', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
            'content' => 'Hello',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeFalse();
    });

    it('fails when required fields are missing', function () {
        $data = [];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('to'))->toBeTrue();
        expect($validator->errors()->has('username'))->toBeTrue();
        expect($validator->errors()->has('password'))->toBeTrue();
        expect($validator->errors()->has('content'))->toBeTrue();
    });

    it('validates max length for to, from, username, password', function () {
        $data = [
            'to' => str_repeat('a', 21),
            'from' => str_repeat('b', 21),
            'username' => str_repeat('c', 31),
            'password' => str_repeat('d', 31),
            'content' => 'msg',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('to'))->toBeTrue();
        expect($validator->errors()->has('from'))->toBeTrue();
        expect($validator->errors()->has('username'))->toBeTrue();
        expect($validator->errors()->has('password'))->toBeTrue();
    });

    it('validates coding and priority fields', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
            'content' => 'msg',
            'coding' => 99,
            'priority' => 99,
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('coding'))->toBeTrue();
        expect($validator->errors()->has('priority'))->toBeTrue();
    });

    it('validates dlr, dlr-url, dlr-level, dlr-method fields', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
            'content' => 'msg',
            'dlr' => 'yes',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('dlr-url'))->toBeTrue();
        expect($validator->errors()->has('dlr-level'))->toBeTrue();
        expect($validator->errors()->has('dlr-method'))->toBeTrue();
    });

    it('validates dlr-url, dlr-level, dlr-method with correct values', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
            'content' => 'msg',
            'dlr' => 'yes',
            'dlr-url' => 'https://example.com',
            'dlr-level' => 2,
            'dlr-method' => 'POST',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeFalse();
    });

    it('validates content and hex-content required_without', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('content'))->toBeTrue();
        expect($validator->errors()->has('hex-content'))->toBeTrue();

        $data['hex-content'] = 'A1B2C3';
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeFalse();
    });

    it('validates hex-content regex', function () {
        $data = [
            'to' => '************',
            'username' => 'testuser',
            'password' => 'testpass',
            'hex-content' => 'ZZZZ',
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeTrue();
        expect($validator->errors()->has('hex-content'))->toBeTrue();
    });

    it('returns custom error messages', function () {
        $data = [];
        $validator = HttpMessageValidator::validate($data);
        $messages = $validator->errors()->getMessages();
        expect($messages['to'][0])->toBe('The destination address is required.');
        expect($messages['username'][0])->toBe('The username for Jasmin user account is required.');
        expect($messages['password'][0])->toBe('The password for Jasmin user account is required.');
        expect($messages['content'][0])->toBe('The content field is required when hex-content is not present.');
    });

    it('passes validation with all correct data fields', function () {
        $data = [
            'to' => '************',
            'from' => '************',
            'coding' => 1,
            'username' => 'testuser',
            'password' => 'testpass',
            'priority' => 2,
            'sdt' => 'optional',
            'validity-period' => 100,
            'dlr' => 'yes',
            'dlr-url' => 'https://example.com',
            'dlr-level' => 2,
            'dlr-method' => 'POST',
            'tags' => 'tag1,tag2',
            'content' => 'Hello world',
            // 'hex-content' is not required if 'content' is present
        ];
        $validator = HttpMessageValidator::validate($data);
        expect($validator->fails())->toBeFalse();
    });
});
