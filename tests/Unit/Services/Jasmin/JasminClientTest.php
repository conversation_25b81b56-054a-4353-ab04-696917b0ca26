<?php

declare(strict_types=1);

use App\Services\Jasmin\HttpService;
use App\Services\Jasmin\JasminClient;
use App\Services\Jasmin\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

it('can be instantiated with a HttpService', function () {
    $httpService = Mockery::mock(HttpService::class);
    $client = new JasminClient($httpService);
    expect($client)->toBeInstanceOf(JasminClient::class);
});

it('createWithConfig returns a JasminClient', function () {
    $client = JasminClient::createWithConfig('user', 'pass', 'url');
    expect($client)->toBeInstanceOf(JasminClient::class);
    expect($client->http())->toBeInstanceOf(HttpService::class);
});

it('http() returns the HttpService instance', function () {
    $httpService = Mockery::mock(HttpService::class);
    $client = new JasminClient($httpService);
    expect($client->http())->toBe($httpService);
});

it('message() returns a Message instance', function () {
    $httpService = Mockery::mock(HttpService::class);
    $client = new JasminClient($httpService);
    expect($client->message())->toBeInstanceOf(Message::class);
});

it('delegates receiveDlrCallback to HttpService', function () {
    $httpService = Mockery::mock(HttpService::class);
    $request = Mockery::mock(Request::class);
    $response = Mockery::mock(Response::class);
    $httpService->shouldReceive('receiveDlrCallback')->once()->with($request, Mockery::type('callable'))->andReturn($response);
    $client = new JasminClient($httpService);
    $result = $client->receiveDlrCallback($request, fn () => true);
    expect($result)->toBe($response);
});

it('delegates receiveMessage to HttpService', function () {
    $httpService = Mockery::mock(HttpService::class);
    $request = Mockery::mock(Request::class);
    $response = Mockery::mock(Response::class);
    $httpService->shouldReceive('receiveMessage')->once()->with($request, Mockery::type('callable'))->andReturn($response);
    $client = new JasminClient($httpService);
    $result = $client->receiveMessage($request, fn () => true);
    expect($result)->toBe($response);
});
