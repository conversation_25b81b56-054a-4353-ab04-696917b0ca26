<?php

declare(strict_types=1);

use App\Services\Jasmin\Exceptions\JasminClientException;
use App\Services\Jasmin\HttpService;
use App\Services\Jasmin\Models\Callbacks\DeliveryCallback;
use App\Services\Jasmin\Models\IncomingMessage;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

describe('HttpService', function () {
    it('sends a message successfully', function () {
        Http::fake([
            '*' => Http::response(['status' => 'Success', 'messageId' => 'test-id'], 200),
        ]);
        $service = new HttpService();
        $response = $service->sendMessage('msg', '+1', '+2', 0, 1, null, null, 'yes', 'http://cb', 1, 'POST', null, null);
        expect($response)->not()->toBeNull();
    });

    it('throws on validation failure', function () {
        $this->expectException(ValidationException::class);
        // Simulate validation failure by passing invalid data (missing required fields)
        $service = new HttpService();
        $service->sendMessage('', '', '', 0, null, null, null, '', '', 1, '', null, null);
    });

    it('throws JasminClientException on connection error', function () {
        Http::fake([
            'http://jasmin/send' => fn () => throw new ConnectionException('fail'),
        ]);
        $service = new HttpService();
        $this->expectException(JasminClientException::class);
        $service->sendMessage('msg', '+1', '+2', 0, 1, null, null, 'yes', 'http://cb', 1, 'POST', null, null);
    });

    it('gets metrics successfully', function () {
        Http::fake([
            '*' => Http::response(['metrics' => 'ok'], 200),
        ]);
        $service = new HttpService();
        $response = $service->getMetrics();
        expect($response)->not()->toBeNull();
    });

    it('throws JasminClientException on metrics connection error', function () {
        Http::fake([
            '*' => fn () => throw new ConnectionException('fail'),
        ]);
        $service = new HttpService();
        $this->expectException(JasminClientException::class);
        $service->getMetrics();
    });

    it('receives a valid message and callback returns true', function () {
        $request = Request::create('/', 'POST', [
            'id' => '1', 'from' => '+1', 'to' => '+2', 'origin-connector' => 'oc', 'priority' => '1', 'coding' => 0, 'validity' => '1', 'content' => 'c', 'binary' => '',
        ]);
        $service = new HttpService();
        $response = $service->receiveMessage($request, fn (IncomingMessage $msg) => true);
        expect($response->getStatusCode())->toBe(200);
    });

    it('receives a valid message and callback returns false', function () {
        $request = Request::create('/', 'POST', [
            'id' => '1', 'from' => '+1', 'to' => '+2', 'origin-connector' => 'oc', 'priority' => '1', 'coding' => 0, 'validity' => '1', 'content' => 'c', 'binary' => '',
        ]);
        $service = new HttpService();
        $response = $service->receiveMessage($request, fn (IncomingMessage $msg) => false);
        expect($response->getStatusCode())->toBe(400);
    });

    it('returns 400 for invalid receiveMessage request', function () {
        $request = Request::create('/', 'POST', []);
        $service = new HttpService();
        $response = $service->receiveMessage($request, fn () => true);
        expect($response->getStatusCode())->toBe(400);
    });

    it('receives a valid DLR callback and callback returns true', function () {
        $request = Request::create('/', 'POST', [
            'id' => '1', 'message_status' => 'DELIVRD', 'level' => 1, 'connector' => 'c',
            'id_smsc' => null, 'subdate' => null, 'donedate' => null, 'sub' => 1, 'dlvrd' => 1, 'err' => 0, 'text' => null,
        ]);
        $service = new HttpService();
        $response = $service->receiveDlrCallback($request, fn (DeliveryCallback $dlr) => true);
        expect($response->getStatusCode())->toBe(200);
    });

    it('receives a valid DLR callback and callback returns false', function () {
        $request = Request::create('/', 'POST', [
            'id' => '1', 'message_status' => 'DELIVRD', 'level' => 1, 'connector' => 'c',
            'id_smsc' => null, 'subdate' => null, 'donedate' => null, 'sub' => 1, 'dlvrd' => 1, 'err' => 0, 'text' => null,
        ]);
        $service = new HttpService();
        $response = $service->receiveDlrCallback($request, fn (DeliveryCallback $dlr) => false);
        expect($response->getStatusCode())->toBe(400);
    });

    it('returns 400 for invalid DLR callback request', function () {
        $request = Request::create('/', 'POST', []);
        $service = new HttpService();
        $response = $service->receiveDlrCallback($request, fn () => true);
        expect($response->getStatusCode())->toBe(400);
    });

    it('receives no error for a valid DLR callback and callback returns false', function () {
        $request = Request::create('/', 'POST', [
            'id' => '1', 'message_status' => 'ESME_ROK', 'level' => 1, 'connector' => 'c',
            'id_smsc' => null, 'subdate' => null, 'donedate' => null, 'sub' => 1, 'dlvrd' => 1, 'err' => 0, 'text' => null,
        ]);
        $service = new HttpService();
        $response = $service->receiveDlrCallback($request, fn (DeliveryCallback $dlr) => false);
        expect($response->getStatusCode())->toBe(400);
    });
});
