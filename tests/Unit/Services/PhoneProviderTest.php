<?php

declare(strict_types=1);

use App\Facades\PhoneProvider;
use App\Models\Provider;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('detects Libyana phone number provider', function () {
    // Create Libyana provider
    Provider::factory()->create([
        'name' => 'libyana',
        'status' => 'active',
        'pattern' => '^(?:\+218|00218)?\s?((92|94)\d{7})$',
    ]);

    $phoneNumbers = [
        '+218922345678',
        '+218942345678',
        '00218922345678',
        '00218942345678',
    ];

    foreach ($phoneNumbers as $phoneNumber) {
        expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
            ->toBe('libyana')
            ->not->toBe('almadar')
            ->not->toBe('Unknown');
    }
});

it('detects Almadar phone number provider', function () {
    // Create Almadar provider
    Provider::factory()->create([
        'name' => 'almadar',
        'status' => 'active',
        'pattern' => '^(?:\+218|00218)?\s?((91|93)\d{7})$',
    ]);

    $phoneNumbers = [
        '+218912345678',
        '+218932345678',
        '00218912345678',
        '00218932345678',
    ];

    foreach ($phoneNumbers as $phoneNumber) {
        expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
            ->toBe('almadar')
            ->not->toBe('libyana')
            ->not->toBe('Unknown');
    }
});

it('returns Unknown for unrecognized phone numbers', function () {
    // Create both providers
    Provider::factory()->create([
        'name' => 'libyana',
        'pattern' => '^(?:\+218|00218)?\s?((92|94)\d{7})$',
    ]);

    Provider::factory()->create([
        'name' => 'almadar',
        'pattern' => '^(?:\+218|00218)?\s?((91|93)\d{7})$',
    ]);

    $invalidNumbers = [
        '+9715012345678', // UAE number
        '+201234567890',  // Egypt number
        'invalid-number',
        '12345',
        '',
    ];

    foreach ($invalidNumbers as $phoneNumber) {
        expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
            ->toBe('Unknown');
    }
});

it('prepares phone number for SMSC with valid pattern', function () {
    $provider = Provider::factory()->create([
        'smpp_pattern' => '^00218',
        'smpp_pattern_replace' => '0',
    ]);

    $phoneNumber = '00218912345678';
    $expected = '**********';

    expect(PhoneProvider::prepareForSMSC($provider, $phoneNumber))
        ->toBe($expected);
});

it('returns original number when SMSC pattern is empty', function () {
    $provider = Provider::factory()->create([
        'smpp_pattern' => '',
        'smpp_pattern_replace' => '0',
    ]);

    $phoneNumber = '00218912345678';

    expect(PhoneProvider::prepareForSMSC($provider, $phoneNumber))
        ->toBe($phoneNumber);
});

it('handles invalid regex patterns gracefully', function () {
    $provider = Provider::factory()->create([
        'smpp_pattern' => '[invalid-regex',  // Invalid regex pattern
        'smpp_pattern_replace' => '0',
    ]);

    $phoneNumber = '00218912345678';

    expect(PhoneProvider::prepareForSMSC($provider, $phoneNumber))
        ->toBe($phoneNumber)
        ->and(fn () => Log::shouldReceive('error')->once());
});

it('handles multiple providers with similar patterns', function () {
    Provider::query()->delete();

    // Create two providers with overlapping patterns
    Provider::factory()->create([
        'name' => 'provider1',
        'pattern' => '^\+\d{12}$',
    ]);

    Provider::factory()->create([
        'name' => 'provider2',
        'pattern' => '^\+218\d{9}$',
    ]);

    $phoneNumber = '+218912345678';

    // Should match the first provider that matches the pattern
    expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
        ->toBe('provider1');
});

it('works with empty provider database', function () {
    Provider::query()->delete();

    $phoneNumber = '+218912345678';

    expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
        ->toBe('Unknown');
});

it('handles special characters in phone numbers', function () {
    Provider::query()->delete();

    Provider::factory()->create([
        'name' => 'test_provider',
        'pattern' => '^\+218[\s-]?9[1-4][\s-]?\d{7}$',
    ]);

    $phoneNumbers = [
        '+218-91-2345678',
        '+218 91 2345678',
        '+21891-2345678',
        '+218912345678',
    ];

    foreach ($phoneNumbers as $phoneNumber) {
        expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
            ->toBe('test_provider');
    }
});

it('processes multiple phone numbers in sequence', function () {
    Provider::query()->delete();

    Provider::factory()->create([
        'name' => 'libyana',
        'pattern' => '^(?:\+218|00218)?\s?((92|94)\d{7})$',
    ]);

    Provider::factory()->create([
        'name' => 'almadar',
        'pattern' => '^(?:\+218|00218)?\s?((91|93)\d{7})$',
    ]);

    $phoneNumbers = [
        '+218912345678' => 'almadar',
        '+218922345678' => 'libyana',
        '+218932345678' => 'almadar',
        '+218942345678' => 'libyana',
        '+9715012345678' => 'Unknown',
    ];

    foreach ($phoneNumbers as $phoneNumber => $expectedProvider) {
        expect(PhoneProvider::detectPhoneNumberProvider($phoneNumber))
            ->toBe($expectedProvider);
    }
});
