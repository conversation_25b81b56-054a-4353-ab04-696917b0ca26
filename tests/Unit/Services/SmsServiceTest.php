<?php

declare(strict_types=1);

use App\Services\SmsService;

it('calculate sms parts', function () {
    // Less than 160 characters (GSM 7-bit)
    $result = SmsService::calculateSmsParts('Hello World');
    expect($result)->toEqual(['parts' => 1, 'type' => 'GSM7']);
    // Exactly 160 characters (GSM 7-bit)
    $result = SmsService::calculateSmsParts(str_repeat('a', 160));
    expect($result)->toEqual(['parts' => 1, 'type' => 'GSM7']);
    // More than 160 characters (GSM 7-bit)
    $result = SmsService::calculateSmsParts(str_repeat('a', 161));
    expect($result)->toEqual(['parts' => 2, 'type' => 'GSM7']);

    // Less than 70 characters (UCS-2)
    $result = SmsService::calculateSmsParts('مرحبا بالعالم');
    expect($result)->toEqual(['parts' => 1, 'type' => 'UCS2']);
    // Exactly 70 characters (UCS-2)
    $result = SmsService::calculateSmsParts(str_repeat('ا', 70));
    expect($result)->toEqual(['parts' => 1, 'type' => 'UCS2']);
    // More than 70 characters (UCS-2)
    $result = SmsService::calculateSmsParts(str_repeat('ا', 71));
    expect($result)->toEqual(['parts' => 2, 'type' => 'UCS2']);
});
