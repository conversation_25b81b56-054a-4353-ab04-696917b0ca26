<?php

declare(strict_types=1);

use App\Services\EncodingService;

it('detects GSM7 compatible messages correctly', function () {
    $gsm7Messages = [
        'Hello World!',
        'Basic GSM: @$_[]\\{}|~^',
        '0123456789',
        'The quick brown fox jumps over the lazy dog',
        '!#%&()*+,-./:;<=>?',
    ];

    foreach ($gsm7Messages as $message) {
        expect(EncodingService::isGSM7Message($message))->toBeTrue();
    }
});

it('detects non-GSM7 messages correctly', function () {
    $nonGsm7Messages = [
        '안녕하세요', // Korean
        'مرحبا', // Arabic
        'привет', // Russian
        '你好', // Chinese
        '🌟⭐️✨', // Emojis
        'über café', // Special Latin characters
    ];

    foreach ($nonGsm7Messages as $message) {
        expect(EncodingService::isGSM7Message($message))->toBeFalse();
    }
});

it('encodes messages to UCS2 correctly', function () {
    $messages = [
        'Hello World!' => "\x00H\x00e\x00l\x00l\x00o\x00 \x00W\x00o\x00r\x00l\x00d\x00!",
        'مرحبا' => "\x06\x45\x06\x31\x06\x2D\x06\x28\x06\x27",
    ];

    foreach ($messages as $input => $expected) {
        $encoded = EncodingService::encodeToUCS2($input);
        expect(bin2hex($encoded))->toBe(bin2hex($expected));
    }
});

it('handles empty strings', function () {
    expect(EncodingService::isGSM7Message(''))->toBeTrue()
        ->and(EncodingService::encodeToUCS2(''))->toBe('');
});

it('handles mixed content correctly', function () {
    $mixedMessages = [
        'Hello 世界' => false,
        'SMS & 📱' => false,
        'Test123!@#' => true,
    ];

    foreach ($mixedMessages as $message => $expectedGsm7) {
        expect(EncodingService::isGSM7Message($message))->toBe($expectedGsm7);
    }
});

it('maintains data integrity through UCS2 encoding', function () {
    $testStrings = [
        'Regular ASCII text',
        'مرحبا بالعالم', // Arabic
        '안녕하세요 세계', // Korean
        '你好，世界', // Chinese
    ];

    foreach ($testStrings as $original) {
        $encoded = EncodingService::encodeToUCS2($original);
        $decoded = mb_convert_encoding($encoded, 'UTF-8', 'UCS-2');
        expect($decoded)->toBe($original);
    }
});

it('handles maximum message lengths', function () {
    // 160 characters GSM7 message
    $gsm7Message = str_repeat('A', 160);
    expect(EncodingService::isGSM7Message($gsm7Message))->toBeTrue();

    // 70 characters Unicode message
    $unicodeMessage = str_repeat('漢', 70);
    expect(EncodingService::isGSM7Message($unicodeMessage))->toBeFalse();
});

it('processes newlines and whitespace correctly', function () {
    $messages = [
        "Line 1\nLine 2" => true,
        "Tab\tTest" => true,
        'Space Test' => true,
        "Mixed\n漢字\tTest" => false,
    ];

    foreach ($messages as $message => $expectedGsm7) {
        expect(EncodingService::isGSM7Message($message))->toBe($expectedGsm7);
    }
});
