<?php

declare(strict_types=1);

namespace Tests;

use App\Models\Company;
use App\Models\Project;
use App\Models\Sender;
use App\Services\SenderHelper;
use Carbon\Carbon;

beforeEach(function () {
    $this->seed();

    $this->company = Company::factory()->create();
    $this->project = Project::factory()->create([
        'company_id' => $this->company->id,
    ]);
    $this->sender = Sender::factory()->create([
        'sender' => 'TestSender',
        'status' => 'active',
    ]);

    $this->senderHelper = app(SenderHelper::class);
});

test('getSender returns sender when valid and not expired', function () {
    // Attach company to sender with future expiration
    $this->company->senders()->attach($this->sender->id, [
        'expired_at' => Carbon::now()->addDay(),
    ]);

    $result = $this->senderHelper->getSender('TestSender', $this->project);

    expect($result)->not->toBeNull()
        ->and($result->id)->toBe($this->sender->id)
        ->and($result->sender)->toBe('TestSender');
});

test('getSender returns sender when valid and no expiration', function () {
    // Attach company to sender with null expiration
    $this->company->senders()->attach($this->sender->id, [
        'expired_at' => null,
    ]);

    $result = $this->senderHelper->getSender('TestSender', $this->project);

    expect($result)->not->toBeNull()
        ->and($result->id)->toBe($this->sender->id);
});

test('getSender returns null when sender is expired', function () {
    // Attach company to sender with past expiration
    $this->company->senders()->attach($this->sender->id, [
        'expired_at' => Carbon::now()->subDay(),
    ]);

    $result = $this->senderHelper->getSender('TestSender', $this->project);

    expect($result)->toBeNull();
});

test('getSender returns null when sender is pending', function () {
    $this->sender->update(['status' => 'pending']);
    $this->company->senders()->attach($this->sender->id);

    $result = $this->senderHelper->getSender('TestSender', $this->project);

    expect($result)->toBeNull();
});

test('getSender returns null when sender is rejected', function () {
    $this->sender->update(['status' => 'rejected']);
    $this->company->senders()->attach($this->sender->id);

    $result = $this->senderHelper->getSender('TestSender', $this->project);

    expect($result)->toBeNull();
});

test('getSender returns null when sender not found', function () {
    $result = $this->senderHelper->getSender('NonExistentSender', $this->project);

    expect($result)->toBeNull();
});

test('checkSenderProvider returns true for valid provider', function () {
    $providers = ['libyana', 'almadar'];

    $result = $this->senderHelper->checkSenderProvider('00218911234567', $providers);

    expect($result)->toBeTrue();
});

test('checkSenderProvider returns false for invalid provider', function () {
    $providers = ['libyana', 'almadar'];

    $result = $this->senderHelper->checkSenderProvider('00218991234567', $providers);

    expect($result)->toBeFalse();
});
