<?php

declare(strict_types=1);

use App\Services\AI\MessageCheck;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    // Set up test configuration
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

it('returns approved for acceptable content', function () {
    // Create a mock response with "approved" content
    $mockResponse = new Response(200, [], json_encode([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        ['text' => 'approved'],
                    ],
                ],
            ],
        ],
    ]));

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Hello world'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toBe('approved');
});

it('returns rejected for unacceptable content', function () {
    // Create a mock response with "rejected" content
    $mockResponse = new Response(200, [], json_encode([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        ['text' => 'rejected'],
                    ],
                ],
            ],
        ],
    ]));

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Political content'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toBe('rejected');
});

it('defaults to rejected for unexpected response content', function () {
    // Create a mock response with unexpected content
    $mockResponse = new Response(200, [], json_encode([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        ['text' => 'This is neither approved nor rejected'],
                    ],
                ],
            ],
        ],
    ]));

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toBe('rejected');
});

it('handles API error responses', function () {
    // Create a mock error response
    $mockResponse = new Response(400, [], json_encode([
        'error' => [
            'message' => 'Invalid request',
        ],
    ]));

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toContain('rejected');
});

it('handles missing text in API response', function () {
    // Create a mock response with missing text
    $mockResponse = new Response(200, [], json_encode([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        // Missing 'text' field
                    ],
                ],
            ],
        ],
    ]));

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toBe('rejected');
});

it('handles request exceptions', function () {
    // Create a mock exception
    $mockException = new RequestException('Connection error', new Request('POST', 'test'));

    // Create a mock handler and client
    $mock = new MockHandler([$mockException]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toContain('Error: Request failed - Connection error');
});

it('handles JSON exceptions', function () {
    // Create a mock response with invalid JSON
    $mockResponse = new Response(200, [], '{invalid json}');

    // Create a mock handler and client
    $mock = new MockHandler([$mockResponse]);
    $handlerStack = HandlerStack::create($mock);
    $mockClient = new Client(['handler' => $handlerStack]);

    // Create a partial mock of MessageCheck to use our mock client
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andReturn($mockClient);

    // Test the moderate method
    $result = $messageCheck->moderate();

    expect($result)->toContain('Error: JSON parsing failed');
});

it('throws exceptions for unexpected errors', function () {
    // Create a partial mock of MessageCheck that throws an unexpected exception
    $messageCheck = Mockery::mock(MessageCheck::class, ['Test message'])
        ->shouldAllowMockingProtectedMethods()
        ->makePartial();
    $messageCheck->shouldReceive('getClient')->andThrow(new Exception('Unexpected error'));

    // Test the moderate method - should throw the exception
    expect(function () use ($messageCheck) {
        $messageCheck->moderate();
    })->toThrow(Exception::class, 'Unexpected error');
});
