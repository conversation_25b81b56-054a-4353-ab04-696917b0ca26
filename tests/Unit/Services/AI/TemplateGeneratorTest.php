<?php

declare(strict_types=1);

use App\Services\AI\TemplateGenerator;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();

    // Set up test configuration
    Config::set('message_check.token', 'test-token');
    Config::set('message_check.url', 'https://test-api.example.com/');
    Config::set('message_check.model', 'test-model');
});

it('can generate a template successfully', function () {
    // Mock successful API response
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'مرحباً {{name}}! نرحب بك في {{company_name}}.',
                                'parameters' => [
                                    ['name' => 'name', 'description' => 'User name'],
                                    ['name' => 'company_name', 'description' => 'Company name'],
                                ],
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('مرحباً {{name}}! نرحب بك في {{company_name}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('company_name');
});

it('handles API failure gracefully', function () {
    $mock = new MockHandler([
        new Response(500, [], 'Internal Server Error'),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('API request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles request exception gracefully', function () {
    $mock = new MockHandler([
        new RequestException('Connection timeout', new Request('POST', 'test')),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toContain('Request failed');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('handles invalid JSON response gracefully', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => 'Invalid JSON response',
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('welcome', 'Welcome new users', 'ar', $client) extends TemplateGenerator {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeFalse();
    expect($result['error'])->toBe('Failed to parse response');
    expect($result['content'])->toBe('');
    expect($result['parameters'])->toBe([]);
});

it('extracts parameters from content when not provided', function () {
    $mockResponse = [
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => json_encode([
                                'content' => 'Hello {{name}}, your code is {{code}}.',
                            ]),
                        ],
                    ],
                ],
            ],
        ],
    ];

    $mock = new MockHandler([
        new Response(200, [], json_encode($mockResponse)),
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    $generator = new class('verification', 'Send verification code', 'en', $client) extends TemplateGenerator {
        private Client $mockClient;

        public function __construct(string $templateType, string $description, string $language, Client $mockClient)
        {
            parent::__construct($templateType, $description, $language);
            $this->mockClient = $mockClient;
        }

        protected function getClient(): Client
        {
            return $this->mockClient;
        }
    };

    $result = $generator->generate();

    expect($result['success'])->toBeTrue();
    expect($result['content'])->toBe('Hello {{name}}, your code is {{code}}.');
    expect($result['parameters'])->toHaveCount(2);
    expect($result['parameters'][0]['name'])->toBe('name');
    expect($result['parameters'][1]['name'])->toBe('code');
});

it('builds correct prompt for different template types and languages', function () {
    $generator = new TemplateGenerator('welcome', 'Welcome message for new users', 'ar');
    
    // Use reflection to test the private buildPrompt method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true);
    
    $prompt = $method->invoke($generator);
    
    expect($prompt)->toContain('Generate the template in Arabic language');
    expect($prompt)->toContain('Template Type: welcome');
    expect($prompt)->toContain('Description: Welcome message for new users');
    expect($prompt)->toContain('{{parameters}}');
});

it('builds correct prompt for English language', function () {
    $generator = new TemplateGenerator('notification', 'Send notification to users', 'en');
    
    // Use reflection to test the private buildPrompt method
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('buildPrompt');
    $method->setAccessible(true);
    
    $prompt = $method->invoke($generator);
    
    expect($prompt)->toContain('Generate the template in English language');
    expect($prompt)->toContain('Template Type: notification');
    expect($prompt)->toContain('Description: Send notification to users');
});
