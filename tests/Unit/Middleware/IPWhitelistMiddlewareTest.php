<?php

declare(strict_types=1);

use App\Http\Middleware\IPWhitelistMiddleware;
use App\Models\IpWhitelist;
use App\Models\Project;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('allows request from whitelisted IP address', function () {
    // Create a project with a token
    $project = Project::factory()->create();

    // Create a whitelisted IP for the project
    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create a mock request with the whitelisted IP
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Execute middleware
    $response = $middleware->handle($request, $next);

    // Assert response is successful
    expect($response->getStatusCode())->toBe(200)
        ->and($response->getContent())->toBe('OK');
});

it('blocks request from non-whitelisted IP address', function () {
    // Create a project with a token
    $project = Project::factory()->create();

    // Create a whitelisted IP for the project
    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create a mock request with a different IP
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Execute middleware
    $response = $middleware->handle($request, $next);

    // Assert response is unauthorized
    expect($response->getStatusCode())->toBe(401)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Unauthorized IP address.',
        ]);
});

it('handles multiple whitelisted IPs correctly', function () {
    // Create a project with a token
    $project = Project::factory()->create();

    // Create multiple whitelisted IPs for the project
    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Test first IP
    $request1 = Request::create('/api/test', 'GET');
    $request1->server->set('REMOTE_ADDR', '***********');
    $request1->headers->set('Authorization', 'Bearer '.$project->token);

    $response1 = $middleware->handle($request1, $next);
    expect($response1->getStatusCode())->toBe(200);

    // Test second IP
    $request2 = Request::create('/api/test', 'GET');
    $request2->server->set('REMOTE_ADDR', '***********');
    $request2->headers->set('Authorization', 'Bearer '.$project->token);

    $response2 = $middleware->handle($request2, $next);
    expect($response2->getStatusCode())->toBe(200);
});

it('handles invalid bearer token', function () {
    // Create a mock request with invalid token
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');
    $request->headers->set('Authorization', 'Bearer invalid-token');

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Expect an exception when project is not found
    expect(fn () => $middleware->handle($request, $next))
        ->toThrow(Exception::class);
});

it('handles missing bearer token', function () {
    // Create a mock request without token
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Expect an exception when bearer token is missing
    expect(fn () => $middleware->handle($request, $next))
        ->toThrow(Exception::class);
});

it('handles project with no whitelisted IPs', function () {
    // Create a project with a token but no whitelisted IPs
    $project = Project::factory()->create();

    // Create a mock request
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    // Create the middleware
    $middleware = new IPWhitelistMiddleware();

    // Create a mock response
    $next = function ($request) {
        return new Response('OK');
    };

    // Execute middleware
    $response = $middleware->handle($request, $next);

    // Assert response is unauthorized
    expect($response->getStatusCode())->toBe(401)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Unauthorized IP address.',
        ]);
});

test('middleware blocks request with invalid CF-Connecting-IP header in production', function () {
    $project = Project::factory()->create();

    // Mock production environment
    App::shouldReceive('isProduction')
        ->once()
        ->andReturn(true);

    IpWhitelist::factory()->create([
        'project_id' => $project->id,
        'ip_address' => '***********',
    ]);

    // Create a request with disallowed IP in CF header
    $request = Request::create('/api/test', 'GET');
    $request->server->set('REMOTE_ADDR', '***********'); // Not relevant in production
    $request->headers->set('CF-Connecting-IP', '***********'); // This is not allowed
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    // Create a response closure that should not be called
    $next = function ($req) {
        return new Response('OK');
    };

    // Create an instance of the middleware and handle the request
    $middleware = new IPWhitelistMiddleware();
    $response = $middleware->handle($request, $next);

    // Assert that the response is blocked
    expect($response->getStatusCode())->toBe(200)
        ->and($response->getContent())->toBe('OK');
});
