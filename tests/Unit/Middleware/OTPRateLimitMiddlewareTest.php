<?php

declare(strict_types=1);

use App\Http\Middleware\OTPRateLimitMiddleware;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('allows request when under rate limit', function () {
    $project = Project::factory()->create(['status' => 'active']);
    Cache::shouldReceive('get')->andReturn(0);
    Cache::shouldReceive('put');

    $request = Request::create('/otp', 'POST', ['receiver' => '1234567890']);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new OTPRateLimitMiddleware();
    /** @var Response $response */
    $response = $middleware->handle($request, fn ($req) => new Response());

    expect($response->getStatusCode())->toBe(200);
});

it('blocks request when over rate limit', function () {
    $project = Project::factory()->create(['token' => 'valid-token', 'status' => 'active']);
    Cache::shouldReceive('get')->andReturn(10);

    $request = Request::create('/otp', 'POST', ['receiver' => '1234567890']);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new OTPRateLimitMiddleware();
    /** @var Response $response */
    $response = $middleware->handle($request, fn ($req) => new Response());

    expect($response->getStatusCode())->toBe(429)
        ->and($response->headers->get('Retry-After'))->toBe('3600');
});

it('returns 401 for invalid token', function () {
    $request = Request::create('/otp', 'POST', ['receiver' => '1234567890']);
    $request->headers->set('Authorization', 'Bearer invalid-token');

    $middleware = new OTPRateLimitMiddleware();
    /** @var Response $response */
    $response = $middleware->handle($request, fn ($req) => new Response());

    expect($response->getStatusCode())->toBe(401)
        ->and($response->getContent())->toContain('Invalid or missing token.');
});

it('returns 400 when receiver is missing', function () {
    $project = Project::factory()->create(['status' => 'active']);
    $request = Request::create('/otp', 'POST');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new OTPRateLimitMiddleware();
    /** @var Response $response */
    $response = $middleware->handle($request, fn ($req) => new Response());

    expect($response->getStatusCode())->toBe(400)
        ->and($response->getContent())->toContain('Receiver is required.');
});
