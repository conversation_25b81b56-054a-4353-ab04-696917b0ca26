<?php

declare(strict_types=1);

use App\Http\Middleware\ProjectMiddleware;
use App\Models\Project;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('allows request with valid token and active project', function () {
    // Arrange
    $project = Project::factory(['status' => 'active'])->create();
    $request = Request::create('/api/test');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe(['success' => true]);
});

it('rejects request with invalid token', function () {
    // Arrange
    $request = Request::create('/api/test');
    $request->headers->set('Authorization', 'Bearer invalid_token');

    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_UNAUTHORIZED)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Invalid or missing token.',
        ]);
});

it('rejects request with missing token', function () {
    // Arrange
    $request = Request::create('/api/test');
    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_UNAUTHORIZED)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Invalid or missing token.',
        ]);
});

it('rejects request for inactive project', function () {
    // Arrange
    $project = Project::factory(['status' => 'inactive'])->create();
    $request = Request::create('/api/test');
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_UNAUTHORIZED)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Project is inactive.',
        ]);
});

it('handles different HTTP methods', function () {
    // Arrange
    $project = Project::factory(['status' => 'active'])->create();
    $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

    foreach ($methods as $method) {
        $request = Request::create('/api/test', $method);
        $request->headers->set('Authorization', 'Bearer '.$project->token);

        $middleware = new ProjectMiddleware();

        // Act
        $response = $middleware->handle($request, function ($request) {
            return response()->json(['success' => true]);
        });

        // Assert
        expect($response->getStatusCode())->toBe(Response::HTTP_OK)
            ->and(json_decode($response->getContent(), true))->toBe(['success' => true]);
    }
});

it('preserves request data after middleware', function () {
    // Arrange
    $project = Project::factory(['status' => 'active'])->create();
    $request = Request::create('/api/test', 'POST', ['test_data' => 'value']);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json([
            'success' => true,
            'data' => $request->all(),
        ]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe([
            'success' => true,
            'data' => ['test_data' => 'value'],
        ]);
});

it('handles malformed bearer token', function () {
    // Arrange
    $request = Request::create('/api/test');
    $request->headers->set('Authorization', 'malformed_token_format');

    $middleware = new ProjectMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_UNAUTHORIZED)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Invalid or missing token.',
        ]);
});
