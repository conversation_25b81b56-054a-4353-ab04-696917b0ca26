<?php

declare(strict_types=1);

use App\Http\Middleware\CompanyVerificationMiddleware;
use App\Models\Company;
use App\Models\Project;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('allows request when company verified_at is not null', function () {
    // Arrange
    $company = Company::factory(['verified_at' => now()])->create();
    $project = Project::factory(['company_id' => $company->id, 'status' => 'active'])->create();

    $request = Request::create('/api/sms/messages', 'POST', ['receiver' => '+1234567890']);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json([
            'success' => true,
            'receiver' => $request->input('receiver'),
        ]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe([
            'success' => true,
            'receiver' => '+1234567890',
        ]);
});

it('redirects receiver to company phone when verified_at is null', function () {
    // Arrange
    $company = Company::factory([
        'verified_at' => null,
        'phone' => '+9876543210',
    ])->create();
    $project = Project::factory(['company_id' => $company->id, 'status' => 'active'])->create();

    $request = Request::create('/api/sms/messages', 'POST', ['receiver' => '+1234567890']);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json([
            'success' => true,
            'receiver' => $request->input('receiver'),
        ]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe([
            'success' => true,
            'receiver' => '+9876543210', // Should be company phone
        ]);
});

it('redirects bulk receivers to company phone when verified_at is null', function () {
    // Arrange
    $company = Company::factory([
        'verified_at' => null,
        'phone' => '+9876543210',
    ])->create();
    $project = Project::factory(['company_id' => $company->id, 'status' => 'active'])->create();

    $request = Request::create('/api/sms/messages/bulk', 'POST', [
        'receivers' => ['+1234567890', '+1111111111'],
    ]);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json([
            'success' => true,
            'receivers' => $request->input('receivers'),
        ]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe([
            'success' => true,
            'receivers' => ['+9876543210'], // Should be company phone only
        ]);
});

it('blocks contacts_send feature when verified_at is null', function () {
    // Arrange
    $company = Company::factory([
        'verified_at' => null,
        'phone' => '+9876543210',
    ])->create();
    $project = Project::factory(['company_id' => $company->id, 'status' => 'active'])->create();

    $request = Request::create('/api/sms/messages/contacts', 'POST', [
        'contact_group_id' => 'some-uuid',
    ]);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_FORBIDDEN)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'You can not use this feature until verify your company',
        ]);
});

it('allows contacts_send feature when company is verified', function () {
    // Arrange
    $company = Company::factory(['verified_at' => now()])->create();
    $project = Project::factory(['company_id' => $company->id, 'status' => 'active'])->create();

    $request = Request::create('/api/sms/messages/contacts', 'POST', [
        'contact_group_id' => 'some-uuid',
    ]);
    $request->headers->set('Authorization', 'Bearer '.$project->token);

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json([
            'success' => true,
            'contact_group_id' => $request->input('contact_group_id'),
        ]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_OK)
        ->and(json_decode($response->getContent(), true))->toBe([
            'success' => true,
            'contact_group_id' => 'some-uuid', // Should remain unchanged
        ]);
});

it('returns error when project token is invalid', function () {
    // Arrange
    $request = Request::create('/api/sms/messages', 'POST', ['receiver' => '+1234567890']);
    $request->headers->set('Authorization', 'Bearer invalid_token');

    $middleware = new CompanyVerificationMiddleware();

    // Act
    $response = $middleware->handle($request, function ($request) {
        return response()->json(['success' => true]);
    });

    // Assert
    expect($response->getStatusCode())->toBe(Response::HTTP_UNAUTHORIZED)
        ->and(json_decode($response->getContent(), true))->toBe([
            'message' => 'Invalid or missing token.',
        ]);
});
