<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\SubscriptionPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any subscription', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new SubscriptionPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_subscription',
    'view' => 'view_subscription',
    'create' => 'create_subscription',
    'update' => 'update_subscription',
    'delete' => 'delete_subscription',
    'deleteAny' => 'delete_any_subscription',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} subscription", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new SubscriptionPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} subscription", function () use ($method) {
        $user = User::factory()->create();

        $policy = new SubscriptionPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
