<?php

declare(strict_types=1);

use App\Models\Role;
use App\Models\User;
use App\Policies\RolePolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any role', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new RolePolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

// Only include permissions that exist in the system
$abilities = [
    'viewAny' => 'view_any_role',
    'view' => 'view_role',
    'create' => 'create_role',
    'update' => 'update_role',
    'delete' => 'delete_role',
    //    'deleteAny' => 'delete_any_role',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} role", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new RolePolicy();

        $args = [$user];

        if (in_array($method, ['view', 'update', 'delete'])) {
            $role = Role::factory()->create(['name' => 'test_role', 'guard_name' => 'web']);
            $args[] = $role;
        }

        expect($policy->{$method}(...$args))->toBeTrue();
    });

    it("denies user without permission to {$method} role", function () use ($method) {
        $user = User::factory()->create();

        $policy = new RolePolicy();

        $args = [$user];

        if (in_array($method, ['view', 'update', 'delete'])) {
            $role = Role::factory()->create(['name' => 'test_role', 'guard_name' => 'web']);
            $args[] = $role;
        }

        expect($policy->{$method}(...$args))->toBeFalse();
    });

}

// Special test: cannot view, update, or delete super_admin or company_owner roles
foreach (['super_admin', 'company_owner'] as $specialRoleName) {
    it("denies user to view, update, and delete the {$specialRoleName} role", function () use ($specialRoleName) {
        $user = User::factory()->create();
        $user->givePermissionTo([
            'view_role',
            'update_role',
            'delete_role',
        ]);

        $specialRole = Role::where('name', $specialRoleName)->first();

        $policy = new RolePolicy();

        expect($policy->view($user, $specialRole))->toBeFalse();
        expect($policy->update($user, $specialRole))->toBeFalse();
        expect($policy->delete($user, $specialRole))->toBeFalse();
    });
}

it('always denies deleteAny', function () {
    $policy = new RolePolicy();

    expect($policy->deleteAny())->toBeFalse();
});
