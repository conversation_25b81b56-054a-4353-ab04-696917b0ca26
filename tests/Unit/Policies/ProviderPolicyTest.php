<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\ProviderPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any provider', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new ProviderPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_provider',
    'view' => 'view_provider',
    'create' => 'create_provider',
    'update' => 'update_provider',
    'delete' => 'delete_provider',
    'deleteAny' => 'delete_any_provider',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} provider", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new ProviderPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} provider", function () use ($method) {
        $user = User::factory()->create();

        $policy = new ProviderPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
