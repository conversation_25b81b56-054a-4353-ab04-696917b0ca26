<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\LogActivityPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any log activity', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new LogActivityPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_log::activity',
    'view' => 'view_log::activity',
    'create' => 'create_log::activity',
    'update' => 'update_log::activity',
    'delete' => 'delete_log::activity',
    'deleteAny' => 'delete_any_log::activity',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} log activity", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new LogActivityPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} log activity", function () use ($method) {
        $user = User::factory()->create();

        $policy = new LogActivityPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
