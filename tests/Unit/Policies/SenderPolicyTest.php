<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\SenderPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any sender', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new SenderPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_sender',
    'view' => 'view_sender',
    'create' => 'create_sender',
    'update' => 'update_sender',
    'delete' => 'delete_sender',
    'deleteAny' => 'delete_any_sender',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} sender", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new SenderPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} sender", function () use ($method) {
        $user = User::factory()->create();

        $policy = new SenderPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
