<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\MessagePolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

$abilities = [
    'viewAny' => 'view_any_message',
    'view' => 'view_message',
    'create' => 'create_message',
    'update' => 'update_message',
    'delete' => 'delete_message',
    'deleteAny' => 'delete_any_message',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} message", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new MessagePolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} message", function () use ($method) {
        $user = User::factory()->create();

        $policy = new MessagePolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
