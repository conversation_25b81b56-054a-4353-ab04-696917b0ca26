<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\ProjectPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

$abilities = [
    'viewAny' => 'view_any_project',
    'view' => 'view_project',
    'create' => 'create_project',
    'update' => 'update_project',
    'delete' => 'delete_project',
    'deleteAny' => 'delete_any_project',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} project", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new ProjectPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} project", function () use ($method) {
        $user = User::factory()->create();

        $policy = new ProjectPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
