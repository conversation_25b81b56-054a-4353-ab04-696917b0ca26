<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\CompanyPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any company', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new CompanyPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_company',
    'view' => 'view_company',
    'create' => 'create_company',
    'update' => 'update_company',
    'delete' => 'delete_company',
    'deleteAny' => 'delete_any_company',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} company", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new CompanyPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} company", function () use ($method) {
        $user = User::factory()->create();

        $policy = new CompanyPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
