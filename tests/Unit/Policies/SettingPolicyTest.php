<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\SettingPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any sender', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new SettingPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_setting',
    'view' => 'view_setting',
    'update' => 'update_setting',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} setting", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new SettingPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} setting", function () use ($method) {
        $user = User::factory()->create();

        $policy = new SettingPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
