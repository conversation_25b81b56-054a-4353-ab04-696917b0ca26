<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\CityPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any city', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new CityPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_city',
    'view' => 'view_city',
    'create' => 'create_city',
    'update' => 'update_city',
    'delete' => 'delete_city',
    'deleteAny' => 'delete_any_city',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} city", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new CityPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} city", function () use ($method) {
        $user = User::factory()->create();

        $policy = new CityPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
