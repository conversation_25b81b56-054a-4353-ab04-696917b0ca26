<?php

declare(strict_types=1);

use App\Models\CampaignMessage;
use App\Models\User;
use App\Policies\CampaignMessagePolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// viewAny tests
it('allows user with permission to view any campaign message', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new CampaignMessagePolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

it('denies user without permission to view any campaign message', function () {
    $user = User::factory()->create();

    $policy = new CampaignMessagePolicy();

    expect($policy->viewAny($user))->toBeFalse();
});

// view tests
it('allows user with permission to view campaign message', function () {
    $user = User::factory()->create();
    $user->givePermissionTo('view_campaign::message');

    $policy = new CampaignMessagePolicy();

    expect($policy->view($user))->toBeTrue();
});

it('denies user without permission to view campaign message', function () {
    $user = User::factory()->create();

    $policy = new CampaignMessagePolicy();

    expect($policy->view($user))->toBeFalse();
});

// create tests
it('allows user with permission to create campaign message', function () {
    $user = User::factory()->create();
    $user->givePermissionTo('create_campaign::message');

    $policy = new CampaignMessagePolicy();

    expect($policy->create($user))->toBeTrue();
});

it('denies user without permission to create campaign message', function () {
    $user = User::factory()->create();

    $policy = new CampaignMessagePolicy();

    expect($policy->create($user))->toBeFalse();
});

// update tests
it('allows user with permission to update campaign message with pending status', function () {
    $user = User::factory()->create();
    $user->givePermissionTo('update_campaign::message');

    $campaignMessage = new CampaignMessage();
    $campaignMessage->status = 'pending';

    $policy = new CampaignMessagePolicy();

    expect($policy->update($user, $campaignMessage))->toBeTrue();
});

it('denies user without permission to update campaign message', function () {
    $user = User::factory()->create();
    $campaignMessage = new CampaignMessage();

    $policy = new CampaignMessagePolicy();

    expect($policy->update($user, $campaignMessage))->toBeFalse();
});

// delete tests
it('allows user with permission to delete campaign message with pending status', function () {
    $user = User::factory()->create();
    $user->givePermissionTo('delete_campaign::message');

    $campaignMessage = new CampaignMessage();
    $campaignMessage->status = 'pending';

    $policy = new CampaignMessagePolicy();

    expect($policy->delete($user, $campaignMessage))->toBeTrue();
});

it('denies user without permission to delete campaign message', function () {
    $user = User::factory()->create();
    $campaignMessage = new CampaignMessage();

    $policy = new CampaignMessagePolicy();

    expect($policy->delete($user, $campaignMessage))->toBeFalse();
});

// deleteAny tests
it('allows user with permission to delete any campaign message', function () {
    $user = User::factory()->create();
    $user->givePermissionTo('delete_any_campaign::message');

    $policy = new CampaignMessagePolicy();

    expect($policy->deleteAny($user))->toBeTrue();
});

it('denies user without permission to delete any campaign message', function () {
    $user = User::factory()->create();

    $policy = new CampaignMessagePolicy();

    expect($policy->deleteAny($user))->toBeFalse();
});
