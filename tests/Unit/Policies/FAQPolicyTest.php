<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\FAQPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

$abilities = [
    'viewAny' => 'view_any_faq',
    'view' => 'view_faq',
    'create' => 'create_faq',
    'update' => 'update_faq',
    'delete' => 'delete_faq',
    'deleteAny' => 'delete_any_faq',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} FAQ", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new FAQPolicy();

        $args = [$user];

        if (in_array($method, ['view', 'update', 'delete'])) {
            $faq = App\Models\FAQ::factory()->create();
            $args[] = $faq;
        }

        expect($policy->{$method}(...$args))->toBeTrue();
    });

    it("denies user without permission to {$method} FAQ", function () use ($method) {
        $user = User::factory()->create();

        $policy = new FAQPolicy();

        $args = [$user];

        if (in_array($method, ['view', 'update', 'delete'])) {
            $faq = App\Models\FAQ::factory()->create();
            $args[] = $faq;
        }

        expect($policy->{$method}(...$args))->toBeFalse();
    });
}
