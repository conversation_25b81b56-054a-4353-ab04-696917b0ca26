<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\ContactGroupPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

$abilities = [
    'viewAny' => 'view_any_contact::group',
    'view' => 'view_contact::group',
    'create' => 'create_contact::group',
    'update' => 'update_contact::group',
    'delete' => 'delete_contact::group',
    'deleteAny' => 'delete_any_contact::group',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method}", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new ContactGroupPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method}", function () use ($method) {
        $user = User::factory()->create();

        $policy = new ContactGroupPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
