<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\TransactionPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any transaction', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new TransactionPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_transaction',
    'view' => 'view_transaction',
    'create' => 'create_transaction',
    'update' => 'update_transaction',
    'delete' => 'delete_transaction',
    'deleteAny' => 'delete_any_transaction',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} transaction", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new TransactionPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} transaction", function () use ($method) {
        $user = User::factory()->create();

        $policy = new TransactionPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
