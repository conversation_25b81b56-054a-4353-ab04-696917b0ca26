<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\UserPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any user', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new UserPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_user',
    'view' => 'view_user',
    'create' => 'create_user',
    'update' => 'update_user',
    'delete' => 'delete_user',
    'deleteAny' => 'delete_any_user',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} user", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new UserPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} user", function () use ($method) {
        $user = User::factory()->create();

        $policy = new UserPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
