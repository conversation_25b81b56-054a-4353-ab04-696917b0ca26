<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\PlanPolicy;

use function Pest\Laravel\seed;

beforeEach(function (): void {
    seed();
});

// Special test for super_admin role
it('allows super_admin to view any plan', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new PlanPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

$abilities = [
    'viewAny' => 'view_any_plan',
    'view' => 'view_plan',
    'create' => 'create_plan',
    'update' => 'update_plan',
    'delete' => 'delete_plan',
    'deleteAny' => 'delete_any_plan',
];

foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} plan", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new PlanPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} plan", function () use ($method) {
        $user = User::factory()->create();

        $policy = new PlanPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}
