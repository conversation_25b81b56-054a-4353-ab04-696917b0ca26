<?php

declare(strict_types=1);

namespace Tests\Unit\Filament\Admin\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use App\Models\Role;
use App\Models\Sender;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Hash;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    $company = Company::factory()->create();

    get(
        CompanyResource::getUrl('manage-users', ['record' => $company]),
    )->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(
        CompanyResource::getUrl('manage-users', ['record' => $company]),
    )->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(
        CompanyResource::getUrl('manage-users', ['record' => $company]),
    )->assertOk();
});

it('can list company users', function () {
    $company = Company::factory()->create();
    $companyUsers = User::factory()->count(3)->create();
    $company->users()->attach($companyUsers);

    $otherUsers = User::factory()->count(2)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])
        ->assertCanSeeTableRecords($companyUsers)
        ->assertCanNotSeeTableRecords($otherUsers);
});

it('can create new user for company', function () {
    $company = Company::factory()->create();
    $newUserData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $component = livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ]);

    $component->callTableAction('create', data: $newUserData);

    $user = User::where('email', '<EMAIL>')->first();
    $user->assignRole('company_owner');

    assertDatabaseHas('company_user', [
        'company_id' => $company->id,
        'user_id' => $user->id,
    ]);

    expect($user)
        ->not->toBeNull()
        ->and($user->name)
        ->toBe('John Doe')
        ->and(Hash::check('password123', $user->password))
        ->toBeTrue();

    // Load fresh models
    $company = $company->fresh();
    $user = $user->fresh();

    // Compare IDs instead of objects
    expect($company->users->pluck('id')->toArray())->toContain($user->id);

    expect($user->roles->pluck('name'))->toContain('company_owner');
});

it('can attach existing user to company', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->callTableAction(
        'attach',
        data: [
            'recordId' => $user->id,
        ],
    );

    assertDatabaseHas('company_user', [
        'company_id' => $company->id,
        'user_id' => $user->id,
    ]);
});

it('can edit company user', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();
    $company->users()->attach($user);

    $updatedData = [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
        'roles' => [Role::where('name', 'company_owner')->first()->id],
    ];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->callTableAction('edit', $user, data: $updatedData);

    expect($user->refresh())
        ->name->toBe('Updated Name')
        ->email->toBe('<EMAIL>');
});

it('can detach user from company', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();
    $company->users()->attach($user);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->callTableAction('detach', $user);

    expect($company->users)->not->toContain($user);
});

it('can set default user for company', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();
    $company->users()->attach($user);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->callTableAction('set_default', $user);

    expect($company->refresh()->default_user_id)->toBe($user->id);
});

it('cannot detach default user from company', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();
    $company->users()->attach($user);
    $company->update(['default_user_id' => $user->id]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->assertTableActionHidden('detach', $user);
});

it('cannot delete default user from company', function () {
    $company = Company::factory()->create();
    $user = User::factory()->create();
    $company->users()->attach($user);
    $company->update(['default_user_id' => $user->id]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->assertTableActionHidden('delete', $user);
});

it('shows correct default user indicator', function () {
    $company = Company::factory()->create();
    $defaultUser = User::factory()->create();
    $regularUser = User::factory()->create();

    $company->users()->attach([$defaultUser->id, $regularUser->id]);
    $company->update(['default_user_id' => $defaultUser->id]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $component = livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ]);

    // Note: You might need to adjust these assertions based on your actual implementation
    // of how you display the default user indicator
    $component->assertSee('default-user-icon');
});

it('shows empty state when company has no users', function () {
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])->assertSee(__('No users found'));
});

it('validates required fields when creating user', function () {
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])
        ->callTableAction(
            'create',
            data: [
                'name' => '',
                'email' => 'invalid-email',
                'password' => 'short',
                'password_confirmation' => 'different',
            ],
        )
        ->assertHasTableActionErrors(['name', 'email', 'password']);
});

it('validates unique email when creating user', function () {
    $company = Company::factory()->create();
    $existingUser = User::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageCompanyUsers::class, [
        'record' => $company->id,
    ])
        ->callTableAction(
            'create',
            data: [
                'name' => 'New User',
                'email' => $existingUser->email,
                'password' => 'password123',
                'password_confirmation' => 'password123',
            ],
        )
        ->assertHasTableActionErrors(['email']);
});

it('shows empty state when company has no transactions', function () {
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ListCompanyTransaction::class, [
        'record' => $company->id,
    ])->assertSee(__('No transactions found'));
});

it('shows empty state when company has no senders', function () {
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->assertSee(__('No sending numbers found'));
});

it('can attach sender to company', function () {
    $company = Company::factory()->create();
    $sender = Sender::factory(['type' => 'multi'])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->callTableAction(
        'attach',
        data: [
            'sender_id' => $sender->id,
            'expired_at' => Carbon::now()->addMonth(),
        ],
    );

    assertDatabaseHas('sender_company', [
        'sender_id' => $sender->id,
        'company_id' => $company->id,
    ]);
});
