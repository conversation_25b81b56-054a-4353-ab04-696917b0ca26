<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\CityResource;
use App\Models\City;
use App\Models\User;
use Filament\Actions\DeleteAction;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertModelMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(
        Filament::getPanel('admin')
    );
});

it('can render page with right permissions', function () {
    City::factory()->count(10)->create();
    get(CityResource::getUrl('index'))->assertRedirect(Filament::getPanel('admin')->getLoginUrl());
    actingAs(User::factory()->create());
    get(CityResource::getUrl('index'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CityResource::getUrl('index'))->assertOk();
});

it('can list cities', function () {
    $cities = City::factory()->count(10)->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(CityResource\Pages\ListCities::class)
        ->assertCanSeeTableRecords($cities);
});

it('can render create page with right permissions', function () {
    get(CityResource::getUrl('create'))->assertRedirect(Filament::getPanel('admin')->getLoginUrl());
    actingAs(User::factory()->create());
    get(CityResource::getUrl('create'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CityResource::getUrl('create'))->assertOk();
});

it('can create city', function () {
    $city = City::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CityResource\Pages\CreateCity::class)
        ->fillForm($city->toArray())
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(City::class, [
        'name' => $city->name,
        'description' => $city->description,
    ]);

});

test('can render edit page with right permissions', function () {
    $city = City::factory()->create();
    get(CityResource::getUrl('edit', ['record' => $city]))->assertRedirect('/admin/login');
    actingAs(User::factory()->create());
    get(CityResource::getUrl('edit', ['record' => $city]))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CityResource::getUrl('edit', ['record' => $city]))->assertOk();
    get(CityResource::getUrl('edit', ['record' => 1]))->assertNotFound();
});

it('can retrieve city', function () {
    $city = City::factory()->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(CityResource\Pages\EditCity::class, [
        'record' => $city->id,
    ])
        ->assertFormSet([
            'name' => $city->name,
            'description' => $city->description,
        ]);
});

it('can update city', function () {
    $city = City::factory()->create();
    $newCity = City::factory()->make();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(CityResource\Pages\EditCity::class, [
        'record' => $city->id,
    ])
        ->fillForm([
            'name' => $newCity->name,
            'description' => $newCity->description,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($city->refresh())
        ->name->toEqual($newCity->name)
        ->description->toEqual($newCity->description);
});

it('can delete city', function () {
    $city = City::factory()->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(CityResource\Pages\EditCity::class, [
        'record' => $city->id,
    ])
        ->callAction(DeleteAction::class);

    assertModelMissing($city);
});
