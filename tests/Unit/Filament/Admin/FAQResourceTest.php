<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\FAQResource;
use App\Filament\Admin\Resources\FAQResource\Pages\CreateFAQ;
use App\Filament\Admin\Resources\FAQResource\Pages\EditFAQ;
use App\Filament\Admin\Resources\FAQResource\Pages\ListFAQS;
use App\Models\FAQ;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Livewire\livewire;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test data if needed
});

it('can render page with right permissions', function () {
    get(FAQResource::getUrl('index'))->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    // Check if the user has the right permissions
    // Note: In this application, regular users might have access to FAQResource
    // So we're only testing the redirect for unauthenticated users and success for admin users

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(FAQResource::getUrl('index'))->assertOk();
});

it('can list FAQs', function () {
    $faqs = FAQ::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(ListFAQS::class)
        ->assertCanSeeTableRecords($faqs);
});

it('can create a FAQ', function () {
    $faq = FAQ::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CreateFAQ::class)
        ->fillForm([
            'question' => $faq->question,
            'answer' => $faq->answer,
            'is_active' => $faq->is_active,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $this->assertDatabaseHas(FAQ::class, [
        'question' => $faq->question,
        'answer' => $faq->answer,
        'is_active' => $faq->is_active,
    ]);
});

it('can edit a FAQ', function () {
    $faq = FAQ::factory()->create();
    $newData = FAQ::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(EditFAQ::class, [
        'record' => $faq->id,
    ])
        ->fillForm([
            'question' => $newData->question,
            'answer' => $newData->answer,
            'is_active' => $newData->is_active,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    $this->assertDatabaseHas(FAQ::class, [
        'id' => $faq->id,
        'question' => $newData->question,
        'answer' => $newData->answer,
        'is_active' => $newData->is_active,
    ]);
});

it('validates required fields when creating a FAQ', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CreateFAQ::class)
        ->fillForm([
            'question' => '',
            'answer' => '',
        ])
        ->call('create')
        ->assertHasFormErrors(['question', 'answer']);
});

it('has the correct navigation group', function () {
    expect(FAQResource::getNavigationGroup())
        ->toBe(__('Settings'));
});

it('has the correct label', function () {
    expect(FAQResource::getLabel())
        ->toBe(__('FAQ'));
});

it('has the correct plural label', function () {
    expect(FAQResource::getPluralModelLabel())
        ->toBe(__('FAQ'));
});
