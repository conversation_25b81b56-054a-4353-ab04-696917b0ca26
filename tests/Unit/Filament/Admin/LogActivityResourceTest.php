<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\LogActivityResource;
use App\Models\LogActivity;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertModelMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(LogActivityResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(LogActivityResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(LogActivityResource::getUrl('index'))->assertOk();
});

it('can list log activities', function () {
    // Create a user to generate some activity
    $user = User::factory()->create()->assignRole('super_admin');

    // Generate some activity by logging in
    actingAs($user);

    // Get the log activities
    $logActivities = LogActivity::limit(5)->get();

    // Ensure we have some log activities to test with
    if ($logActivities->isEmpty()) {
        // Create a test activity log if none exists
        activity()
            ->causedBy($user)
            ->log('Test activity');

        $logActivities = LogActivity::limit(5)->get();
    }

    actingAs($user);
    livewire(LogActivityResource\Pages\ListLogActivities::class)
        ->assertCanSeeTableRecords($logActivities);
});

// Testing the policy for viewing any log activities
it('can authorize viewAny policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->viewAny($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->viewAny($regularUser))->toBeFalse();
});

// Testing the policy for viewing log activities
it('can authorize view policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->view($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->view($regularUser))->toBeFalse();
});

// Testing the policy for creating log activities
it('can authorize create policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->create($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->create($regularUser))->toBeFalse();
});

// Testing the policy for updating log activities
it('can authorize update policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->update($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->update($regularUser))->toBeFalse();
});

// Testing the policy for deleting log activities
it('can authorize delete policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->delete($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->delete($regularUser))->toBeFalse();
});

it('can delete multiple log activities', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    // Create test activities
    activity()->causedBy($user)->log('Test activity 1');
    activity()->causedBy($user)->log('Test activity 2');

    $logActivities = LogActivity::latest()->limit(2)->get();
    $logActivityIds = $logActivities->pluck('id')->toArray();

    actingAs($user);
    livewire(LogActivityResource\Pages\ListLogActivities::class)
        ->callTableBulkAction('delete', $logActivityIds);

    foreach ($logActivities as $logActivity) {
        assertModelMissing($logActivity);
    }
});

// Testing the policy for deleting any log activities
it('can authorize deleteAny policy', function () {
    $user = User::factory()->create()->assignRole('super_admin');
    $policy = new App\Policies\LogActivityPolicy();

    expect($policy->deleteAny($user))->toBeTrue();

    // Test with a user without the right permission
    $regularUser = User::factory()->create();
    expect($policy->deleteAny($regularUser))->toBeFalse();
});

it('shows correct columns in the table', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    actingAs($user);
    livewire(LogActivityResource\Pages\ListLogActivities::class)
        ->assertCanRenderTableColumn('log_name')
        ->assertCanRenderTableColumn('subject.name')
        ->assertCanRenderTableColumn('event')
        ->assertCanRenderTableColumn('description')
        ->assertCanRenderTableColumn('causer.name')
        ->assertCanRenderTableColumn('created_at');
});

it('has the correct navigation group', function () {
    expect(LogActivityResource::getNavigationGroup())
        ->toBe(__('filament-shield::filament-shield.nav.group'));
});

it('has the correct label', function () {
    expect(LogActivityResource::getLabel())
        ->toBe(__('Log Activity'));
});

it('has the correct plural label', function () {
    expect(LogActivityResource::getPluralModelLabel())
        ->toBe(__('Log Activities'));
});
