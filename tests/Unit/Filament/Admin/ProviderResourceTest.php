<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\ProviderResource;
use App\Models\Provider;
use App\Models\User;
use Filament\Actions\DeleteAction;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertModelMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(
        Filament::getPanel('admin')
    );
});

it('can render page with right permissions', function () {
    Provider::factory()->count(10)->create();
    get(ProviderResource::getUrl('index'))
        ->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(ProviderResource::getUrl('index'))
        ->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(ProviderResource::getUrl('index'))
        ->assertOk();
});

// FIXME: This test is failing because the table is not being rendered
it('can list providers', function () {
    $providers = Provider::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(ProviderResource\Pages\ListProviders::class)
        ->assertCanSeeTableRecords($providers);
});

it('can render create page with right permissions', function () {
    get(ProviderResource::getUrl('create'))
        ->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(ProviderResource::getUrl('create'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(ProviderResource::getUrl('create'))->assertOk();
});

it('can create provider', function () {
    $provider = Provider::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(ProviderResource\Pages\CreateProvider::class)
        ->fillForm($provider->toArray())
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Provider::class, [
        'name' => $provider->name,
        'port' => $provider->port,
        'system_type' => $provider->system_type,
    ]);
});

test('can render edit page with right permissions', function () {
    $provider = Provider::factory()->create();

    get(ProviderResource::getUrl('edit', ['record' => $provider]))
        ->assertRedirect('/admin/login');

    actingAs(User::factory()->create());
    get(ProviderResource::getUrl('edit', ['record' => $provider]))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(ProviderResource::getUrl('edit', ['record' => $provider]))->assertOk();
    get(ProviderResource::getUrl('edit', ['record' => 1]))->assertNotFound();
});

it('can retrieve provider', function () {
    $provider = Provider::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(ProviderResource\Pages\EditProvider::class, [
        'record' => $provider->id,
    ])
        ->assertFormSet([
            'name' => $provider->name,
            'port' => $provider->port,
        ]);
});

it('can update provider', function () {
    $provider = Provider::factory()->create();
    $newprovider = Provider::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(ProviderResource\Pages\EditProvider::class, [
        'record' => $provider->id,
    ])
        ->fillForm([
            'name' => $newprovider->name,
            'host' => $newprovider->port,
            'port' => $newprovider->port,
            'system_id' => $newprovider->system_id,
            'password' => $newprovider->password,
            'system_type' => $newprovider->system_type,
            'connection_timeout' => $newprovider->connection_timeout,
            'enquire_link_interval' => $newprovider->enquire_link_interval,
            'status' => $newprovider->status,
            'pattern' => $newprovider->pattern,
            'smpp_pattern' => $newprovider->smpp_pattern,
            'smpp_pattern_replace' => $newprovider->smpp_pattern_replace,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($provider->refresh())
        ->name->toEqual($newprovider->name)
        ->port->toEqual($newprovider->port)
        ->system_type->toEqual($newprovider->system_type);
});

it('can delete provide', function () {
    $provider = Provider::factory()->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(ProviderResource\Pages\EditProvider::class, [
        'record' => $provider->id,
    ])
        ->callAction(DeleteAction::class);

    assertModelMissing($provider);
});
