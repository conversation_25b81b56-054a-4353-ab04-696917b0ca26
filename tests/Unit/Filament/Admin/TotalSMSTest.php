<?php

declare(strict_types=1);

use App\Filament\Admin\Widgets\TotalSMS;
use App\Models\MessageReceipt;
use Filament\Support\RawJs;
use Illuminate\Support\Carbon;

use function Pest\Laravel\travelTo;

beforeEach(function () {
    // Fix current time to have predictable test output
    travelTo(Carbon::parse('2024-01-15'));
});

it('returns correct data structure for all statuses', function () {
    // Create dummy data over the past 15 days
    foreach (range(0, 14) as $day) {
        $date = now()->subDays(14 - $day);
        MessageReceipt::factory()->count(2)->create([
            'status' => 'pending',
            'created_at' => $date,
        ]);
        MessageReceipt::factory()->count(3)->create([
            'status' => 'sent',
            'created_at' => $date,
        ]);
    }

    $widget = new TotalSMS();

    $reflectionMethod = new ReflectionMethod($widget, 'getData');
    $data = $reflectionMethod->invoke($widget);

    expect($data)->toHaveKeys(['labels', 'datasets']);

    expect($data['labels'])->toHaveCount(15);

    $pendingDataset = collect($data['datasets'])->firstWhere('label', __('pending'));
    $sentDataset = collect($data['datasets'])->firstWhere('label', __('sent'));

    expect($pendingDataset['data'])->toHaveCount(15);
    expect($sentDataset['data'])->toHaveCount(15);

    // Assert a specific day count
    expect($pendingDataset['data'][0])->toBe(2);
    expect($sentDataset['data'][0])->toBe(3);
});

it('returns correct data structure for a single status filter', function () {
    // Create data for 'failed' status only
    foreach (range(0, 14) as $day) {
        MessageReceipt::factory()->count(1)->create([
            'status' => 'failed',
            'created_at' => now()->subDays(14 - $day),
        ]);
    }

    $widget = new TotalSMS();
    $widget->filter = 'failed';

    $reflectionMethod = new ReflectionMethod($widget, 'getData');
    $data = $reflectionMethod->invoke($widget);

    expect($data['datasets'])->toHaveCount(1);

    $failedDataset = $data['datasets'][0];

    expect($failedDataset['label'])->toBe(__('failed'));
    expect($failedDataset['data'])->toBeArray()->toHaveCount(15);
    expect($failedDataset['data'][0])->toBe(1);
});

it('returns correct heading and description', function () {
    $widget = new TotalSMS();

    expect($widget->getHeading())->toBe(__('Total Messages'));
    expect($widget->getDescription())->toBe(__('Total Messages in the last 15 days'));
});

it('returns correct filters', function () {
    $widget = new TotalSMS();
    $filters = (new ReflectionMethod($widget, 'getFilters'))->invoke($widget);

    expect($filters)->toBeArray()
        ->toHaveKeys(['all', 'pending', 'sent', 'delivered', 'failed'])
        ->all->toBeString();
});

it('handles empty data correctly', function () {
    // No message receipts in the database
    $widget = new TotalSMS();
    $reflectionMethod = new ReflectionMethod($widget, 'getData');
    $data = $reflectionMethod->invoke($widget);

    expect($data)->toHaveKeys(['labels', 'datasets'])
        ->and($data['labels'])->toHaveCount(15)
        ->and($data['datasets'])->toHaveCount(4);

    // Check that all datasets have zero counts
    foreach ($data['datasets'] as $dataset) {
        expect($dataset['data'])->toBeArray()
            ->toHaveCount(15)
            ->each->toBe(0);
    }
});

it('uses correct colors for different statuses', function () {
    // Create some data to trigger dataset creation
    MessageReceipt::factory()->create(['status' => 'pending']);
    MessageReceipt::factory()->create(['status' => 'sent']);
    MessageReceipt::factory()->create(['status' => 'delivered']);
    MessageReceipt::factory()->create(['status' => 'failed']);

    $widget = new TotalSMS();
    $reflectionMethod = new ReflectionMethod($widget, 'getData');
    $data = $reflectionMethod->invoke($widget);

    $expectedColors = [
        'pending' => '#FFC107',
        'sent' => '#8BC34A',
        'delivered' => '#64B5F6',
        'failed' => '#EF5350',
    ];

    foreach ($data['datasets'] as $dataset) {
        $status = $dataset['label'];
        // Convert the label back to its non-translated form for lookup
        foreach (['pending', 'sent', 'delivered', 'failed'] as $statusKey) {
            if (__($statusKey) === $status) {
                $status = $statusKey;
                break;
            }
        }

        $expectedColor = $expectedColors[$status] ?? null;
        expect($dataset['backgroundColor'])->toBe($expectedColor);
    }
});

it('returns correct chart type', function () {
    $widget = new TotalSMS();
    $type = (new ReflectionMethod($widget, 'getType'))->invoke($widget);

    expect($type)->toBe('bar');
});

it('returns RawJs instance for chart options', function () {
    $widget = new TotalSMS();
    $options = (new ReflectionMethod($widget, 'getOptions'))->invoke($widget);

    expect($options)->toBeInstanceOf(RawJs::class);
});
