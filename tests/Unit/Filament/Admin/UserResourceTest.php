<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\UserResource;
use App\Models\Role;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(UserResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(UserResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(UserResource::getUrl('index'))->assertOk();
});

it('can list users', function () {
    $users = User::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(UserResource\Pages\ListUsers::class)
        ->set('tableRecordsPerPage', 20)
        ->assertCanSeeTableRecords($users);
});

it('can render create page with right permissions', function () {
    get(UserResource::getUrl('create'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(UserResource::getUrl('create'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(UserResource::getUrl('create'))->assertOk();
});

it('can create users', function () {
    $user = User::factory()->make();
    $userRole = Role::first();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(UserResource\Pages\CreateUser::class)
        ->fillForm([
            'name' => $user->name,
            'email' => $user->email,
            'password' => $user->password,
            'password_confirmation' => $user->password,
            'roles' => $userRole->id,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(User::class, [
        'name' => $user->name,
        'email' => $user->email,
        'password' => $user->password,
    ]);
});

it('validates required fields when creating user', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(UserResource\Pages\CreateUser::class)
        ->fillForm([
            'name' => null,
            'email' => null,
        ])
        ->call('create')
        ->assertHasFormErrors(['name', 'email']);
});

it('can render edit page with right permissions', function () {
    $user = User::factory()->create();

    get(UserResource::getUrl('edit', ['record' => $user]))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(UserResource::getUrl('edit', ['record' => $user]))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(UserResource::getUrl('edit', ['record' => $user]))->assertOk();
});

it('can retrieve user', function () {
    $user = User::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(UserResource\Pages\EditUser::class, [
        'record' => $user->id,
    ])->assertFormSet([
        'name' => $user->name,
        'email' => $user->email,
    ]);
});

it('can update user', function () {
    $user = User::factory()->create();
    $newUser = User::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(UserResource\Pages\EditUser::class, [
        'record' => $user->id,
    ])
        ->fillForm([
            'name' => $newUser->name,
            'email' => $newUser->email,
            'password' => $newUser->password,
            'password_confirmation' => $newUser->password,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($user->refresh())
        ->name->toEqual($newUser->name)
        ->email->toEqual($newUser->email)
        ->password->toEqual($newUser->password);
});
