<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\SenderResource;
use App\Models\Company;
use App\Models\Provider;
use App\Models\Sender;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;
use Filament\Notifications\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(SenderResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(SenderResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(SenderResource::getUrl('index'))->assertOk();
});

it('can list senders', function () {
    $senders = Sender::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListSenders::class)
        ->set('tableRecordsPerPage', 20)
        ->assertCanSeeTableRecords($senders);
});

it('shows empty state when senders has no items', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListSenders::class)->assertSee(
        __('No senders found'),
    );
});

it('can render create page with right permissions', function () {
    get(SenderResource::getUrl('create'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(SenderResource::getUrl('create'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(SenderResource::getUrl('create'))->assertOk();
});

it('can create sender', function () {
    $sender = Sender::factory()->make();
    $provider = Provider::first();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    livewire(SenderResource\Pages\CreateSender::class)
        ->fillForm([
            'sender' => $sender->sender,
            'category' => $sender->category,
            'type' => $sender->type,
            'status' => $sender->status,
            'notes' => $sender->notes,
            'provider' => [
                [
                    'provider_id' => $provider->id,
                    'expired_at' => Carbon::now()->addMonth(),
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    assertDatabaseHas(Sender::class, [
        'sender' => $sender->sender,
        'category' => $sender->category,
        'type' => $sender->type,
        'status' => $sender->status,
        'notes' => $sender->notes,
    ]);

    // Assert representative was created
    $sender = Sender::where('sender', $sender->sender)->firstOrFail();
    expect($sender->provider)
        ->toHaveCount(1)
        ->and($sender->provider->first())
        ->provider_id->toBe($provider->id);
});

it('can render edit page with right permissions', function () {
    $sender = Sender::factory()->create();

    get(SenderResource::getUrl('edit', ['record' => $sender]))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(
        SenderResource::getUrl('edit', ['record' => $sender]),
    )->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(SenderResource::getUrl('edit', ['record' => $sender]))->assertOk();
});

it('can retrieve sender', function () {
    $sender = Sender::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\EditSender::class, [
        'record' => $sender->id,
    ])->assertFormSet([
        'sender' => $sender->sender,
        'category' => $sender->category,
        'type' => $sender->type,
        'status' => $sender->status,
        'notes' => $sender->notes,
    ]);
});

it('can update sender', function () {
    $sender = Sender::factory()->create();
    $newSender = Sender::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\EditSender::class, [
        'record' => $sender->id,
    ])
        ->fillForm([
            'sender' => $newSender->sender,
            'category' => $newSender->category,
            'type' => $newSender->type,
            'status' => $newSender->status,
            'notes' => $newSender->notes,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($sender->refresh())
        ->sender->toEqual($newSender->sender)
        ->category->toEqual($newSender->category)
        ->type->toEqual($newSender->type)
        ->status->toEqual($newSender->status)
        ->notes->toEqual($newSender->notes);
});

it('shows empty state when associated companies has no company', function () {
    $sender = Sender::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->assertSee(__('No Companies found'));
});

it('can list associated companies.', function () {
    $sender = Sender::factory()->create();
    $senderCompanies = Company::factory()->count(5)->create();
    $sender->companies()->attach($senderCompanies);

    $otherCompanies = Company::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])
        ->assertCanSeeTableRecords($senderCompanies)
        ->assertCanNotSeeTableRecords($otherCompanies);
});

it('can attach new company to sender', function () {
    $company = Company::factory()->create();
    $sender = Sender::factory(['type' => 'multi'])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction(
        'attach',
        data: [
            'company_id' => $company->id,
            'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
        ],
    );

    assertDatabaseHas('sender_company', [
        'company_id' => $company->id,
        'sender_id' => $sender->id,
    ]);
});

it('validates required fields when creating sender', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\CreateSender::class)
        ->fillForm([
            'sender' => null,
            'category' => null,
            'type' => null,
            'provider' => [
                [
                    'provider_id' => null,
                ],
            ],
        ])
        ->call('create')
        ->assertHasFormErrors([
            'sender',
            'category',
            'type',
            'provider.0.provider_id',
        ]);
});

it('validates required fields when updating sender', function () {
    $sender = Sender::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\EditSender::class, [
        'record' => $sender->id,
    ])
        ->fillForm([
            'sender' => null,
            'category' => null,
            'type' => null,
            'provider' => [
                [
                    'provider_id' => null,
                ],
            ],
        ])
        ->call('save')
        ->assertHasFormErrors([
            'sender',
            'category',
            'type',
            'provider.0.provider_id',
        ]);
});

it('can detach company from sender', function () {
    $company = Company::factory()->create();
    $sender = Sender::factory(['type' => 'multi'])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Attach the company first
    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction(
        'attach',
        data: [
            'company_id' => $company->id,
            'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
        ],
    );

    assertDatabaseHas('sender_company', [
        'company_id' => $company->id,
        'sender_id' => $sender->id,
    ]);

    // Detach the company
    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction('detach', $company->id);

    assertDatabaseMissing('sender_company', [
        'company_id' => $company->id,
        'sender_id' => $sender->id,
    ]);
});

it('can update the expiration date of a company', function () {
    $sender = Sender::factory()->create(['type' => 'multi']);
    $company = Company::factory()->create();
    $sender->companies()->attach($company->id, ['expired_at' => now()->subDay()]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction('edit-expiration', $company->id,
        data: [
            'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
        ],
    )->assertNotified(__('Success'));

    $this->assertDatabaseHas('sender_company', [
        'company_id' => $company->id,
        'sender_id' => $sender->id,
        'expired_at' => now()->addMonth()->format('Y-m-d'),
    ]);
});

it('prevents attaching sender to multiple companies if type is not multi', function () {
    $sender = Sender::factory()->create(['type' => 'private']);
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Attach the company
    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction(
        'attach',
        data: [
            'company_id' => $company->id,
            'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
        ],
    )->assertNotified(__('Error'));
});

it('cannot attach a sender to a company', function () {
    $sender = Sender::factory()->create(['type' => 'multi']);
    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $sender->companies()->attach($company->id, [
        'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
    ]);

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction(
        'attach',
        data: [
            'company_id' => $company->id,
            'expired_at' => Carbon::now()->addYear()->format('Y-m-d'),
        ],
    )->assertNotified(
        Notification::make()
            ->title(__('Error'))
            ->body(__('The sender could not be attached to the company'))
            ->status('danger'),
    );
});

it('shows provider name in itemLabel in repeater', function () {
    $provider = Provider::factory()->create(['name' => 'Demo Provider']);

    $sender = Sender::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\EditSender::class, [
        'record' => $sender->id,
    ])
        ->fillForm([
            'provider' => [
                [
                    'id' => 1,
                    'provider_id' => $provider->id,
                    'expired_at' => now()->addMonth()->format('Y-m-d'),
                ],
            ],
        ])
        ->call('save')
        ->assertHasNoFormErrors();
});

it('can see the pending status for sender', function () {
    $sender = Sender::factory()->count(5)->create([
        'status' => 'active',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListSenders::class)
        ->assertCanSeeTableRecords($sender);
});

it('see notification if sender type not multi', function () {
    $sender = Sender::factory()->create([
        'type' => 'private',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SenderResource\Pages\ListAssociatedCompany::class, [
        'record' => $sender->id,
    ])->callTableAction(
        'attach',
        data: [
            'company_id' => 1,
            'expired_at' => Carbon::now()->addMonth()->format('Y-m-d'),
        ],
    )
        ->assertNotified();
});

it('can see the status for sender', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    $sender = Sender::factory(['status' => 'active'])->create();

    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $sender->id,
    ])->assertSeeHtmlInOrder(['success', __('active')]);

    $sender->update(['status' => 'rejected']);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $sender->id,
    ])->assertSeeHtmlInOrder(['danger', __('rejected')]);

    $sender->update(['status' => 'pending']);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $sender->id,
    ])->assertSeeHtmlInOrder(['warning', __('pending')]);
});
