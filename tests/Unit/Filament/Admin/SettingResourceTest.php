<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\SettingResource;
use App\Models\Plan;
use App\Models\Sender;
use App\Models\Setting;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    // Create some settings
    createTestSettings();

    get(SettingResource::getUrl('index'))->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(SettingResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(SettingResource::getUrl('index'))->assertOk();
});

it('can list settings', function () {
    $settings = createTestSettings();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->assertCanSeeTableRecords($settings);
});

it('can open edit modal with right permissions', function () {
    $setting = createTestSettings()->first();

    // Test without authentication
    get(SettingResource::getUrl('index'))->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    // Test with regular user (should not see settings)
    actingAs(User::factory()->create());
    get(SettingResource::getUrl('index'))->assertForbidden();

    // Test with super admin
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->assertCanSeeTableRecords([$setting]);
});

it('can retrieve setting in edit modal', function () {
    $setting = createTestSettings()->where('key', 'single_sms_cost')->first();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->mountTableAction('edit', $setting)
        ->assertTableActionDataSet([
            'value' => $setting->value,
        ]);
});

it('can update text input setting via modal', function () {
    $setting = createTestSettings()->where('key', 'single_sms_cost')->first();
    $newValue = '300';

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => $newValue,
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toEqual($newValue);
});

it('can update free_plan setting with multiple plans', function () {
    $plans = Plan::factory()->count(3)->create();
    $setting = Setting::create([
        'key' => 'free_plan',
        'value' => [],
    ]);

    $selectedPlans = $plans->take(2)->pluck('id')->toArray();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => $selectedPlans,
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toEqual($selectedPlans);
});

it('can update is_free_plan setting', function () {
    $setting = Setting::create([
        'key' => 'is_free_plan',
        'value' => false,
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => 1, // Yes
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toBeTrue();
});

it('can update default_sender setting', function () {
    $sender = Sender::factory()->create();
    $setting = Setting::create([
        'key' => 'default_sender',
        'value' => '',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => $sender->id,
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toEqual($sender->id);
});

it('validates free_plan setting requires at least one plan', function () {
    Plan::factory()->count(3)->create();
    $setting = Setting::create([
        'key' => 'free_plan',
        'value' => [],
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => [], // Empty array should fail validation
        ])
        ->assertHasTableActionErrors(['value']);
});

it('validates free_plan setting allows maximum two plans', function () {
    $plans = Plan::factory()->count(4)->create();
    $setting = Setting::create([
        'key' => 'free_plan',
        'value' => [],
    ]);

    $selectedPlans = $plans->pluck('id')->toArray(); // All 4 plans

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => $selectedPlans, // More than 2 plans should fail
        ])
        ->assertHasTableActionErrors(['value']);
});

it('validates required fields for all setting types', function () {
    $settings = [
        Setting::create(['key' => 'single_sms_cost', 'value' => '235']),
        Setting::create(['key' => 'is_free_plan', 'value' => false]),
        Setting::create(['key' => 'default_sender', 'value' => '']),
    ];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    foreach ($settings as $setting) {
        livewire(SettingResource\Pages\ListSettings::class)
            ->callTableAction('edit', $setting, data: [
                'value' => '', // Empty value should fail validation
            ])
            ->assertHasTableActionErrors(['value']);
    }
});

it('displays formatted values in table for different setting types', function () {
    // Create plans and sender for testing
    $plans = Plan::factory()->count(2)->create();
    $sender = Sender::factory()->create();

    // Create settings with different types
    $freePlanSetting = Setting::create([
        'key' => 'free_plan',
        'value' => $plans->pluck('id')->toArray(),
    ]);

    $isFreePlanSetting = Setting::create([
        'key' => 'is_free_plan',
        'value' => true,
    ]);

    $defaultSenderSetting = Setting::create([
        'key' => 'default_sender',
        'value' => $sender->id,
    ]);

    $costSetting = Setting::create([
        'key' => 'single_sms_cost',
        'value' => '235',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $component = livewire(SettingResource\Pages\ListSettings::class);

    // Test that the table shows the settings
    $component->assertCanSeeTableRecords([
        $freePlanSetting,
        $isFreePlanSetting,
        $defaultSenderSetting,
        $costSetting,
    ]);
});

it('can edit setting and see form fields change based on key', function () {
    $plans = Plan::factory()->count(3)->create();
    $sender = Sender::factory()->create();

    // Test free_plan setting shows select with multiple options
    $freePlanSetting = Setting::create([
        'key' => 'free_plan',
        'value' => [],
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Test that we can open the edit modal for different setting types
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $freePlanSetting);

    // Test is_free_plan setting
    $isFreePlanSetting = Setting::create([
        'key' => 'is_free_plan',
        'value' => false,
    ]);

    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $isFreePlanSetting);

    // Test default_sender setting
    $defaultSenderSetting = Setting::create([
        'key' => 'default_sender',
        'value' => '',
    ]);

    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $defaultSenderSetting);

    // Test other settings show text input
    $textSetting = Setting::create([
        'key' => 'single_sms_cost',
        'value' => '235',
    ]);

    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $textSetting);
});

it('handles edge cases for value formatting', function () {
    $sender = Sender::factory()->create();

    // Test with zero values
    $zeroSetting = Setting::create([
        'key' => 'single_sms_cost',
        'value' => '0',
    ]);

    // Test with non-numeric values for cost settings
    $nonNumericSetting = Setting::create([
        'key' => 'multiple_sms_cost',
        'value' => 'invalid',
    ]);

    // Test with valid sender
    $senderSetting = Setting::create([
        'key' => 'default_sender',
        'value' => $sender->id,
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $component = livewire(SettingResource\Pages\ListSettings::class);

    // Should handle zero values
    $component->assertSeeText('0 '.__('LYD'));

    // Should handle non-numeric values gracefully
    $component->assertCanSeeTableRecords([
        $zeroSetting,
        $nonNumericSetting,
        $senderSetting,
    ]);
});

it('can handle DynamicCast for free_plan setting', function () {
    $plans = Plan::factory()->count(2)->create();

    // Create setting with array value (should be JSON encoded by DynamicCast)
    $setting = Setting::create([
        'key' => 'free_plan',
        'value' => $plans->pluck('id')->toArray(),
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Test that the setting can be retrieved and edited
    livewire(SettingResource\Pages\ListSettings::class)
        ->mountTableAction('edit', $setting)
        ->assertTableActionDataSet([
            'value' => $plans->pluck('id')->toArray(),
        ]);

    // Test updating with new plans
    $newPlans = Plan::factory()->count(2)->create();

    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => $newPlans->pluck('id')->toArray(),
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toEqual($newPlans->pluck('id')->toArray());
});

it('can handle DynamicCast for is_free_plan setting', function () {
    // Create setting with boolean value (should be stored as integer by DynamicCast)
    $setting = Setting::create([
        'key' => 'is_free_plan',
        'value' => true,
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Test that the setting can be retrieved and shows correct value
    livewire(SettingResource\Pages\ListSettings::class)
        ->mountTableAction('edit', $setting)
        ->assertTableActionDataSet([
            'value' => 1, // Should be converted to integer for form
        ]);

    // Test updating to false
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $setting, data: [
            'value' => 0, // No
        ])
        ->assertHasNoTableActionErrors();

    expect($setting->refresh()->value)->toBeFalse();
});

it('displays translated labels and navigation correctly', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Test navigation group
    expect(SettingResource::getNavigationGroup())->toBe(__('Settings'));

    // Test labels
    expect(SettingResource::getPluralModelLabel())->toBe(__('Settings'));
    expect(SettingResource::getLabel())->toBe(__('Settings'));

    // Test that the page shows translated content
    get(SettingResource::getUrl('index'))
        ->assertOk()
        ->assertSeeText(__('Settings'));
});

it('can handle settings with custom keys not in predefined list', function () {
    // Create a setting with a custom key
    $customSetting = Setting::create([
        'key' => 'custom_setting',
        'value' => 'custom_value',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Should be able to list the custom setting
    livewire(SettingResource\Pages\ListSettings::class)
        ->assertCanSeeTableRecords([$customSetting]);

    // Should be able to edit with text input (default case)
    livewire(SettingResource\Pages\ListSettings::class)
        ->mountTableAction('edit', $customSetting)
        ->assertTableActionDataSet([
            'value' => 'custom_value',
        ]);

    // Test updating the custom setting
    livewire(SettingResource\Pages\ListSettings::class)
        ->callTableAction('edit', $customSetting, data: [
            'value' => 'updated_custom_value',
        ])
        ->assertHasNoTableActionErrors();

    expect($customSetting->refresh()->value)->toEqual('updated_custom_value');
});

// Helper function to create test settings
function createTestSettings()
{
    return collect([
        Setting::updateOrCreate(['key' => 'single_sms_cost', 'value' => '235']),
        Setting::updateOrCreate(['key' => 'multiple_sms_cost', 'value' => '200']),
        Setting::updateOrCreate(['key' => 'multiple_sms_threshold', 'value' => '1000']),
    ]);
}
