<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\TransactionResource;
use App\Models\Company;
use App\Models\Transaction;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(TransactionResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(TransactionResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(TransactionResource::getUrl('index'))->assertOk();
});

it('can list transactions', function () {
    $transactions = Transaction::factory()->count(5)->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\ListTransactions::class)
        ->set('tableRecordsPerPage', 20)
        ->assertCanSeeTableRecords($transactions);
});

it('shows empty state when transactions has no items', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\ListTransactions::class)->assertSee(
        __('No transactions found'),
    );
});

it('can render create page with right permissions', function () {
    get(TransactionResource::getUrl('create'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(TransactionResource::getUrl('create'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(TransactionResource::getUrl('create'))->assertOk();
});

it('can create transaction', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit'])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\CreateTransaction::class)
        ->fillForm([
            'amount' => $transaction->amount,
            'action_type' => $transaction->action_type,
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Transaction::class, [
        'amount' => $transaction->amount * 1000,
        'action_type' => $transaction->action_type,
        'company_id' => $transaction->company_id,
        'status' => $transaction->status,
    ]);
});

it('validates required fields when creating transaction', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(TransactionResource\Pages\CreateTransaction::class)
        ->fillForm([
            'amount' => null,
            'action_type' => null,
            'company_id' => null,
        ])
        ->call('create')
        ->assertHasFormErrors(['amount', 'action_type', 'company_id']);
});

it('can render edit page with right permissions', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit'])->create();

    get(
        TransactionResource::getUrl('edit', ['record' => $transaction]),
    )->assertRedirect(Filament::getPanel('admin')->getLoginUrl());
    actingAs(User::factory()->create());
    get(
        TransactionResource::getUrl('edit', ['record' => $transaction]),
    )->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(
        TransactionResource::getUrl('edit', ['record' => $transaction]),
    )->assertOk();
});

it('can retrieve transaction', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit'])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\EditTransaction::class, [
        'record' => $transaction->id,
    ])->assertFormSet([
        'amount' => $transaction->amount,
        'action_type' => $transaction->action_type,
        'company_id' => $transaction->company_id,
        'status' => $transaction->status,
    ]);
});

it('can update transaction', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit'])->create();
    $newTransaction = Transaction::factory([
        'action_type' => 'deposit',
    ])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\EditTransaction::class, [
        'record' => $transaction->id,
    ])
        ->fillForm([
            'amount' => $newTransaction->amount,
            'action_type' => $newTransaction->action_type,
            'company_id' => $newTransaction->company_id,
            'status' => $newTransaction->status,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($transaction->refresh())
        ->amount->toEqual($newTransaction->amount)
        ->action_type->toEqual($newTransaction->action_type)
        ->company_id->toEqual($newTransaction->company_id)
        ->status->toEqual($newTransaction->status);
});

it('can list deposit transactions', function () {

    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'deposit']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can list charge transactions', function () {

    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'charge']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can list withdraw transactions', function () {

    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'withdraw']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('mutates amount to negative for charge transactions', function () {
    $transaction = Transaction::factory([
        'action_type' => 'charge',
        'amount' => 100,
    ])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\CreateTransaction::class)
        ->fillForm([
            'amount' => $transaction->amount,
            'action_type' => $transaction->action_type,
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Transaction::class, [
        'amount' => $transaction->amount * -1000, // Note: multiplied by -1 and 1000 (for conversion)
        'action_type' => $transaction->action_type,
        'company_id' => $transaction->company_id,
        'status' => $transaction->status,
    ]);
});

it('mutates amount to negative for refund transactions with positive amount', function () {
    $transaction = Transaction::factory([
        'action_type' => 'refund',
        'amount' => 100,
    ])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\CreateTransaction::class)
        ->fillForm([
            'amount' => $transaction->amount,
            'action_type' => $transaction->action_type,
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Transaction::class, [
        'amount' => $transaction->amount * -1000, // Note: multiplied by -1 and 1000 (for conversion)
        'action_type' => $transaction->action_type,
        'company_id' => $transaction->company_id,
        'status' => $transaction->status,
    ]);
});

it('keeps amount positive for deposit transactions', function () {
    $transaction = Transaction::factory([
        'action_type' => 'deposit',
        'amount' => 100,
    ])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\CreateTransaction::class)
        ->fillForm([
            'amount' => $transaction->amount,
            'action_type' => $transaction->action_type,
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Transaction::class, [
        'amount' => $transaction->amount * 1000, // Note: only multiplied by 1000 (for conversion)
        'action_type' => $transaction->action_type,
        'company_id' => $transaction->company_id,
        'status' => $transaction->status,
    ]);
});

it('mutates amount to negative when updating to charge transaction', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit', 'amount' => 100])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\EditTransaction::class, [
        'record' => $transaction->id,
    ])
        ->fillForm([
            'amount' => 100,
            'action_type' => 'charge',
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($transaction->refresh())
        ->amount->toBe(-100)
        ->action_type->toBe('charge');
});

it('mutates amount to negative when updating to refund transaction with positive amount', function () {
    $transaction = Transaction::factory(['action_type' => 'deposit', 'amount' => 100])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\EditTransaction::class, [
        'record' => $transaction->id,
    ])
        ->fillForm([
            'amount' => 100,
            'action_type' => 'refund',
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($transaction->refresh())
        ->amount->toBe(-100)
        ->action_type->toBe('refund');
});

it('keeps amount positive when updating to deposit transaction', function () {
    $transaction = Transaction::factory(['action_type' => 'charge', 'amount' => -100])->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(TransactionResource\Pages\EditTransaction::class, [
        'record' => $transaction->id,
    ])
        ->fillForm([
            'amount' => 100,
            'action_type' => 'deposit',
            'company_id' => $transaction->company_id,
            'status' => $transaction->status,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($transaction->refresh())
        ->amount->toBe(100)
        ->action_type->toBe('deposit');
});
