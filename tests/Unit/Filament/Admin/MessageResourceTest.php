<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\MessageResource;
use App\Jobs\MessageJobCreator;
use App\Models\Company;
use App\Models\Message;
use App\Models\User;
use App\Services\Jasmin\JasminClient;
use Filament\Facades\Filament;
use Livewire\Livewire;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function () {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(MessageResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(MessageResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(MessageResource::getUrl('index'))->assertOk();
});

it('shows only pending messages in table', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Create messages with different statuses
    $pendingMessage = Message::factory()->create(['status' => 'pending']);
    $rejectedMessage = Message::factory()->create(['status' => 'rejected']);
    $approvedMessage = Message::factory()->create(['status' => 'approved']);

    Livewire::test(MessageResource\Pages\ListMessages::class)
        ->assertCanSeeTableRecords([$pendingMessage])
        ->assertCanNotSeeTableRecords([$approvedMessage, $rejectedMessage]);
});

it('displays correct columns in table', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    Livewire::test(MessageResource\Pages\ListMessages::class)
        ->assertCanRenderTableColumn('short_message')
        ->assertCanRenderTableColumn('company.name')
        ->assertCanRenderTableColumn('project.name')
        ->assertCanRenderTableColumn('message_type')
        ->assertCanRenderTableColumn('message_consumption')
        ->assertCanRenderTableColumn('status');
});

it('dispatches MessageJobCreator when status is approved', function () {
    // Create a mock message record
    $message = Message::factory()->create(['status' => 'pending']);

    // Simulate the afterStateUpdated callback
    $message->update(['status' => 'approved']);

    if ($message->status === 'approved') {
        $jasminClient = app()->make(JasminClient::class);
        MessageJobCreator::dispatch($message, $jasminClient);
    }

    // Assert the message status was updated
    expect($message->status)->toBe('approved');
});

it('has correct plural model label', function () {
    expect(MessageResource::getPluralModelLabel())->toBe(__('Message Pending'));
});

it('has correct navigation group', function () {
    expect(MessageResource::getNavigationGroup())->toBe(__('Messages'));
});

it('shows correct navigation badge count', function () {
    // Create 3 pending messages
    Message::factory()->count(3)->create(['status' => 'pending']);

    // Create some non-pending messages
    Message::factory()->create(['status' => 'approved']);
    Message::factory()->create(['status' => 'rejected']);

    // Test
    expect(MessageResource::getNavigationBadge())->toBe('3');
});

it('does not dispatch job when message is rejected', function () {
    Queue::fake();

    // Setup
    $message = Message::factory()->create(['status' => 'pending']);

    $message->update(['status' => 'rejected']);

    // Assertions
    $message->refresh();
    expect($message->status)->toBe('rejected');

    Queue::assertNotPushed(MessageJobCreator::class);
});

it('can filter posts by `company_id`', function () {
    actingAs(User::factory()->create()->assignRole('super_admin'));

    $company = Company::factory()->create(['status' => 'active']);
    $messages = Message::factory()->count(10)->create(['status' => 'pending', 'company_id' => $company->id]);

    livewire(MessageResource\Pages\ListMessages::class)
        ->assertCanSeeTableRecords($messages)
        ->filterTable('company_id', $company->id)
        ->assertCanSeeTableRecords($messages->where('company_id', $company->id))
        ->assertCanNotSeeTableRecords($messages->where('company_id', '!=', $company->id));
});
