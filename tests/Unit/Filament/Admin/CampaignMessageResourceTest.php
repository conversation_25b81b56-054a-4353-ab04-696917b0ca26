<?php

declare(strict_types=1);

use App\Models\CampaignMessage;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;

beforeEach(function (): void {
    Filament::setCurrentPanel(
        Filament::getPanel('admin')
    );
});

test('Resource Page Permissions', function () {
    seed();
    CampaignMessage::factory(['status' => 'active'])->create();
    CampaignMessage::factory(['status' => 'pending'])->create();
    CampaignMessage::factory(['status' => 'inactive'])->create();
    CampaignMessage::factory(['status' => 'rejected'])->create();
    get('/admin/campaign-messages')->assertRedirect('/admin/login');
    actingAs(User::factory()->create());
    get('/admin/campaign-messages')->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get('/admin/campaign-messages')->assertOk();
});

test('Create Page Permissions', function () {
    seed();
    get('/admin/campaign-messages/create')->assertRedirect('/admin/login');
    actingAs(User::factory()->create());
    get('/admin/campaign-messages/create')->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get('/admin/campaign-messages/create')->assertOk();
});

test('Edit Page Permissions', function () {
    seed();
    $message = CampaignMessage::factory()->create();
    get('/admin/campaign-messages/'.$message->id.'/edit')->assertRedirect('/admin/login');
    actingAs(User::factory()->create());
    get('/admin/campaign-messages/'.$message->id.'/edit')->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get('/admin/campaign-messages/'.$message->id.'/edit')->assertOk();
    get('/admin/campaign-messages/1/edit')->assertNotFound();
});
