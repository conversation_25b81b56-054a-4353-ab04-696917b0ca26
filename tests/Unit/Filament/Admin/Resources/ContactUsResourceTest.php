<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\ContactUsResource;
use App\Filament\Admin\Resources\ContactUsResource\Pages\ListContactUs;
use App\Models\ContactUs;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Livewire\livewire;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Create permissions if they don't exist
    Permission::firstOrCreate(['name' => 'view_any_contact_us']);
    Permission::firstOrCreate(['name' => 'view_contact_us']);
    Permission::firstOrCreate(['name' => 'delete_contact_us']);
    Permission::firstOrCreate(['name' => 'delete_any_contact_us']);

    // Create admin role with permissions
    $adminRole = Role::firstOrCreate(['name' => 'admin']);
    $adminRole->givePermissionTo([
        'view_any_contact_us',
        'view_contact_us',
        'delete_contact_us',
        'delete_any_contact_us',
    ]);

    // Create admin user
    $this->adminUser = User::factory()->create();
    $this->adminUser->assignRole($adminRole);

    // Create regular user
    $this->regularUser = User::factory()->create();

    // Set the current panel to admin
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    // Unauthenticated user should be redirected to login
    get(ContactUsResource::getUrl('index'))
        ->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    // User without permissions should see forbidden
    actingAs($this->regularUser);
    get(ContactUsResource::getUrl('index'))
        ->assertForbidden();

    // User with permissions should see the page
    actingAs($this->adminUser);
    get(ContactUsResource::getUrl('index'))
        ->assertSuccessful();
});

it('can list contact us entries', function () {
    // Create some contact us entries
    $contactUs = [
        ContactUs::create([
            'name' => 'John Doe',
            'phone' => '00218911234567',
            'subject' => 'Test Subject',
            'email' => '<EMAIL>',
            'message' => 'This is a test message',
        ]),
        ContactUs::create([
            'name' => 'Jane Smith',
            'phone' => '00218922345678',
            'subject' => 'Another Subject',
            'email' => '<EMAIL>',
            'message' => 'This is another test message',
        ]),
    ];

    actingAs($this->adminUser);

    livewire(ListContactUs::class)
        ->assertCanSeeTableRecords($contactUs)
        ->assertCountTableRecords(2);
});

it('shows empty state when no contact us entries exist', function () {
    actingAs($this->adminUser);

    livewire(ListContactUs::class)
        ->assertCountTableRecords(0);
});

it('can view contact us details', function () {
    $contactUs = ContactUs::create([
        'name' => 'John Doe',
        'phone' => '00218911234567',
        'subject' => 'Test Subject',
        'email' => '<EMAIL>',
        'message' => 'This is a test message',
    ]);

    actingAs($this->adminUser);

    get(ContactUsResource::getUrl('view', ['record' => $contactUs]))
        ->assertSuccessful();
});

it('has the correct navigation group', function () {
    expect(ContactUsResource::getNavigationGroup())
        ->toBe(__('Settings'));
});

it('has the correct label', function () {
    expect(ContactUsResource::getLabel())
        ->toBe(__('Contact Us'));
});

it('has the correct plural label', function () {
    expect(ContactUsResource::getPluralModelLabel())
        ->toBe(__('Contact Us'));
});
