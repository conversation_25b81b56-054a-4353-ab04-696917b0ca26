<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use App\Models\Sender;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;
use Filament\Notifications\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    Company::factory()->count(20)->create();
    get(CompanyResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(CompanyResource::getUrl('index'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CompanyResource::getUrl('index'))->assertOk();
});

it('can list companies', function () {
    $companies = Company::factory()->count(5)->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(
        CompanyResource\Pages\ListCompanies::class,
    )->assertCanSeeTableRecords($companies);
});

it('can render create page with right permissions', function () {
    get(CompanyResource::getUrl('create'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(CompanyResource::getUrl('create'))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CompanyResource::getUrl('create'))->assertOk();
});

it('can create company', function () {
    $company = Company::factory()->make();
    $representative = [
        'name' => fake()->name(),
        'phone' => fake()->phoneNumber(),
        'email' => fake()->email(),
        'position' => fake()->jobTitle(),
    ];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    livewire(CompanyResource\Pages\CreateCompany::class)
        ->fillForm([
            'name' => $company->name,
            'phone' => $company->phone,
            'email' => $company->email,
            'category' => $company->category,
            'address' => $company->address,
            'city_id' => $company->city_id,
            'description' => $company->description,
            'status' => $company->status,
            'representatives' => [
                [
                    'name' => $representative['name'],
                    'phone' => $representative['phone'],
                    'email' => $representative['email'],
                    'position' => $representative['position'],
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    assertDatabaseHas(Company::class, [
        'name' => $company->name,
        'phone' => $company->phone,
        'email' => $company->email,
        'category' => $company->category,
        'address' => $company->address,
        'city_id' => $company->city_id,
        'description' => $company->description,
        'status' => $company->status,
    ]);

    // Assert representative was created
    $company = Company::where('name', $company->name)->firstOrFail();
    expect($company->representatives)
        ->toHaveCount(1)
        ->and($company->representatives->first())
        ->name->toBe($representative['name'])
        ->phone->toBe($representative['phone'])
        ->email->toBe($representative['email'])
        ->position->toBe($representative['position']);
});

test('can render edit page with right permissions', function () {
    $company = Company::factory()->create();
    get(
        CompanyResource::getUrl('edit', ['record' => $company]),
    )->assertRedirect(Filament::getPanel('admin')->getLoginUrl());
    actingAs(User::factory()->create());
    get(
        CompanyResource::getUrl('edit', ['record' => $company]),
    )->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(CompanyResource::getUrl('edit', ['record' => $company]))->assertOk();
    get(CompanyResource::getUrl('edit', ['record' => 1]))->assertNotFound();
});

it('can retrieve company', function () {
    $company = Company::factory()->create();
    $representative = $company
        ->representatives()
        ->create([
            'name' => fake()->name(),
            'phone' => fake()->phoneNumber(),
            'email' => fake()->email(),
            'position' => fake()->jobTitle(),
        ])
        ->refresh();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\EditCompany::class, [
        'record' => $company->id,
    ])->assertFormSet([
        'name' => $company->name,
        'phone' => $company->phone,
        'email' => $company->email,
        'category' => $company->category,
        'address' => $company->address,
        'city_id' => $company->city_id,
        'description' => $company->description,
        'status' => $company->status,
        'representatives' => [
            'record-'.$representative->id => [
                'name' => $representative->name,
                'phone' => $representative->phone,
                'email' => $representative->email,
                'position' => $representative->position,
            ],
        ],
    ]);
});

it('can update company', function () {
    $company = Company::factory()->create();
    $newCompany = Company::factory()->make();
    $representative = [
        'name' => fake()->name(),
        'phone' => fake()->phoneNumber(),
        'email' => fake()->email(),
        'position' => fake()->jobTitle(),
    ];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    livewire(CompanyResource\Pages\EditCompany::class, [
        'record' => $company->id,
    ])
        ->fillForm([
            'name' => $newCompany->name,
            'phone' => $newCompany->phone,
            'email' => $newCompany->email,
            'category' => $newCompany->category,
            'address' => $newCompany->address,
            'city_id' => $newCompany->city_id,
            'description' => $newCompany->description,
            'status' => $newCompany->status,
            'representatives' => [
                [
                    'name' => $representative['name'],
                    'phone' => $representative['phone'],
                    'email' => $representative['email'],
                    'position' => $representative['position'],
                ],
            ],
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    expect($company->refresh())
        ->name->toEqual($newCompany->name)
        ->phone->toEqual($newCompany->phone)
        ->email->toEqual($newCompany->email)
        ->category->toEqual($newCompany->category)
        ->address->toEqual($newCompany->address)
        ->city_id->toEqual($newCompany->city_id)
        ->description->toEqual($newCompany->description)
        ->status->toEqual($newCompany->status)
        // Assert representative was created
        ->and($company->representatives)
        ->toHaveCount(1)
        ->and($company->representatives->first())
        ->name->toBe($representative['name'])
        ->phone->toBe($representative['phone'])
        ->email->toBe($representative['email'])
        ->position->toBe($representative['position']);
});

it('can list transactions', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();
    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id]);
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(CompanyResource\Pages\ListCompanyTransaction::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can list deposit transactions', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();
    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'deposit']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ListCompanyTransaction::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can list withdraw transactions', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();
    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'withdraw']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ListCompanyTransaction::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can list charge transactions', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();
    Transaction::factory()
        ->count(5)
        ->create(['company_id' => $company->id, 'action_type' => 'charge']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ListCompanyTransaction::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->transactions);
});

it('can see the pending status for sender', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Sender::factory()->create([
        'company_id' => $company->id,
        'status' => 'pending',
    ])->each(function ($sender) use ($company) {
        $company->senders()->attach($sender);
    });

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->senders);

});

it('can see the active status for sender', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Sender::factory()->count(5)->create([
        'company_id' => $company->id,
        'status' => 'active',
    ])->each(function ($sender) use ($company) {
        $company->senders()->attach($sender);
    });

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->senders);

});

it('can see the rejected status for sender', function () {

    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    Sender::factory()->count(5)->create([
        'status' => 'rejected',
    ])->each(function ($sender) use ($company) {
        $company->senders()->attach($sender);
    });

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->assertCanSeeTableRecords($company->senders);

});

it('can see notification of non multi sender attached', function () {
    $company = Company::factory(['status' => 'active'])
        ->create()
        ->refresh();

    $sender = Sender::factory()->create([
        'type' => 'private',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->callTableAction(
        'attach',
        data: [
            'sender_id' => $sender->id,
            'expired_at' => Carbon::now()->addMonth(),
        ],
    );
    Notification::assertNotified();
});

it('cannot attach a deleted sender to company', function () {

    $company = Company::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(CompanyResource\Pages\ManageSenders::class, [
        'record' => $company->id,
    ])->callTableAction(
        'attach',
        data: [
            'expired_at' => now()->addMonth(),
        ],
    )->assertNotified();
});
