<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\PlanResource;
use App\Models\Company;
use App\Models\Feature;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    $company = Company::factory()->create();

    get(PlanResource::getUrl('index'), [
        'record' => $company,
    ])->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(PlanResource::getUrl('index'), [
        'record' => $company,
    ])->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(PlanResource::getUrl('index'), [
        'record' => $company,
    ])->assertOk();
});

it('can list plans', function () {
    $plans = Plan::limit(20)->get();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(PlanResource\Pages\ListPlans::class)
        ->set('tableRecordsPerPage', 20)
        ->assertCanSeeTableRecords($plans);
});

it('can create plan', function () {
    Plan::truncate();
    $plan = Plan::factory([
        'status' => 'active',
    ])->make();
    $feature = Feature::firstOrFail();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();
    livewire(PlanResource\Pages\CreatePlan::class)
        ->fillForm([
            'name' => $plan->name,
            'periodicity_type' => $plan->periodicity_type,
            'periodicity_quantity' => $plan->periodicity_quantity,
            'price' => $plan->price,
            'over_quota_price' => $plan->over_quota_price,
            'features' => [
                [
                    'feature_id' => $feature->id,
                    'charges' => 200,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    assertDatabaseHas(Plan::class, [
        'name' => $plan->name,
        'periodicity_type' => $plan->periodicity_type,
        'periodicity_quantity' => $plan->periodicity_quantity,
    ]);

    // Assert representative was created
    $plan = Plan::where('name', $plan->name)->firstOrFail();
    expect($plan->features)
        ->toHaveCount(1)
        ->and($plan->features->first())
        ->feature_id->toBe($feature->id);
});

it('can render edit page with right permissions', function () {
    $plan = Plan::factory()->create();
    get(PlanResource::getUrl('edit', ['record' => $plan]))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(PlanResource::getUrl('edit', ['record' => $plan]))->assertForbidden();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(PlanResource::getUrl('edit', ['record' => $plan]))->assertOk();
    get(PlanResource::getUrl('edit', ['record' => 1]))->assertNotFound();
});

it('can update plan', function () {
    $plan = Plan::factory()->create();
    $newPlan = Plan::factory()->make();
    $feature = Feature::firstOrFail();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->fillForm([
            'name' => $newPlan->name,
            'periodicity_type' => $newPlan->periodicity_type,
            'periodicity_quantity' => $newPlan->periodicity_quantity,
            'price' => $newPlan->price,
            'over_quota_price' => $newPlan->over_quota_price,
            'features' => [
                [
                    'feature_id' => $feature->id,
                    'charges' => 100,
                ],
            ],
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    expect($plan->refresh())
        ->name->toEqual($newPlan->name)
        ->periodicity_type->toEqual($newPlan->periodicity_type)
        ->periodicity_quantity->toEqual($newPlan->periodicity_quantity)
        ->price->toEqual($newPlan->price)
        ->over_quota_price->toEqual($newPlan->over_quota_price)
        // Assert representative was created
        ->and($plan->features)
        ->toHaveCount(1)
        ->and($plan->features->first())
        ->feature_id->toBe($feature->id);
});

it('can create plan with tags', function () {
    Plan::truncate();
    $plan = Plan::factory([
        'status' => 'active',
    ])->make();
    $feature = Feature::firstOrFail();
    $tags = ['popular', 'recommended'];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();
    livewire(PlanResource\Pages\CreatePlan::class)
        ->fillForm([
            'name' => $plan->name,
            'periodicity_type' => $plan->periodicity_type,
            'periodicity_quantity' => $plan->periodicity_quantity,
            'price' => $plan->price,
            'over_quota_price' => $plan->over_quota_price,
            'tags' => $tags,
            'features' => [
                [
                    'feature_id' => $feature->id,
                    'charges' => 200,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    // Get the created plan
    $createdPlan = Plan::where('name', $plan->name)->firstOrFail();

    // Assert tags were saved correctly
    expect($createdPlan->tags)->toBe($tags);
});

it('can update plan with tags', function () {
    // Create a plan without tags initially
    $plan = Plan::factory()->create(['tags' => null]);
    $feature = Feature::firstOrFail();

    // Define tags to add
    $tags = ['new', 'limited'];

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->fillForm([
            'tags' => $tags,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    // Refresh the plan from the database
    $plan->refresh();

    // Assert tags were updated correctly
    expect($plan->tags)->toBe($tags);
});

it('can add and remove tags from a plan', function () {
    // Create a plan with initial tags
    $plan = Plan::factory()->create(['tags' => ['popular']]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    $undoRepeaterFake = Repeater::fake();

    // First, verify the initial tags are loaded correctly
    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->assertFormSet([
            'tags' => ['popular'],
        ]);

    // Now update with different tags
    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->fillForm([
            'tags' => ['recommended', 'new'],
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    @$undoRepeaterFake();

    // Refresh the plan from the database
    $plan->refresh();

    // Assert tags were updated correctly (old tag removed, new tags added)
    expect($plan->tags)
        ->toBe(['recommended', 'new'])
        ->not->toContain('popular');
});

it('can set and save tags from suggestions', function () {
    // Create a plan without tags initially
    $plan = Plan::factory()->create(['tags' => null]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    // Choose two tags from the suggestions
    $selectedTags = ['popular', 'limited'];

    // Update the plan with the selected tags
    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->fillForm([
            'tags' => $selectedTags,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    // Refresh the plan from the database
    $plan->refresh();

    // Assert tags were saved correctly
    expect($plan->tags)->toBe($selectedTags);

    // Now try with a different tag from the suggestions
    livewire(PlanResource\Pages\EditPlan::class, [
        'record' => $plan->id,
    ])
        ->fillForm([
            'tags' => ['recommended'],
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    // Refresh the plan from the database
    $plan->refresh();

    // Assert the new tag was saved correctly
    expect($plan->tags)->toBe(['recommended']);
});

it('cannot delete plan when assigned to subscriptions', function () {
    Plan::truncate();
    $plan = Plan::factory()->create();
    Subscription::factory()->create(['plan_id' => $plan->id]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(PlanResource\Pages\ListPlans::class)
        ->assertCanSeeTableRecords([$plan])
        ->callTableAction('delete', $plan)
        ->assertNotified(__('Cannot delete plan'));
});

it('can delete plan when not assigned to subscriptions', function () {
    Plan::truncate();
    $plan = Plan::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(PlanResource\Pages\ListPlans::class)
        ->assertCanSeeTableRecords([$plan])
        ->callTableAction('delete', $plan)
        ->assertNotified(__('Plan deleted successfully'));
});
