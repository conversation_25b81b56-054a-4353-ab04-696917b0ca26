<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\RegistrationRequestResource;
use App\Mail\ApprovedRegistration;
use App\Models\Company;
use App\Models\Invitation;
use App\Models\RegistrationRequest;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function () {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    RegistrationRequest::factory()->count(5)->create();

    get(RegistrationRequestResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );
    actingAs(User::factory()->create());
    get(RegistrationRequestResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(RegistrationRequestResource::getUrl('index'))->assertOk();
});

it('can list registration requests', function () {
    $registration_request = RegistrationRequest::factory()->count(5)->create();
    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(
        RegistrationRequestResource\Pages\ListRegistrationRequests::class,
    )->assertCanSeeTableRecords($registration_request);
});

it('handles approved registration request correctly', function () {
    // Mock dependencies
    Mail::fake();
    DB::shouldReceive('transaction')->andReturnUsing(function ($callback) {
        $callback();
    });

    // Create a registration request
    $registrationRequest = RegistrationRequest::factory()->create([
        'status' => 'approved',
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '*********',
        'company' => 'Test Company',
        'category' => 'IT',
    ]);

    $company = Company::create([
        'name' => $registrationRequest->company,
        'phone' => $registrationRequest->phone,
        'email' => $registrationRequest->email,
        'category' => $registrationRequest->category,
        'status' => 'active',
    ]);

    $invitation = Invitation::create([
        'email' => $registrationRequest->email,
        'company_id' => $company->id,
        'expires_at' => now()->addDays(7),
        'role' => 'company_owner',
    ]);

    Mail::to($registrationRequest->email)->send(new ApprovedRegistration($invitation));

    // Assertions
    expect(Invitation::where('email', $invitation->email)->exists())->toBeTrue()
        ->and(Company::where('email', $invitation->email)->exists())->toBeTrue();
    Mail::assertSent(ApprovedRegistration::class, function ($mail) use ($invitation) {
        return $mail->hasTo($invitation->email);
    });
});

it('creates the correct content for the ApprovedRegistration email', function () {
    $registrationRequest = RegistrationRequest::factory()->create([
        'status' => 'approved',
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '*********',
        'company' => 'Test Company',
        'category' => 'IT',
    ]);

    $company = Company::create([
        'name' => $registrationRequest->company,
        'phone' => $registrationRequest->phone,
        'email' => $registrationRequest->email,
        'category' => $registrationRequest->category,
        'status' => 'active',
    ]);

    $invitation = Invitation::create([
        'email' => $registrationRequest->email,
        'company_id' => $company->id,
        'expires_at' => now()->addDays(7),
        'role' => 'company_owner',
    ]);

    $mailable = new ApprovedRegistration($invitation);

    $content = $mailable->content();

    expect($content->view)->toBe('email.approved-registration');
});

test('can render edit page with right permissions', function () {
    $registration_request = RegistrationRequest::factory()->create(['status' => 'pending']);

    get(RegistrationRequestResource::getUrl('edit', ['record' => $registration_request]))
        ->assertRedirect('/admin/login');

    actingAs(User::factory()->create());
    get(RegistrationRequestResource::getUrl('edit', ['record' => $registration_request]))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(RegistrationRequestResource::getUrl('edit', ['record' => $registration_request]))->assertOk();
    get(RegistrationRequestResource::getUrl('edit', ['record' => 1]))->assertNotFound();
});

it('can update registration request', function () {
    $registrationRequest = RegistrationRequest::factory()->create(['status' => 'pending']);
    $newRegistrationRequest = RegistrationRequest::factory([
        'status' => 'approved',
    ])->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(RegistrationRequestResource\Pages\EditRegistrationRequest::class, [
        'record' => $registrationRequest->id,
    ])
        ->fillForm([
            'status' => $newRegistrationRequest->status,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($registrationRequest->refresh())
        ->status->toEqual($newRegistrationRequest->status);
});
