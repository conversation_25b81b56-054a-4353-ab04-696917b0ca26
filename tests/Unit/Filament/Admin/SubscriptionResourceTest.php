<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\SubscriptionResource;
use App\Models\Company;
use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\FeaturePlan;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    $company = Company::factory()->create();

    get(SubscriptionResource::getUrl('index'), [
        'record' => $company,
    ])->assertRedirect(Filament::getPanel('admin')->getLoginUrl());

    actingAs(User::factory()->create());
    get(SubscriptionResource::getUrl('index'), [
        'record' => $company,
    ])->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(SubscriptionResource::getUrl('index'), [
        'record' => $company,
    ])->assertOk();
});

it('can list subscriptions', function () {
    $subscriptions = Subscription::factory()->count(5)->create();
    $feature = Feature::factory()->create();
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);

    foreach ($subscriptions as $subscription) {
        $subscription->plan()->associate($plan);
        $subscription->save();
    }

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SubscriptionResource\Pages\ListSubscriptions::class)
        ->set('tableRecordsPerPage', 20)
        ->assertCanSeeTableRecords($subscriptions);
});

it('can list subscriptions consumption', function () {
    $subscriptions = Subscription::factory()->create();
    $featureConsumptions = FeatureConsumption::factory([
        'subscription_id' => $subscriptions->id,
    ])
        ->count(5)
        ->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SubscriptionResource\Pages\SubscriptionConsumption::class, [
        'record' => $subscriptions->id,
    ])->assertCanSeeTableRecords($featureConsumptions);
});

it('shows empty state when subscription has no consumption', function () {
    $subscriptions = Subscription::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(SubscriptionResource\Pages\SubscriptionConsumption::class, [
        'record' => $subscriptions->id,
    ])->assertSee(__('No record founds'));
});

it('detects duplicate features in active subscriptions', function () {
    // Create a project and two plans with overlapping features
    $project = Project::factory()->create();

    // Create the first plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $plan->feature()->save($feature);

    // Create an active subscription for the project with the first plan
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $plan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    // Create a new plan with overlapping features
    $newPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $newPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 3']);
    $newPlan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return true because Feature 1 is duplicated
    expect($result)->toBeTrue();
});

it('returns false when no duplicate features exist', function () {
    // Create a project and two plans with non-overlapping features
    $project = Project::factory()->create();

    // Create the first plan with features
    $existingPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $existingPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $existingPlan->feature()->save($feature);

    // Create an active subscription for the project with the first plan
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $existingPlan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    // Create a new plan with non-overlapping features
    $newPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 3']);
    $existingPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 4']);
    $existingPlan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return false because there are no duplicate features
    expect($result)->toBeFalse();
});

it('returns false when project has no active subscriptions', function () {
    // Create a project with no active subscriptions
    $project = Project::factory()->create();

    // Create a plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $plan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $plan);

    // Should return false because there are no active subscriptions
    expect($result)->toBeFalse();
});

it('returns false when project has expired subscriptions with duplicate features', function () {
    // Create a project
    $project = Project::factory()->create();

    // Create a plan with features
    $existingPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $existingPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $existingPlan->feature()->save($feature);

    // Create an expired subscription for the project
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $existingPlan->id,
        'expired_at' => now()->subDay(), // Expired subscription
        'canceled_at' => null,
    ]);

    // Create a new plan with overlapping features
    $newPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $newPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 3']);
    $newPlan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return false because the subscription with duplicate features is expired
    expect($result)->toBeFalse();
});

it('returns false when project has canceled subscriptions with duplicate features', function () {
    // Create a project
    $project = Project::factory()->create();

    // Create a plan with features
    $existingPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $existingPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $existingPlan->feature()->save($feature);

    // Create a canceled subscription for the project
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $existingPlan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => now()->subDay(), // Canceled subscription
    ]);

    // Create a new plan with overlapping features
    $newPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $newPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 3']);
    $newPlan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return false because the subscription with duplicate features is canceled
    expect($result)->toBeFalse();
});

it('returns false when plan has no features', function () {
    // Create a project with an active subscription
    $project = Project::factory()->create();

    // Create a plan with features
    $existingPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $existingPlan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $existingPlan->feature()->save($feature);

    // Create an active subscription for the project
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $existingPlan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    // Create a new plan with no features
    $newPlan = Plan::factory()->create();
    // No features added to this plan

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return false because the new plan has no features to duplicate
    expect($result)->toBeFalse();
});

it('returns false when active subscription has no features', function () {
    // Create a project
    $project = Project::factory()->create();

    // Create a plan with no features
    $existingPlan = Plan::factory()->create();
    // No features added to this plan

    // Create an active subscription for the project
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $existingPlan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    // Create a new plan with features
    $newPlan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $newPlan->feature()->save($feature);
    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $newPlan->feature()->save($feature);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'hasDuplicateFeatures');

    // Call the method and check the result
    $result = $reflectionMethod->invoke($listSubscriptions, $project, $newPlan);

    // Should return false because the active subscription has no features to duplicate
    expect($result)->toBeFalse();
});

it('returns correct form components for new subscription', function () {
    // Create some test data
    $activeCompany = Company::factory()->create(['status' => 'active']);
    $inactiveCompany = Company::factory()->create(['status' => 'inactive']);

    $project = Project::factory()->create(['company_id' => $activeCompany->id]);

    $activePlan = Plan::factory()->create(['status' => 'active', 'name' => 'Active Plan']);
    $inactivePlan = Plan::factory()->create(['status' => 'inactive', 'name' => 'Inactive Plan']);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'newSubscriptionForm');
    $formComponents = $reflectionMethod->invoke($listSubscriptions);

    // Assert that the form has the expected components
    expect($formComponents)->toBeArray()
        ->and($formComponents)->toHaveCount(3);
    // company_id, project_id, plan_id

    // Check company_id component
    $companyComponent = collect($formComponents)->first(fn ($component) => $component->getName() === 'company_id');
    expect($companyComponent)->not->toBeNull()
        ->and($companyComponent->getLabel())->toBe('Company')
        ->and($companyComponent->isSearchable())->toBeTrue();

    // Check project_id component
    $projectComponent = collect($formComponents)->first(fn ($component) => $component->getName() === 'project_id');
    expect($projectComponent)->not->toBeNull()
        ->and($projectComponent->getLabel())->toBe('Project')
        ->and($projectComponent->isSearchable())->toBeTrue()
        ->and($projectComponent->isLive())->toBeTrue();

    // Check plan_id component
    $planComponent = collect($formComponents)->first(fn ($component) => $component->getName() === 'plan_id');
    expect($planComponent)->not->toBeNull()
        ->and($planComponent->getLabel())->toBe('Plan')
        ->and($planComponent->isSearchable())->toBeTrue();

    // Test that only active companies are included in the options
    $companyOptions = $companyComponent->getOptions();
    expect($companyOptions)->toBeArray()
        ->and($companyOptions)->toHaveKey($activeCompany->id)
        ->and($companyOptions)->not->toHaveKey($inactiveCompany->id);

    // Test that only active plans are included in the options
    $planOptions = $planComponent->getOptions();
    expect($planOptions)->toBeArray();

    // The plan options should include the active plan but not the inactive plan
    $planIds = array_keys($planOptions);
    expect($planIds)->toContain($activePlan->id)
        ->and($planIds)->not->toContain($inactivePlan->id);

    // Check that the plan option text is formatted correctly
    $planOptionText = $planOptions[$activePlan->id];
    expect($planOptionText)->toContain($activePlan->name)
        ->and($planOptionText)->toContain(number_format($activePlan->price, 2));
});

it('processes a subscription successfully', function () {
    // Create test data
    $company = Company::factory()->create();
    $project = Project::factory()->create(['company_id' => $company->id]);
    $plan = Plan::factory()->create(['price' => 100, 'periodicity_quantity' => 1]);
    Subscription::factory()->create(['project_id' => $project->id, 'plan_id' => $plan->id]);

    // Create an instance of ListSubscriptions to test the method
    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();

    // Use reflection to access the private method
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'processSubscription');

    // Call the method
    $reflectionMethod->invoke($listSubscriptions, $company, $project, $plan);

    // Assert that a subscription was created
    $this->assertDatabaseHas('subscriptions', [
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ]);

    // Assert that a transaction was created
    $this->assertDatabaseHas('transactions', [
        'company_id' => $company->id,
        'amount' => -100000,
        'description' => 'Subscription to '.$plan->name,
    ]);

    // Assert that a notification was sent
    Notification::assertNotified(
        Notification::make()
            ->title(__('Subscription Successful'))
            ->body(__('You have successfully subscribed to ').$plan->name)
            ->success()
    );
});

it('fails to subscribe due to balance not enough', function () {
    $company = Company::factory()->create();
    $project = Project::factory()->create(['company_id' => $company->id]);
    $plan = Plan::factory()->create(['price' => 100]);

    // Create an active subscription with the same features
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $plan->id,
        'expired_at' => now()->addMonth(),
    ]);

    $data = [
        'company_id' => $company->id,
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ];

    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'subscribeTo');
    $reflectionMethod->invoke($listSubscriptions, $data);

    Notification::assertNotified(
        Notification::make()
            ->title(__('Insufficient Balance'))
            ->body(__('You do not have enough balance to subscribe to this plan.'))
            ->danger()
    );
});

it('fails to subscribe due to duplicate features', function () {
    // Create a project and two plans with overlapping features
    $company = Company::factory()->create(['status' => 'active']);
    $company->transactions()->create(['amount' => 100000, 'status' => 'completed', 'action_type' => 'deposit']);
    $project = Project::factory()->create();

    // Create the first plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    $feature = Feature::factory()->create(['name' => 'Feature 2']);
    $plan->feature()->save($feature);

    // Create an active subscription for the project with the first plan
    Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $plan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    $this->assertDatabaseHas('subscriptions', [
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ]);

    $data = [
        'company_id' => $company->id,
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ];

    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'subscribeTo');
    $reflectionMethod->invoke($listSubscriptions, $data);

    Notification::assertNotified(
        Notification::make()
            ->title(__('Duplicate Plan Features'))
            ->body(__('already subscribed to a You areplan with similar features.'))
            ->danger()
    );
});

it('success to subscribe', function () {
    // Create a project and two plans with overlapping features
    $company = Company::factory()->create(['status' => 'active']);
    $company->transactions()->create(['amount' => 1000000, 'status' => 'completed', 'action_type' => 'deposit']);
    $project = Project::factory()->create();

    // Create the first plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    $data = [
        'company_id' => $company->id,
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ];

    $listSubscriptions = new SubscriptionResource\Pages\ListSubscriptions();
    $reflectionMethod = new ReflectionMethod($listSubscriptions, 'subscribeTo');
    $reflectionMethod->invoke($listSubscriptions, $data);

    $this->assertDatabaseHas('subscriptions', [
        'project_id' => $project->id,
        'plan_id' => $plan->id,
    ]);

    Notification::assertNotified(
        Notification::make()
            ->title(__('Subscription Successful'))
            ->body(__('You have successfully subscribed to ').$plan->name)
            ->success()
    );
});

it('renders the correct color for each state', function () {
    $company = Company::factory()->create(['status' => 'active']);
    $company->transactions()->create(['amount' => 100000, 'status' => 'completed', 'action_type' => 'deposit']);
    $project = Project::factory()->create();

    // Create the first plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    // Create an active subscription for the project with the first plan
    $subscription = Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $plan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $subscription,
    ])->assertSeeHtml('var(--success-400)'); // success-color;

    $subscription->update(['expired_at' => now()->subMonth()]);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $subscription,
    ])->assertSeeHtml('var(--danger-400)'); // danger-color;
});

it('will halt cancel', function () {
    $company = Company::factory()->create(['status' => 'active']);
    $company->transactions()->create(['amount' => 100000, 'status' => 'completed', 'action_type' => 'deposit']);
    $project = Project::factory()->create();

    // Create the first plan with features
    $plan = Plan::factory()->create();
    $feature = Feature::factory()->create(['name' => 'Feature 1']);
    $plan->feature()->save($feature);

    // Create an active subscription for the project with the first plan
    $subscription = Subscription::factory()->create([
        'project_id' => $project->id,
        'plan_id' => $plan->id,
        'expired_at' => now()->addMonth(),
        'canceled_at' => null,
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $subscription,
    ])->callTableAction('cancel', $subscription);
});
