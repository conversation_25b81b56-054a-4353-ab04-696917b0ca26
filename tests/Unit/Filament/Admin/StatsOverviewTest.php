<?php

declare(strict_types=1);

use App\Filament\Admin\Widgets\StatsOverview;
use App\Models\Company;
use App\Models\MessageReceipt;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget\Stat;

use function Pest\Laravel\travelTo;

beforeEach(function () {
    // Fix current time to have predictable test output
    travelTo(Carbon::parse('2024-01-15'));
});

it('returns correct stats array structure', function () {
    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    expect($stats)->toBeArray()->toHaveCount(4);

    foreach ($stats as $stat) {
        expect($stat)->toBeInstanceOf(Stat::class);
    }
});

it('shows correct total customers count', function () {
    // Create some companies
    Company::factory()->count(5)->create();

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    $totalCustomersStat = $stats[0];

    expect($totalCustomersStat->getLabel())->toBe(__('Total Customers'))
        ->and($totalCustomersStat->getValue())->toBe(number_format(5))
        ->and($totalCustomersStat->getDescription())->toBe(__('Active companies'))
        ->and($totalCustomersStat->getDescriptionIcon())->toBe('heroicon-m-building-office')
        ->and($totalCustomersStat->getColor())->toBe('primary');
});

it('calculates success rate correctly', function () {
    // Create message receipts with different statuses
    $thirtyDaysAgo = Carbon::now()->subDays(30)->startOfDay();

    // Create 10 delivered messages
    MessageReceipt::factory()->count(10)->create([
        'status' => 'delivered',
        'created_at' => $thirtyDaysAgo->addDays(5),
    ]);

    // Create 10 failed messages
    MessageReceipt::factory()->count(10)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->addDays(10),
    ]);

    // Create 5 pending messages
    MessageReceipt::factory()->count(5)->create([
        'status' => 'pending',
        'created_at' => $thirtyDaysAgo->addDays(15),
    ]);

    // Create 5 sent messages
    MessageReceipt::factory()->count(5)->create([
        'status' => 'sent',
        'created_at' => $thirtyDaysAgo->addDays(20),
    ]);

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    $successRateStat = $stats[1];

    // Success rate should be (10 delivered / 30 total) * 100 = 33.3%
    expect($successRateStat->getLabel())->toBe(__('Success Rate'))
        ->and($successRateStat->getValue())->toBe('33.3%')
        ->and($successRateStat->getDescription())->toBe(__('Delivered messages'))
        ->and($successRateStat->getDescriptionIcon())->toBe('heroicon-m-check-circle')
        ->and($successRateStat->getColor())->toBe('danger'); // Below 70% should be danger
});

it('shows correct pending messages count', function () {
    // Create 15 pending messages
    MessageReceipt::factory()->count(15)->create([
        'status' => 'pending',
    ]);

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    $pendingMessagesStat = $stats[2];

    expect($pendingMessagesStat->getLabel())->toBe(__('Pending Messages'))
        ->and($pendingMessagesStat->getValue())->toBe(number_format(15))
        ->and($pendingMessagesStat->getDescription())->toBe(__('Awaiting delivery'))
        ->and($pendingMessagesStat->getDescriptionIcon())->toBe('heroicon-m-clock')
        ->and($pendingMessagesStat->getColor())->toBe('warning');
});

it('shows correct failed messages count', function () {
    $thirtyDaysAgo = Carbon::now()->subDays(30)->startOfDay();

    // Create 8 failed messages within the last 30 days
    MessageReceipt::factory()->count(8)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->addDays(15),
    ]);

    // Create 5 failed messages older than 30 days (should not be counted)
    MessageReceipt::factory()->count(5)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->subDays(5),
    ]);

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    $failedMessagesStat = $stats[3];

    expect($failedMessagesStat->getLabel())->toBe(__('Failed Messages'))
        ->and($failedMessagesStat->getValue())->toBe(number_format(13))
        ->and($failedMessagesStat->getDescription())->toBe(__('In the last 30 days'))
        ->and($failedMessagesStat->getDescriptionIcon())->toBe('heroicon-m-exclamation-triangle')
        ->and($failedMessagesStat->getColor())->toBe('danger');
});

it('handles empty data correctly', function () {
    // No companies or message receipts in the database
    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    expect($stats[0]->getValue())->toBe('0') // Total customers
        ->and($stats[1]->getValue())->toBe('0%') // Success rate
        ->and($stats[2]->getValue())->toBe('0') // Pending messages
        ->and($stats[3]->getValue())->toBe('0'); // Failed messages
});

it('changes success rate color based on percentage', function () {
    $thirtyDaysAgo = Carbon::now()->subDays(30)->startOfDay();

    // Test for danger color (< 70%)
    MessageReceipt::factory()->count(10)->create([
        'status' => 'delivered',
        'created_at' => $thirtyDaysAgo->addDays(5),
    ]);
    MessageReceipt::factory()->count(30)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->addDays(10),
    ]);

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    expect($stats[1]->getColor())->toBe('danger');

    // Clear the database
    MessageReceipt::query()->delete();

    // Test for warning color (>= 70% and < 90%)
    MessageReceipt::factory()->count(75)->create([
        'status' => 'delivered',
        'created_at' => $thirtyDaysAgo->addDays(5),
    ]);
    MessageReceipt::factory()->count(25)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->addDays(10),
    ]);

    $stats = $reflectionMethod->invoke($widget);
    expect($stats[1]->getColor())->toBe('warning');

    // Clear the database
    MessageReceipt::query()->delete();

    // Test for success color (>= 90%)
    MessageReceipt::factory()->count(95)->create([
        'status' => 'delivered',
        'created_at' => $thirtyDaysAgo->addDays(5),
    ]);
    MessageReceipt::factory()->count(5)->create([
        'status' => 'failed',
        'created_at' => $thirtyDaysAgo->addDays(10),
    ]);

    $stats = $reflectionMethod->invoke($widget);
    expect($stats[1]->getColor())->toBe('success');
});

it('generates correct message trend data for chart', function () {
    $sevenDaysAgo = Carbon::now()->subDays(7)->startOfDay();

    // Create messages for each of the last 7 days with different counts
    for ($i = 0; $i < 7; $i++) {
        $date = $sevenDaysAgo->copy()->addDays($i);
        $count = ($i + 1) * 2; // 2, 4, 6, 8, 10, 12, 14

        MessageReceipt::factory()->count($count)->create([
            'created_at' => $date,
        ]);
    }

    $widget = new StatsOverview();
    $reflectionMethod = new ReflectionMethod($widget, 'getStats');
    $stats = $reflectionMethod->invoke($widget);

    $successRateStat = $stats[1];
    $chart = $successRateStat->getChart();

    expect($chart)->toBeArray()->toHaveCount(7);

    // Check if the trend data matches our expected pattern
    // The values should follow the pattern we created: 2, 4, 6, 8, 10, 12, 14
    for ($i = 0; $i < 7; $i++) {
        $expectedCount = ($i + 1) * 2;
        expect($chart[$i])->toBe((float) $expectedCount);
    }
});
