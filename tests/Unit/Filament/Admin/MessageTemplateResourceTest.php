<?php

declare(strict_types=1);

use App\Filament\Admin\Resources\MessageTemplateResource;
use App\Models\MessageTemplate;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Tables\Actions\DeleteAction;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('admin'));
});

it('can render page with right permissions', function () {
    get(MessageTemplateResource::getUrl('index'))->assertRedirect(
        Filament::getPanel('admin')->getLoginUrl(),
    );

    actingAs(User::factory()->create());
    get(MessageTemplateResource::getUrl('index'))->assertForbidden();

    actingAs(User::factory()->create()->assignRole('super_admin'));
    get(MessageTemplateResource::getUrl('index'))->assertOk();
});

it('can create message template', function () {
    $messageTemplate = MessageTemplate::factory()->withParameters(2)->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'company_id' => $messageTemplate->company_id,
            'project_id' => $messageTemplate->project_id,
            'display_name' => $messageTemplate->display_name,
            'short_name' => $messageTemplate->short_name,
            'content' => $messageTemplate->content,
            'type' => $messageTemplate->type,
            'status' => $messageTemplate->status,
            'parameters' => [
                [
                    'name' => 'name',
                    'type' => 'string',
                    'max_limit' => 100,
                ],
                [
                    'name' => 'age',
                    'type' => 'number',
                    'max_limit' => 100,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(MessageTemplate::class, [
        'company_id' => $messageTemplate->company_id,
        'project_id' => $messageTemplate->project_id,
        'display_name' => $messageTemplate->display_name,
        'short_name' => $messageTemplate->short_name,
        'content' => $messageTemplate->content,
        'type' => $messageTemplate->type,
        'status' => $messageTemplate->status,
    ]);
});

it('can update message template', function () {
    $messageTemplate = MessageTemplate::factory()->withParameters(2)->create();
    $newMessageTemplate = MessageTemplate::factory()->withParameters(2)->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\EditMessageTemplate::class, [
        'record' => $messageTemplate->id,
    ])
        ->fillForm([
            'company_id' => $newMessageTemplate->company_id,
            'project_id' => $newMessageTemplate->project_id,
            'display_name' => $newMessageTemplate->display_name,
            'short_name' => $newMessageTemplate->short_name,
            'content' => $newMessageTemplate->content,
            'type' => $newMessageTemplate->type,
            'status' => $newMessageTemplate->status,
            'parameters' => [
                [
                    'name' => 'name',
                    'type' => 'string',
                    'max_limit' => 100,
                ],
                [
                    'name' => 'age',
                    'type' => 'number',
                    'max_limit' => 100,
                ],
            ],
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($messageTemplate->refresh())
        ->company_id->toEqual($newMessageTemplate->company_id)
        ->project_id->toEqual($newMessageTemplate->project_id)
        ->display_name->toEqual($newMessageTemplate->display_name)
        ->short_name->toEqual($newMessageTemplate->short_name)
        ->content->toEqual($newMessageTemplate->content)
        ->type->toEqual($newMessageTemplate->type)
        ->status->toEqual($newMessageTemplate->status);
});

it('can set parameters from content', function () {
    $messageTemplate = MessageTemplate::factory()->make();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'content' => 'Hello {{name}}',
        ])
        ->assertFormSet([
            'parameters' => [
                [
                    'name' => 'name',
                    'type' => 'string',
                ],
            ],
        ]);
});

it('can delete message template', function () {
    $messageTemplate = MessageTemplate::factory()->create();

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\EditMessageTemplate::class, [
        'record' => $messageTemplate->id,
    ])
        ->callAction(DeleteAction::class);

    assertDatabaseMissing('message_templates', [
        'id' => $messageTemplate->id,
    ]);
});

it('can render active status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create([
        'status' => 'active',
    ]);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['success', 'active']);
});

it('can render inactive status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'inactive']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['danger', __('inactive')]);
});

it('can render pending status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'pending']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['warning', __('pending')]);
});

it('can render rejected status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'rejected']);

    actingAs(User::factory()->create()->assignRole('super_admin'));

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['danger', __('rejected')]);
});
