<?php

declare(strict_types=1);

use App\Filament\Company\Resources\SenderResource;
use App\Models\Company;
use App\Models\Provider;
use App\Models\Sender;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        SenderResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        SenderResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        SenderResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('shows empty state when company has no sender', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class)->assertSee(
        __('No senders found'),
    );
});

it('can list senders company', function () {
    $senders = Sender::factory()->count(5)->create();
    $this->company->senders()->attach($senders);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $senders,
    ])->assertCanSeeTableRecords($senders);
});

it('can render create page with right permissions', function () {
    get(
        SenderResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        SenderResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        SenderResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can create sender', function () {
    $sender = Sender::factory()->make();
    $provider = Provider::first();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(SenderResource\Pages\CreateSender::class)
        ->fillForm([
            'sender' => $sender->sender,
            'category' => $sender->category,
            'notes' => $sender->notes,
            'provider' => [
                [
                    'provider_id' => $provider->id,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();
    @$undoRepeaterFake();

    assertDatabaseHas(Sender::class, [
        'sender' => $sender->sender,
        'category' => $sender->category,
        'notes' => $sender->notes,
        'company_id' => $this->company->id,
    ]);
});

it('shows provider name in itemLabel in repeater', function () {
    $provider = Provider::factory()->create(['name' => 'Demo Provider']);

    $sender = Sender::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(SenderResource\Pages\EditSender::class, [
        'record' => $sender->id,
    ])
        ->fillForm([
            'provider' => [
                [
                    'id' => 1,
                    'provider_id' => $provider->id,
                    'expired_at' => now()->addMonth()->format('Y-m-d'),
                ],
            ],
        ])
        ->call('save')
        ->assertHasNoFormErrors();
});

it('can list pinding senders company', function () {
    $senders = Sender::factory()->count(5)->create(
        ['status' => 'pending'],
    );
    $this->company->senders()->attach($senders);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $senders,
    ])->assertCanSeeTableRecords($senders);
});

it('can list active senders company', function () {
    $senders = Sender::factory()->count(5)->create(
        ['status' => 'active'],
    );
    $this->company->senders()->attach($senders);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $senders,
    ])->assertCanSeeTableRecords($senders);
});

it('can list rejected senders company', function () {
    $senders = Sender::factory()->count(5)->create(
        ['status' => 'rejected'],
    );
    $this->company->senders()->attach($senders);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $senders,
    ])->assertCanSeeTableRecords($senders);
});

it('can list expired at senders company', function () {
    $senders = Sender::factory()->count(5)->create(
        ['status' => 'active'],
    );

    $this->company->senders()->attach($senders, [
        'expired_at' => now()->addMonth(),
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SenderResource\Pages\ListSenders::class, [
        'record' => $senders,
    ])->assertCanSeeTableRecords($senders);
});
