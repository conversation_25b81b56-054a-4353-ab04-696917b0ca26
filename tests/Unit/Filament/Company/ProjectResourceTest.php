<?php

declare(strict_types=1);

use App\Filament\Company\Resources\ProjectResource;
use App\Filament\Company\Resources\ProjectResource\Pages\ListIpwhitelist;
use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\FeatureConsumption;
use App\Models\FeaturePlan;
use App\Models\IpWhitelist;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        ProjectResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ProjectResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ProjectResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can list project company', function () {
    $projects = Project::factory()
        ->count(5)
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ListProjects::class, [
        'record' => $projects,
    ])->assertCanSeeTableRecords($projects);
});

it('shows empty state when company has no project', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ListProjects::class)->assertSee(
        __('No projects found'),
    );
});

it('can create project', function () {
    $project = Project::factory(['type' => 'budget'])->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ProjectResource\Pages\CreateProject::class)
        ->fillForm([
            'name' => $project->name,
            'type' => $project->type,
            'limit' => $project->limit,
            'status' => $project->status,
            'description' => $project->description,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(Project::class, [
        'name' => $project->name,
        'type' => $project->type,
        'limit' => $project->limit,
        'status' => $project->status,
        'description' => $project->description,
        'company_id' => $this->company->id,
    ]);
});

it('can render edit page with right permissions', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    get(
        ProjectResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ProjectResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ProjectResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertOk();
    get(
        ProjectResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => 1,
        ]),
    )->assertNotFound();
});

it('can update plan', function () {
    $project = Project::factory()->create([
        'company_id' => $this->company->id,
        'type' => 'mixed',
    ]);
    $newProject = Project::factory()->make([
        'company_id' => $this->company->id,
        'type' => 'mixed',
    ]);

    actingAs(User::factory()->create()->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\EditProject::class, [
        'record' => $project->id,
    ])
        ->fillForm([
            'name' => $newProject->name,
            'type' => $newProject->type,
            'limit' => $newProject->limit,
            'status' => $newProject->status,
            'description' => $newProject->description,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($project->refresh())
        ->name->toEqual($newProject->name)
        ->type->toEqual($newProject->type)
        ->limit->toEqual($newProject->limit)
        ->status->toEqual($newProject->status)
        ->description->toEqual($newProject->description);
});

it('can render contacts page with right permissions', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    get(
        ProjectResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ProjectResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ProjectResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertOk();
    get(
        ProjectResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => 1,
        ]),
    )->assertNotFound();
});

it('shows empty state when project has no contacts', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ManageContact::class, [
        'record' => $project->id,
    ])->assertSee(__('No contacts found'));
});

it('can list contacts in the project', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $contactGroup = ContactGroup::factory()
        ->withContacts(5)
        ->create([
            'company_id' => $this->company->id,
        ]);
    $project->contact_groups()->attach($contactGroup);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ManageContact::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords([$contactGroup]);
});

it('can attach group contacts to project', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $contactGroup = ContactGroup::factory()
        ->withContacts(5)
        ->create([
            'company_id' => $this->company->id,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ManageContact::class, [
        'record' => $project->id,
    ])->callTableAction(
        'attach',
        data: [
            'recordId' => $contactGroup->id,
        ],
    );

    assertDatabaseHas('contact_group_project', [
        'contact_group_id' => $contactGroup->id,
        'project_id' => $project->id,
    ]);
});

it('shows empty state when project has no subscriptions', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectSubscription::class, [
        'record' => $project->id,
    ])->assertSee(__('No subscriptions found'));
});

it('can list subscriptions in the project', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::first();
    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectSubscription::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords($subscriptions);
});

it('shows empty state when project has no consumptions', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectConsumption::class, [
        'record' => $project->id,
    ])->assertSee(__('No consumptions found'));
});

it('can list consumptions in the project', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::first();
    $subscriptions = Subscription::factory()->create([
        'plan_id' => $plan->id,
        'project_id' => $project->id,
    ]);

    $consumptions = FeatureConsumption::factory()
        ->count(3)
        ->create(['subscription_id' => $subscriptions->id]);
    $consumptions->push(FeatureConsumption::factory()->create([
        'subscription_id' => $subscriptions->id,
        'type' => 'OTP',
    ]));
    $consumptions->push(FeatureConsumption::factory()->create([
        'subscription_id' => $subscriptions->id,
        'type' => 'SMS',
    ]));

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectConsumption::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords($consumptions);
});

it('can create a new token', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\EditProject::class, [
        'record' => $project->id,
    ])->callAction('new-token')
        ->assertSessionHasNoErrors()
        ->assertNotified(__('New Token'));

    $project->refresh();
    expect($project->token)->not->toBeNull();
});

it('handles errors when creating a new token', function () {
    // This test directly tests the exception handling logic in the new-token action

    // Create a function that simulates the action in EditProject.php
    $exceptionHandlingFunction = function (Exception $e) {
        // This is the exact code from the catch block in EditProject.php
        $notification = Notification::make()
            ->title(__('Error'))
            ->body($e->getMessage())
            ->icon('heroicon-o-exclamation-circle')
            ->send();

        return $notification;
    };

    // Create an exception with a test message
    $testException = new Exception('Token creation failed');

    // Call the function with our test exception
    $notification = $exceptionHandlingFunction($testException);

    // Verify that the notification has the correct properties
    // This is a more direct test of the exception handling logic
    expect($notification)->toBeInstanceOf(Notification::class);

    // Test that the exception message is properly passed to the notification
    $exceptionMessage = $testException->getMessage();
    expect($exceptionMessage)->toBe('Token creation failed');
});

it('see project subscription data in table status color success', function () {

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectSubscription::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords($subscriptions);
});

it('see project subscription data in table status color danger', function () {

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->subMonth(),
            'canceled_at' => null,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ProjectSubscription::class, [
        'record' => $project->id,
    ])
        ->assertTableColumnFormattedStateSet('id', __('Expired'), $subscriptions->first())
        ->assertCanSeeTableRecords($subscriptions);
});

it('see project contact status inactive', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    // create contact group and attach to company
    $contactGroups = ContactGroup::factory()
        ->count(5)
        ->create(['company_id' => $this->company->id, 'status' => 'inactive']);

    $project->contact_groups()->attach($contactGroups);

    /*$Groups = ContactGroup::with('contacts')
        ->whereRelation('projects', 'id', $project->id)->get();*/

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ManageContact::class, [
        'record' => $project->id,
    ])
        ->assertTableColumnFormattedStateSet('status', __('inactive'), $contactGroups->first())
        ->assertCanSeeTableRecords($contactGroups);
});

it('see project contact status active', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    // create contact group and attach to company
    $contactGroups = ContactGroup::factory()
        ->count(5)
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    $project->contact_groups()->attach($contactGroups);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ManageContact::class, [
        'record' => $project->id,
    ])
        ->assertTableColumnFormattedStateSet('status', __('active'), $contactGroups->first())
        ->assertCanSeeTableRecords($contactGroups);
});

it('can render ip-whitelist page with right permissions', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);

    get(
        ProjectResource::getUrl('ip-whitelist', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ProjectResource::getUrl('ip-whitelist', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ProjectResource::getUrl('ip-whitelist', [
            'tenant' => $this->company->id,
            'record' => $project,
        ]),
    )->assertOk();
    get(
        ProjectResource::getUrl('ip-whitelist', [
            'tenant' => $this->company->id,
            'record' => 1,
        ]),
    )->assertNotFound();
});

it('can list ip whitelist in the project', function () {
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $ipwhitelist = IpWhitelist::factory()
        ->count(5)
        ->create(['project_id' => $project->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ListIpwhitelist::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords($ipwhitelist);
});

it('shows empty state when ip whitelist has no items', function () {
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ListIpwhitelist::class, [
        'record' => $project->id,
    ])->assertSee(
        __('No IP Whitelist found'),
    );
});

it('can create ip whitelist', function () {
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $ipwhitelist = IpWhitelist::factory()->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ListIpwhitelist::class, [
        'record' => $project->id,
    ])
        ->callAction('new-ipwhitelist',
            [
                'ip_address' => $ipwhitelist->ip_address,
                'description' => $ipwhitelist->description,
            ]
        )
        ->assertHasNoErrors()
        ->assertNotified(__('IP Whitelist Successful'));

    assertDatabaseHas(IpWhitelist::class, [
        'ip_address' => $ipwhitelist->ip_address,
        'description' => $ipwhitelist->description,
        'project_id' => $project->id,
    ]);
});

it('validates required fields when creating ip whitelist', function () {
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $ipwhitelist = IpWhitelist::factory()->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ListIpwhitelist::class, [
        'record' => $project->id,
    ])
        ->callAction('new-ipwhitelist',
            [
                'ip_address' => 'invalid-ip',
            ]
        )
        ->assertHasErrors();

    expect($ipwhitelist::count())->toBe(0);
});

it('can delete ip whitelist', function () {
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $ipWhitelist = IpWhitelist::factory()->create([
        'project_id' => $project->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    $response = Livewire::test(ListIpwhitelist::class, [
        'record' => $project->id,
    ]);

    $response->assertSuccessful();

    $response->callTableAction('delete', $ipWhitelist);

    expect(IpWhitelist::count())->toBe(0);
});
it('can list active and inactive status color in project company', function () {
    $projectsActive = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    $projectsInactive = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'inactive']);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ListProjects::class, [
        'record' => $projectsActive,
    ])->assertCanSeeTableRecords([
        $projectsActive,
        $projectsInactive,
    ])

        ->assertSeeHtml('style="--c-50:var(--success-50);--c-400:var(--success-400);--c-600:var(--success-600);"') // success-color
        ->assertSeeHtml('style="--c-50:var(--danger-50);--c-400:var(--danger-400);--c-600:var(--danger-600);"'); // danger-color;
});

it('can list active and inactive status icon in project company', function () {
    $projectsActive = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    $projectsInactive = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'inactive']);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ProjectResource\Pages\ListProjects::class, [
        'record' => $projectsActive,
    ])->assertCanSeeTableRecords([
        $projectsActive,
        $projectsInactive,
    ])
        ->assertSeeHtml('M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z') // heroicon-o-check-circle
        ->assertSeeHtml('M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z'); // heroicon-o-x-circle;
}
);
