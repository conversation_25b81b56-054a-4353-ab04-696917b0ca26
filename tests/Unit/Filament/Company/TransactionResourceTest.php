<?php

declare(strict_types=1);

use App\Filament\Company\Resources\TransactionResource;
use App\Models\Company;
use App\Models\Transaction;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        TransactionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        TransactionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        TransactionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('shows empty state when company has no transaction', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(TransactionResource\Pages\ListTransactions::class)->assertSee(
        __('No transactions found'),
    );
});

it('can list transaction company', function () {
    $transaction = Transaction::factory()->count(5)->create([
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $transaction,
    ])->assertCanSeeTableRecords($transaction);
});

it('renders the correct icon for each state', function () {
    $transaction = Transaction::factory()->create([
        'company_id' => $this->company->id,
        'action_type' => 'charge',
    ]);

    $transactionDeposit = Transaction::factory()->create([
        'company_id' => $this->company->id,
        'action_type' => 'deposit',
        'status' => 'completed',
    ]);

    $transactionWithdraw = Transaction::factory()->create([
        'company_id' => $this->company->id,
        'action_type' => 'withdraw',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $transaction,
    ])
        ->assertCanSeeTableRecords([$transaction, $transactionDeposit, $transactionWithdraw])
        ->assertSeeHtml('style="--c-50:var(--warning-50);--c-400:var(--warning-400);--c-600:var(--warning-600);"') // warning-color
        ->assertSeeHtml('style="--c-50:var(--success-50);--c-400:var(--success-400);--c-600:var(--success-600);"') // success-color
        ->assertSeeHtml('style="--c-50:var(--danger-50);--c-400:var(--danger-400);--c-600:var(--danger-600);"'); // danger-color
});

it('renders the correct badge color for each status', function () {
    $statuses = [
        'pending' => 'warning',
        'completed' => 'success',
        'rejected' => 'danger',
        'custom' => 'info', // default case
    ];

    $transactions = collect();
    foreach ($statuses as $status => $color) {
        $transactions->push(Transaction::factory()->create([
            'company_id' => $this->company->id,
            'status' => $status,
        ]));
    }

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $livewire = livewire(TransactionResource\Pages\ListTransactions::class, [
        'record' => $transactions,
    ])->assertCanSeeTableRecords($transactions);

    foreach ($statuses as $status => $color) {
        $livewire->assertSeeHtml('style="--c-50:var(--'.$color.'-50);--c-400:var(--'.$color.'-400);--c-600:var(--'.$color.'-600);"');
    }
});
