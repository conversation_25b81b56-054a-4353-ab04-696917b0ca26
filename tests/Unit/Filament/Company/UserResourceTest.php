<?php

declare(strict_types=1);

use App\Filament\Company\Resources\UserResource;
use App\Models\Company;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        UserResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        UserResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        UserResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can list users', function () {
    $users = User::factory()->count(5)->create();
    $this->company->users()->attach($users);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(UserResource\Pages\ListUsers::class, [
        'record' => $users,
    ])->assertCanSeeTableRecords($users);
});

it('can invite user to company', function () {
    Mail::fake();
    $email = Faker\Factory::create()->email;

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(UserResource\Pages\ListUsers::class)
        ->callAction('inviteUser', ['email' => $email])
        ->assertHasNoErrors()
        ->assertNotified(__('Invitation Sent'));
});

it('send notification if email already exist', function () {
    Mail::fake();
    $email = Faker\Factory::create()->email;
    $user = User::factory()->create(['email' => $email]);
    $this->company->users()->attach($user);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(UserResource\Pages\ListUsers::class)
        ->callAction('inviteUser', ['email' => $email])
        ->assertHasNoErrors()
        ->assertNotified(__('Invitation Failed'));
});

it('can render edit page with right permissions', function () {
    $user = User::factory()->create();
    $this->company->users()->attach($user);

    get(
        UserResource::getUrl('edit', ['tenant' => $this->company->id, 'record' => $user]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        UserResource::getUrl('edit', ['tenant' => $this->company->id, 'record' => $user]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        UserResource::getUrl('edit', ['tenant' => $this->company->id, 'record' => $user]),
    )->assertOk();
});

it('can update user', function () {
    $user = User::factory()->create([
        'name' => 'old name',
        'email' => '<EMAIL>',
    ]);
    $newUser = User::factory()->make([
        'name' => 'old name',
        'email' => '<EMAIL>',
        'password' => 'pass',
    ]);

    $this->company->users()->attach($user);

    actingAs(User::factory()->create()->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(UserResource\Pages\EditUser::class, [
        'tenant' => $this->company->id,
        'record' => $user->id,
    ])
        ->fillForm([
            'name' => $newUser->name,
            'email' => $newUser->email,
            'password' => $newUser->password,
            'password_confirmation' => $newUser->password,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($user->refresh())
        ->name->toEqual($newUser->name)
        ->email->toEqual($newUser->email);
});

it('validates required fields when updating user', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(UserResource\Pages\EditUser::class, [
        'tenant' => $this->company->id,
        'record' => $this->user->id,
    ])
        ->fillForm([
            'name' => null,
            'email' => null,
        ])
        ->call('save')
        ->assertHasFormErrors([
            'name',
            'email',
        ]);
});

it('renders the correct icon for each state', function () {
    $this->company->update(['default_user_id' => $this->user->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(UserResource\Pages\ListUsers::class, [
        'tenant' => $this->company->id,
    ])->assertSeeHtml('(--primary-600)'); // primary-color
});
