<?php

declare(strict_types=1);

use App\Filament\Company\Resources\CampaignMessageResource;
use App\Models\CampaignMessage;
use App\Models\City;
use App\Models\Company;
use App\Models\Provider;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        CampaignMessageResource::getUrl('index', [
            'tenant' => $this->company->id,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        CampaignMessageResource::getUrl('index', [
            'tenant' => $this->company->id,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        CampaignMessageResource::getUrl('index', [
            'tenant' => $this->company->id,
        ]),
    )->assertOk();
});

it('shows empty state when company has no sender', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(
        CampaignMessageResource\Pages\ListCampaignMessage::class,
    )->assertSee(__('No Campaign messages found'));
});

it('can list senders company', function () {
    $campaignMessage = CampaignMessage::factory()
        ->count(5)
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(CampaignMessageResource\Pages\ListCampaignMessage::class, [
        'record' => $campaignMessage,
    ])->assertCanSeeTableRecords($campaignMessage);
});

it('can render create page with right permissions', function () {
    get(
        CampaignMessageResource::getUrl('create', [
            'tenant' => $this->company->id,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        CampaignMessageResource::getUrl('create', [
            'tenant' => $this->company->id,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        CampaignMessageResource::getUrl('create', [
            'tenant' => $this->company->id,
        ]),
    )->assertOk();
});

it('can create campaign message', function () {
    $campaignMessage = CampaignMessage::factory()->make([
        'age_from' => 20,
        'age_to' => 30, // Make sure age_to > age_from
    ]);
    $city = City::factory()->create();
    $provider = Provider::first();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\CreateCampaignMessage::class)
        ->fillForm([
            'short_message' => $campaignMessage->short_message,
            'sex' => $campaignMessage->sex,
            'age_from' => $campaignMessage->age_from,
            'age_to' => $campaignMessage->age_to,
            'quantity' => $campaignMessage->quantity,
            'city_id' => $city->id,
            'provider_id' => $provider->id,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(CampaignMessage::class, [
        'short_message' => $campaignMessage->short_message,
        'sex' => $campaignMessage->sex,
        'age_from' => $campaignMessage->age_from,
        'age_to' => $campaignMessage->age_to,
        'quantity' => $campaignMessage->quantity,
        'company_id' => $this->company->id,
    ]);
});

it('can render edit page with right permissions', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
    ]);
    $city = City::factory()->create();
    $provider = Provider::first();
    $campaignMessage->cities()->attach($city);
    $campaignMessage->providers()->attach($provider);

    // Test if the user is not authenticated
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    // Test if the user is not a company owner
    actingAs($this->user);
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertForbidden();

    // set the user as a company owner
    actingAs($this->user->assignRole('company_owner'));

    // Test if the campaign message exists
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertOk();

    // Test if the campaign message does not exist
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => 1,
        ]),
    )->assertNotFound();

    // Test if the campaign message is active
    $campaignMessage->update(['status' => 'active']);
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertForbidden();

    // Test if the campaign message is rejected
    $campaignMessage->update(['status' => 'rejected']);
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertForbidden();

    // Test if the campaign message is inactive
    $campaignMessage->update(['status' => 'inactive']);
    get(
        CampaignMessageResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $campaignMessage,
        ]),
    )->assertForbidden();
});

it('can update campaign message', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
        'age_from' => 20,
        'age_to' => 30, // Make sure age_to > age_from
    ]);
    $newCampaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
        'age_from' => 25,
        'age_to' => 35, // Make sure age_to > age_from
    ]);

    $city = City::factory()->create();
    $provider = Provider::first();

    $campaignMessage->cities()->attach($city);
    $campaignMessage->providers()->attach($provider);

    actingAs(User::factory()->create()->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(CampaignMessageResource\Pages\EditCampaignMessage::class, [
        'record' => $campaignMessage->id,
    ])
        ->fillForm([
            'short_message' => $newCampaignMessage->short_message,
            'sex' => $newCampaignMessage->sex,
            'age_from' => $newCampaignMessage->age_from,
            'age_to' => $newCampaignMessage->age_to,
            'quantity' => $newCampaignMessage->quantity,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($campaignMessage->refresh())
        ->short_message->toEqual($newCampaignMessage->short_message)
        ->sex->toEqual($newCampaignMessage->sex)
        ->age_from->toEqual($newCampaignMessage->age_from)
        ->age_to->toEqual($newCampaignMessage->age_to)
        ->quantity->toEqual($newCampaignMessage->quantity);
});

it('validates required fields when creating campaign message', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\CreateCampaignMessage::class)
        ->fillForm([
            'short_message' => null,
            'sex' => null,
            'age_from' => null,
            'age_to' => null,
            'quantity' => null,
            'city_id' => null,
            'provider_id' => null,
        ])
        ->call('create')
        ->assertHasFormErrors([
            'short_message',
            'sex',
            'age_from',
            'age_to',
            'quantity',
            'provider_id',
        ]);
});

it('validates age_to must be greater than or equal to age_from', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    $city = City::factory()->create();
    $provider = Provider::first();

    livewire(CampaignMessageResource\Pages\CreateCampaignMessage::class)
        ->fillForm([
            'short_message' => 'Test message',
            'sex' => 'male',
            'age_from' => 30,
            'age_to' => 20, // Age_to less than age_from
            'quantity' => 5000,
            'city_id' => [$city->id],
            'provider_id' => [$provider->id],
        ])
        ->call('create')
        ->assertHasFormErrors(['age_to']);
});

it('validates quantity minimum value', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    $city = City::factory()->create();
    $provider = Provider::first();

    livewire(CampaignMessageResource\Pages\CreateCampaignMessage::class)
        ->fillForm([
            'short_message' => 'Test message',
            'sex' => 'male',
            'age_from' => 20,
            'age_to' => 30,
            'quantity' => 4999, // Less than minimum 5000
            'city_id' => [$city->id],
            'provider_id' => [$provider->id],
        ])
        ->call('create')
        ->assertHasFormErrors(['quantity']);
});

it('can delete campaign message', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\EditCampaignMessage::class, [
        'record' => $campaignMessage->id,
    ])
        ->callAction('delete');

    assertDatabaseMissing(CampaignMessage::class, ['id' => $campaignMessage->id]);
});

it('can delete campaign message from table', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\ListCampaignMessage::class)
        ->assertTableActionExists('delete')
        ->callTableAction('delete', $campaignMessage);

    assertDatabaseMissing(CampaignMessage::class, ['id' => $campaignMessage->id]);
});

// Skipping bulk delete test as it's not implemented in the resource
// it('can bulk delete campaign messages', function () {
//     $campaignMessages = CampaignMessage::factory()->count(3)->create([
//         'company_id' => $this->company->id,
//         'status' => 'pending',
//     ]);
//
//     actingAs($this->user->assignRole('company_owner'));
//     Filament::setTenant($this->company);
//
//     livewire(CampaignMessageResource\Pages\ListCampaignMessage::class)
//         ->assertTableBulkActionExists('delete')
//         ->callTableBulkAction('delete', $campaignMessages);
//
//     foreach ($campaignMessages as $campaignMessage) {
//         assertDatabaseMissing(CampaignMessage::class, ['id' => $campaignMessage->id]);
//     }
// });

it('cannot delete active campaign message', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'active',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\ListCampaignMessage::class)
        ->assertTableActionHidden('delete', $campaignMessage);
});

it('displays correct table columns', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'short_message' => 'Test message',
        'age_from' => 20,
        'age_to' => 30,
        'sex' => 'male',
        'quantity' => 5000,
        'status' => 'pending',
    ]);

    $city = City::factory()->create(['name' => 'Test City']);
    $provider = Provider::first();

    $campaignMessage->cities()->attach($city);
    $campaignMessage->providers()->attach($provider);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CampaignMessageResource\Pages\ListCampaignMessage::class)
        ->assertCanSeeTableRecords([$campaignMessage])
        ->assertTableColumnExists('short_message')
        ->assertTableColumnExists('status')
        ->assertTableColumnExists('age_from')
        ->assertTableColumnExists('sex')
        ->assertTableColumnExists('providers.name')
        ->assertTableColumnExists('cities.name')
        ->assertTableColumnExists('quantity')
        ->assertSee('Test message')
        ->assertSee(__('pending'))
        ->assertSee('20 - 30')
        ->assertSee(__('male'))
        ->assertSee('Test City')
        ->assertSee('5000');
});

it('displays correct status badge colors', function () {
    $statuses = ['active', 'inactive', 'pending', 'rejected', 'processing'];
    $campaignMessages = [];

    foreach ($statuses as $status) {
        $campaignMessages[] = CampaignMessage::factory()->create([
            'company_id' => $this->company->id,
            'status' => $status,
        ]);
    }

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $livewire = livewire(CampaignMessageResource\Pages\ListCampaignMessage::class);

    // Check that all statuses are displayed with correct translations
    foreach ($statuses as $status) {
        $livewire->assertSee(__($status));
    }
});

it('correctly handles relationships', function () {
    $campaignMessage = CampaignMessage::factory()->create([
        'company_id' => $this->company->id,
        'status' => 'pending', // Make sure it's pending so we can edit it
    ]);

    $cities = City::factory()->count(3)->create();
    $providers = Provider::factory()->count(2)->create();

    $campaignMessage->cities()->attach($cities);
    $campaignMessage->providers()->attach($providers);

    // Test relationships
    expect($campaignMessage->cities)->toHaveCount(3);
    expect($campaignMessage->providers)->toHaveCount(2);
    expect($campaignMessage->company->id)->toBe($this->company->id);
});

it('has correct navigation group and labels', function () {
    expect(CampaignMessageResource::getNavigationGroup())->toBe(__('Messages'));
    expect(CampaignMessageResource::getLabel())->toBe(__('CampaignMessage'));
    expect(CampaignMessageResource::getPluralModelLabel())->toBe(__('CampaignMessage'));
});
