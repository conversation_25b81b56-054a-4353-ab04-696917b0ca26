<?php

declare(strict_types=1);

use App\Filament\Company\Pages\Profile;
use App\Models\Company;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function () {
    seed();
    Storage::fake('local');

    Filament::setCurrentPanel(Filament::getPanel('company'));
    $this->company = Company::factory()->create(['verified_at' => null]);
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render profile page', function () {
    // Act as the user
    actingAs($this->user);

    // Test the page renders
    get(Profile::getUrl(['tenant' => $this->company->id]))
        ->assertSuccessful()
        ->assertSee(__('Company Profile'))
        ->assertSee($this->company->name)
        ->assertSee($this->company->email);
});

it('can download required documents', function () {
    // Create a test PDF file
    $filePath = public_path('documents');
    if (! file_exists($filePath)) {
        mkdir($filePath, 0777, true);
    }
    file_put_contents("{$filePath}/required_documents.pdf", 'test content');

    // Act as the user
    actingAs($this->user);
    Filament::setTenant($this->company);

    // Test the download action
    livewire(Profile::class)
        ->callAction('download_required_documents')
        ->assertSuccessful();
});

it('displays company documents and logo upload fields', function () {
    // Act as the user
    actingAs($this->user);
    Filament::setTenant($this->company);

    // Test the page contains upload fields
    livewire(Profile::class)
        ->assertSuccessful()
        ->assertFormFieldExists('company_documents')
        ->assertFormFieldExists('logo');
});

it('saves company relationships without errors', function () {
    actingAs($this->user);
    Filament::setTenant($this->company);

    livewire(Profile::class)
        ->call('save')
        ->assertSuccessful();

    expect(true)->toBeTrue();
});
