<?php

declare(strict_types=1);

use App\Filament\Company\Resources\MessageResource;
use App\Filament\Company\Resources\MessageResource\Pages\CreateMessage;
use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\Feature;
use App\Models\FeaturePlan;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Sender;
use App\Models\Setting;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseEmpty;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        MessageResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        MessageResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        MessageResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('shows empty state when company has no message', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(MessageResource\Pages\ListMessages::class)->assertSee(
        __('No message found'),
    );
});

it('can list message company', function () {
    // create sender and attach to company
    $senders = Sender::factory()->create();
    $this->company->senders()->attach($senders);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $messages = Message::factory()
        ->count(5)
        ->create([
            'company_id' => $this->company->id,
            'sender_id' => $senders->id,
            'project_id' => $project->id,
            'message_type' => 'sms',
            'send_type' => 'single',
            'message_consumption' => 1,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(MessageResource\Pages\ListMessages::class, [
        'record' => $messages,
    ])->assertCanSeeTableRecords($messages);
});

it('can render create page with right permissions', function () {
    get(
        MessageResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        MessageResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        MessageResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can create message company', function () {
    // create sender and attach to company
    $this->company->auto_approve = true;

    $sender = Sender::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create contact group and attach to company
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);
    $project->contact_groups()->attach($contactGroups);

    // create contact and attach to contact group
    $contacts = Contact::factory()->withProviders()->count(5)
        ->create(['contact_group_id' => $contactGroups->id]);
    $contact = $contacts->first();

    // create subscription
    $feature = Feature::first();
    $plan = Plan::first();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
            'deleted_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => $contactGroups->id,
            'send_type' => 'single',
            'message_type' => 'sms',
            'destination_addr' => [
                [
                    'contact_id' => $contact->id,
                    'number' => $contact->phone,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors();
    @$undoRepeaterFake();

    assertDatabaseHas(Message::class, [
        'sender_id' => $sender->id,
        'company_id' => $this->company->id,
        'project_id' => $project->id,
        'short_message' => 'test message',
        'message_type' => 'sms',
        'send_type' => 'single',
    ]);
});

it('can create message with multiple contacts', function () {
    // create sender and attach to company
    $sender = Sender::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create contact group and attach to company
    $contactGroup = ContactGroup::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);
    $project->contact_groups()->attach($contactGroup);

    // create contacts and attach to contact group
    $contacts = Contact::factory()->withProviders()->count(5)
        ->create(['contact_group_id' => $contactGroup->id]);

    // create subscription
    $feature = Feature::first();
    $plan = Plan::first();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
            'deleted_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => $contactGroup->id,
            'send_type' => 'multiple',
            'message_type' => 'sms',
            'destination_addr' => $contacts->map(fn ($contact) => [
                'contact_id' => $contact->id,
                'number' => $contact->phone,
            ])->toArray(),
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertNotified();
    @$undoRepeaterFake();

    assertDatabaseHas(Message::class, [
        'sender_id' => $sender->id,
        'company_id' => $this->company->id,
        'project_id' => $project->id,
        'short_message' => 'test message',
        'message_type' => 'sms',
        'send_type' => 'multiple',
    ]);
});

it('can not create message when no subscription found for feature sms', function () {
    // create sender and attach to company
    $sender = Sender::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create contact group and attach to company
    $contactGroup = ContactGroup::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);
    $project->contact_groups()->attach($contactGroup);

    // create contacts and attach to contact group
    $contacts = Contact::factory()->withProviders()->count(5)
        ->create(['contact_group_id' => $contactGroup->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => $contactGroup->id,
            'send_type' => 'multiple',
            'message_type' => 'sms',
            'destination_addr' => $contacts->map(fn ($contact) => [
                'contact_id' => $contact->id,
                'number' => $contact->phone,
            ])->toArray(),
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertNotified('Error');
    @$undoRepeaterFake();
});

it('can view detail messages', function () {
    // create sender and attach to company
    $senders = Sender::factory()->create();
    $this->company->senders()->attach($senders);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id]);

    $message = Message::factory()
        ->create([
            'company_id' => $this->company->id,
            'sender_id' => $senders->id,
            'project_id' => $project->id,
            'message_type' => 'sms',
            'send_type' => 'single',
            'message_consumption' => 1,
        ]);

    $message_recipients = MessageReceipt::Factory()
        ->count(5)
        ->create([
            'message_id' => $message->id,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(MessageResource\Pages\ViewMessage::class, [
        'record' => $message,
    ])->assertCanSeeTableRecords($message_recipients);
});

it('validates required fields when creating message', function () {
    $contactGroup = ContactGroup::factory()->create();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => null,
            'short_message' => null,
            'balance_type' => null,
            'project_id' => null,
            'contact_group_id' => $contactGroup->id,
            'send_type' => 'single',
            'message_type' => null,
            'destination_addr' => [
                [
                    'number' => '1234567890',
                ],
            ],
        ])
        ->call('create')
        ->assertHasFormErrors([
            'sender_id',
            'short_message',
            'balance_type',
            'message_type',
            'destination_addr.0.number',
        ]);
    @$undoRepeaterFake();
});

it('can not create message when project is inactive', function () {
    // create sender and attach to company
    $sender = Sender::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'inactive']);

    // create subscription
    $feature = Feature::first();
    $plan = Plan::first();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
            'deleted_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => null,
            'send_type' => 'single',
            'message_type' => 'sms',
            'destination_addr' => [
                [
                    'number' => '00218912345678',
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertNotified('Error');
    @$undoRepeaterFake();

    assertDatabaseEmpty(Message::class);
});

it('checks balance for balance type', function () {
    $company = Company::factory()->create();
    $company->transactions()->create(['amount' => 1000000, 'status' => 'completed', 'action_type' => 'deposit']);
    $receivers = Collection::make([['id' => '1', 'number' => '00218912345678']]);
    $createMessage = new CreateMessage();

    $createMessage->checkBalance(1, $receivers, $company, 'balance');
    Notification::assertNotNotified();
});

it('throws an exception for insufficient balance', function () {
    $company = Company::factory()->create();
    $company->transactions()->create(['amount' => 1, 'status' => 'completed', 'action_type' => 'deposit']);
    $receivers = collect([['id' => '1', 'number' => '00218912345678']]);
    $parts = 10;
    $type = 'balance';

    try {
        $createMessage = new CreateMessage();
        $reflectionMethod = new ReflectionMethod($createMessage, 'checkBalance');
        $reflectionMethod->invoke($createMessage, $parts, $receivers, $company, $type);
    } catch (\Filament\Support\Exceptions\Halt $e) {
        // Handle the exception, e.g., log the error or notify the user
        Notification::assertNotified(
            Notification::make()
                ->title(__('Error'))
                ->body(__('Insufficient balance'))
                ->danger());
    }
});

it('throws an exception for no found project', function () {
    $company = Company::factory()->create();
    $company->transactions()->create(['amount' => 1, 'status' => 'completed', 'action_type' => 'deposit']);
    $receivers = collect([['id' => '1', 'number' => '00218912345678']]);
    $parts = 10;
    $type = 'subscription';

    try {
        $createMessage = new CreateMessage();
        $reflectionMethod = new ReflectionMethod($createMessage, 'checkBalance');
        $reflectionMethod->invoke($createMessage, $parts, $receivers, $company, $type);
    } catch (\Filament\Support\Exceptions\Halt $e) {
        // Handle the exception, e.g., log the error or notify the user
        Notification::assertNotified(
            Notification::make()
                ->title(__('Error'))
                ->body(__('Project not found'))
                ->danger());
    }
});

it('throws an exception for no found project when check project', function () {
    try {
        $createMessage = new CreateMessage();
        $reflectionMethod = new ReflectionMethod($createMessage, 'checkProject');
        $reflectionMethod->invoke($createMessage);
    } catch (\Filament\Support\Exceptions\Halt $e) {
        // Handle the exception, e.g., log the error or notify the user
        Notification::assertNotified(
            Notification::make()
                ->title(__('Error'))
                ->body(__('Project not found'))
                ->danger());
    }
});

it('retrieves numbers from contact group', function () {
    $contactGroup = ContactGroup::factory()->create();
    $contacts = Contact::factory()->count(3)->create(['contact_group_id' => $contactGroup->id]);

    // Instantiate the CreateMessage class
    $createMessage = new CreateMessage();
    $numbers = $createMessage->getNumberFromContact($contactGroup->id);

    // Assert that the numbers are retrieved correctly
    expect($numbers)->toHaveCount(3);
    foreach ($contacts as $contact) {
        expect($numbers)->toContain(['id' => $contact->id, 'number' => $contact->phone]);
    }
});

it('sent notification if project id is empty', function () {
    $data = ['project_id' => ''];
    try {
        $createMessage = new CreateMessage();
        $reflectionMethod = new ReflectionMethod($createMessage, 'validateAndFindProject');
        $reflectionMethod->invoke($createMessage, $data);
    } catch (\Filament\Support\Exceptions\Halt $e) {
        // Handle the exception, e.g., log the error or notify the user
        Notification::assertNotified(
            Notification::make()
                ->title(__('Error'))
                ->body(__('Project ID is required for subscription balance type.'))
                ->danger());
    }
});

it('sent notification if project not found', function () {
    $data = ['project_id' => '11'];
    try {
        $createMessage = new CreateMessage();
        $reflectionMethod = new ReflectionMethod($createMessage, 'validateAndFindProject');
        $reflectionMethod->invoke($createMessage, $data);
    } catch (\Filament\Support\Exceptions\Halt $e) {
        // Handle the exception, e.g., log the error or notify the user
        Notification::assertNotified(
            Notification::make()
                ->title(__('Error'))
                ->body(__('Project not found.'))
                ->danger());
    }
});

it('check BalanceConsumption', function () {
    $data = ['balance_type' => 'balance'];
    $project = null;
    $parts = 1;
    $receivers = collect([['id' => '1', 'number' => '00218912345678']]);
    $message = Message::factory()->create(['message_type' => 'sms']);

    $createMessage = new CreateMessage();
    $reflectionMethod = new ReflectionMethod($createMessage, 'handleBalanceConsumption');
    $reflectionMethod->invoke($createMessage, $data, $project, $parts, $receivers, $message);

    assertDatabaseHas(Transaction::class, [
        'action_type' => 'charge',
        'status' => 'completed',
    ]);
});

it('calculates the total cost of a message', function () {
    $createMessage = new CreateMessage();
    $reflectionMethod = new ReflectionMethod($createMessage, 'messageTotalCost');
    $result = $reflectionMethod->invoke($createMessage, 2, 5);
    expect($result)->toBe(2350);
});

it('throws exception when single_sms_cost setting is missing', function () {
    Setting::where('key', 'single_sms_cost')->delete();

    $createMessage = new CreateMessage();
    $reflectionMethod = new ReflectionMethod($createMessage, 'messageTotalCost');

    expect(fn () => $reflectionMethod->invoke($createMessage, 1, 1))
        ->toThrow(Exception::class, 'Single SMS cost not found');
});

it('calculates SMS parts when no transaction exists', function () {
    $message = Message::factory()->create([
        'short_message' => 'test message',
        'message_type' => 'sms',
        'transaction_id' => null,
    ]);

    MessageReceipt::factory()->create([
        'message_id' => $message->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageResource\Pages\ListMessages::class)
        ->assertTableColumnStateSet('transaction_id', 1, record: $message)
        ->assertTableColumnFormattedStateSet('transaction_id', 1 .' '.__('Message'), record: $message);
});

it('renders the correct icon for each state', function () {
    $message = Message::factory()->create([
        'short_message' => 'test message',
        'message_type' => 'sms',
        'transaction_id' => null,
        'status' => 'approved',
    ]);

    $pending = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'pending',
    ]);

    $sent = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'sent',
    ]);

    $delivered = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'delivered',
    ]);

    $expired = MessageReceipt::factory()->create([
        'message_id' => $message->id,
        'status' => 'expired',
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageResource\Pages\ViewMessage::class, [
        'record' => $message,
    ])
        ->assertCanSeeTableRecords([$pending, $delivered, $sent, $expired])
        ->assertSeeHtml('M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z') // heroicon-o-clock
        ->assertSeeHtml('M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z') // heroicon-o-check-circle
        ->assertSeeHtml('M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z') // heroicon-o-x-circle
        ->assertSeeHtml('style="--c-500:var(--warning-500);"') // warning-color
        ->assertSeeHtml('style="--c-500:var(--info-500);"') // info-color
        ->assertSeeHtml('style="--c-500:var(--success-500);"') // success-color
        ->assertSeeHtml('style="--c-500:var(--danger-500);"'); // danger-color
});

it('can create message with all contacts', function () {
    // create sender and attach to company
    $sender = Sender::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);

    // create contact group and attach to company
    $contactGroup = ContactGroup::factory()
        ->create(['company_id' => $this->company->id, 'status' => 'active']);
    $project->contact_groups()->attach($contactGroup);

    // create contacts and attach to contact group
    $contacts = Contact::factory()->withProviders()->count(5)
        ->create(['contact_group_id' => $contactGroup->id]);

    // create subscription
    $feature = Feature::first();
    $plan = Plan::first();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
            'deleted_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => $contactGroup->id,
            'send_type' => 'multiple',
            'message_type' => 'sms',
            'select_all' => true,
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertNotified();

    assertDatabaseHas(Message::class, [
        'sender_id' => $sender->id,
        'company_id' => $this->company->id,
        'project_id' => $project->id,
        'short_message' => 'test message',
        'message_type' => 'sms',
        'send_type' => 'multiple',
        'message_consumption' => 5,
    ]);
});

it('can create message with multiple contacts without verification at company', function () {

    $company = Company::factory()->create(['verified_at' => null]);

    // create sender and attach to company
    $sender = Sender::factory()
        ->create(['company_id' => $company->id, 'status' => 'active']);

    // create project and attach to company
    $project = Project::factory()
        ->create(['company_id' => $company->id, 'status' => 'active']);

    // create contact group and attach to company
    $contactGroup = ContactGroup::factory()
        ->create(['company_id' => $company->id, 'status' => 'active']);
    $project->contact_groups()->attach($contactGroup);

    // create contacts and attach to contact group
    $contacts = Contact::factory()->withProviders()->count(5)
        ->create(['contact_group_id' => $contactGroup->id]);

    // create subscription
    $feature = Feature::first();
    $plan = Plan::first();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
            'deleted_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($company);

    $undoRepeaterFake = Repeater::fake();
    livewire(CreateMessage::class)
        ->fillForm([
            'sender_id' => $sender->id,
            'short_message' => 'test message',
            'balance_type' => 'subscription',
            'project_id' => $project->id,
            'contact_group_id' => $contactGroup->id,
            'send_type' => 'multiple',
            'message_type' => 'sms',
            'destination_addr' => $contacts->map(fn ($contact) => [
                'contact_id' => $contact->id,
                'number' => $contact->phone,
            ])->toArray(),
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertNotified();
    @$undoRepeaterFake();

    assertDatabaseHas(Message::class, [
        'sender_id' => $sender->id,
        'company_id' => $company->id,
        'project_id' => $project->id,
        'short_message' => 'test message',
        'message_type' => 'sms',
        'send_type' => 'multiple',
    ]);
});
