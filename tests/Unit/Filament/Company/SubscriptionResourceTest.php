<?php

declare(strict_types=1);

use App\Filament\Company\Resources\SubscriptionResource;
use App\Models\Company;
use App\Models\Feature;
use App\Models\FeaturePlan;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        SubscriptionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        SubscriptionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        SubscriptionResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('shows empty state when company has no subscription', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class)->assertSee(
        __('No subscription found'),
    );
});

it('can list subscriptions company', function () {
    // create subscription
    $feature = Feature::factory()->create();
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);
    $subscriptions = Subscription::factory()->count(5)->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $subscriptions,
    ])->assertCanSeeTableRecords($subscriptions);
});

it('see subscription data in table status color success', function () {

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])->assertCanSeeTableRecords($subscriptions);
});

it('see subscription data in table status color danger', function () {

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->subMonth(),
            'canceled_at' => null,
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])
        ->assertTableColumnFormattedStateSet('id', __('Expired'), $subscriptions->first())
        ->assertCanSeeTableRecords($subscriptions);
});

it('see subscription data in table status color danger canceled', function () {

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscriptions = Subscription::factory()
        ->count(5)
        ->create([
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->subMonth(),
            'canceled_at' => now(),
        ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])
        ->assertTableColumnFormattedStateSet('id', __('Canceled'), $subscriptions->first())
        ->assertCanSeeTableRecords($subscriptions);
});

it('can create subscription', function () {

    App\Models\Transaction::factory()->create([
        'company_id' => $this->company->id,
        'amount' => 10000000,
        'status' => 'completed',
        'action_type' => 'deposit',
    ]);

    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));

    Filament::setTenant($this->company);
    // call subscribe_to in list subscription page
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])
        ->callAction('subscribe_to',
            [
                'plan_id' => $plan->id,
                'project_id' => $project->id,
            ]
        )
        ->assertHasNoErrors()
        ->assertNotified(__('Subscription Successful'));
});

it('can not create subscription when duplicate features', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create();
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    $subscription = Subscription::factory()->create(
        [
            'plan_id' => $plan->id,
            'project_id' => $project->id,
            'expired_at' => now()->addMonth(),
            'canceled_at' => null,
        ],
    );

    actingAs($this->user->assignRole('company_owner'));

    Filament::setTenant($this->company);
    // call subscribe_to in list subscription page
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])
        ->callAction('subscribe_to',
            [
                'plan_id' => $plan->id,
                'project_id' => $project->id,
            ]
        )
        ->assertHasNoErrors()
        ->assertNotified(__('Duplicate Plan Features'));
});

it('Check if the company has sufficient balance', function () {
    $project = Project::factory()->create(['company_id' => $this->company->id]);
    $plan = Plan::factory()->create(['price' => 10000000]);
    FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));

    Filament::setTenant($this->company);
    // call subscribe_to in list subscription page
    livewire(SubscriptionResource\Pages\ListSubscriptions::class, [
        'record' => $project->id,
    ])
        ->callAction('subscribe_to',
            [
                'plan_id' => $plan->id,
                'project_id' => $project->id,
            ]
        )
        ->assertHasNoErrors()
        ->assertNotified(__('Insufficient Balance'));
});
