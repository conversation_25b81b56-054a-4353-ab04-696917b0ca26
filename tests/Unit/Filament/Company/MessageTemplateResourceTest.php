<?php

declare(strict_types=1);

use App\Enums\MessageTemplateParameterType;
use App\Filament\Company\Resources\MessageTemplateResource;
use App\Models\Company;
use App\Models\MessageTemplate;
use App\Models\User;
use Filament\Facades\Filament;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        MessageTemplateResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        MessageTemplateResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        MessageTemplateResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can create message template', function () {
    $messageTemplate = MessageTemplate::factory()->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'display_name' => $messageTemplate->display_name,
            'short_name' => $messageTemplate->short_name,
            'content' => $messageTemplate->content,
            'type' => $messageTemplate->type,
            'status' => $messageTemplate->status,
            'project_id' => $messageTemplate->project_id,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

});

it('can create message template with parameters', function () {
    $messageTemplate = MessageTemplate::factory()->withParameters(2)->make();
    $parameters = $messageTemplate->parameters;

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'display_name' => $messageTemplate->display_name,
            'short_name' => $messageTemplate->short_name,
            'content' => $messageTemplate->content,
            'type' => $messageTemplate->type,
            'status' => $messageTemplate->status,
            'project_id' => $messageTemplate->project_id,
            'parameters' => $parameters,
        ])
        ->call('create')
        ->assertHasNoFormErrors();
});

it('can delete message template using table bulk action', function () {
    $messageTemplate = MessageTemplate::factory()->create([
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class)
        ->callTableBulkAction('delete', [$messageTemplate])
        ->assertHasNoTableActionErrors();
});

it('can delete message template with parameters using table bulk action', function () {
    $messageTemplate = MessageTemplate::factory()->withParameters(2)->create([
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class)
        ->callTableBulkAction('delete', [$messageTemplate])
        ->assertHasNoTableActionErrors();
});

it('can extract parameter from content', function () {
    $messageTemplate = MessageTemplate::factory()->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'content' => 'Hello {{name}}',
        ])
        ->assertFormSet([
            'parameters' => [
                [
                    'name' => 'name',
                    'type' => 'string',
                ],
            ],
        ]);

});

it('fails validation when parameter name is not unique', function () {
    $messageTemplate = MessageTemplate::factory()
        ->withParameters(2)
        ->create(['company_id' => $this->company->id]);

    $existingParamName = $messageTemplate->parameters()->first()->name;

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    Livewire::test(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'display_name' => 'Test',
            'short_name' => 'test',
            'content' => 'Hello {{name}}',
            'type' => 'marketing',
            'project_id' => $messageTemplate->project_id,
            'parameters' => [
                [
                    'name' => $existingParamName,
                    'max_limit' => 10,
                    'type' => MessageTemplateParameterType::string->value,
                ],
                [
                    'name' => $existingParamName,
                    'max_limit' => 10,
                    'type' => MessageTemplateParameterType::string->value,
                ],
            ],
        ])
        ->call('create')
        ->assertHasErrors();
});

it('validation when parameter name is unique', function () {
    $messageTemplate = MessageTemplate::factory()
        ->withParameters(2)
        ->create(['company_id' => $this->company->id]);

    $existingParamName = $messageTemplate->parameters()->first()->name;

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    Livewire::test(MessageTemplateResource\Pages\CreateMessageTemplate::class)
        ->fillForm([
            'display_name' => 'Test',
            'short_name' => 'test',
            'content' => 'Hello {{name}}',
            'type' => 'marketing',
            'project_id' => $messageTemplate->project_id,
            'parameters' => [
                [
                    'name' => $existingParamName.'1',
                    'max_limit' => 10,
                    'type' => MessageTemplateParameterType::string->value,
                ],
                [
                    'name' => $existingParamName.'2',
                    'max_limit' => 10,
                    'type' => MessageTemplateParameterType::string->value,
                ],
            ],
        ])
        ->call('create')
        ->assertHasNoErrors();
});

it('can render active status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create([
        'status' => 'active',
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['success', 'active']);
});

it('can render inactive status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'inactive', 'company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['danger', __('inactive')]);
});

it('can render pending status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'pending', 'company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['warning', __('pending')]);
});

it('can render rejected status in table', function () {
    $messageTemplate = MessageTemplate::factory()->create(['status' => 'rejected', 'company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(MessageTemplateResource\Pages\ListMessageTemplates::class, [
        'record' => $messageTemplate->id,
    ])->assertSeeHtmlInOrder(['danger', __('rejected')]);
});
