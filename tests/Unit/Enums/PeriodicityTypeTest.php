<?php

declare(strict_types=1);

use App\Enums\PeriodicityType;
use Carbon\CarbonImmutable;

it('should return all periodicity types', function () {
    $types = PeriodicityType::all();

    expect($types)->toBe([
        'Yearly',
        'Monthly',
        'Quarterly',
        'Weekly',
        'Daily',
    ]);

});

it('returns correct expiration date modifier for yearly periodicity', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $modifier = PeriodicityType::getExpirationDateModifier('Yearly', $startDate, $quantity);
    $expirationDate = $modifier();
    expect($expirationDate->year)->toBe($startDate->year + $quantity);
});

it('returns correct expiration date modifier for monthly periodicity', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $modifier = PeriodicityType::getExpirationDateModifier('Monthly', $startDate, $quantity);
    $expirationDate = $modifier();
    expect($expirationDate->month)->toBe($startDate->month + $quantity);
});

it('returns correct expiration date modifier for quarterly periodicity', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $modifier = PeriodicityType::getExpirationDateModifier('Quarterly', $startDate, $quantity);
    $expirationDate = $modifier();
    expect($expirationDate->month)->toBe($startDate->month + 3 * $quantity);
});

it('returns correct expiration date modifier for weekly periodicity', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $modifier = PeriodicityType::getExpirationDateModifier('Weekly', $startDate, $quantity);
    $expirationDate = $modifier();
    expect($expirationDate->weekOfYear)->toBe($startDate->weekOfYear + $quantity);
});

it('returns correct expiration date modifier for daily periodicity', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $modifier = PeriodicityType::getExpirationDateModifier('Daily', $startDate, $quantity);
    $expirationDate = $modifier();
    expect($expirationDate->dayOfYear)->toBe($startDate->dayOfYear + $quantity);
});

it('throws exception for invalid periodicity type', function () {
    $startDate = CarbonImmutable::now();
    $quantity = 1;
    $invalidType = 'InvalidType';
    expect(fn () => PeriodicityType::getExpirationDateModifier($invalidType, $startDate, $quantity))
        ->toThrow(InvalidArgumentException::class);
});
