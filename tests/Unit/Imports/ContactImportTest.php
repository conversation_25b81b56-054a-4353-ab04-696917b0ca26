<?php

declare(strict_types=1);

use App\Imports\ContactImport;
use App\Models\Contact;
use App\Models\ContactGroup;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('imports contacts from excel file', function () {
    $group = ContactGroup::factory()->create();
    $import = new ContactImport($group->id);

    $rows = [
        [
            'name' => '<PERSON>',
            'phone' => '00218911234567',
            'sex' => 'male',
            'births' => '1990-01-01',
            'city' => 'Tripoli',
        ],
        [
            'name' => '<PERSON>',
            'phone' => '00218921234567',
            'sex' => 'female',
            'births' => '1992-02-02',
            'city' => 'Benghazi',
        ],
    ];

    foreach ($rows as $row) {
        $contact = $import->model($row);
        $contact->save();
    }

    // Assert
    expect(Contact::count())->toBe(2)
        ->and(Contact::first())
        ->contact_group_id->toBe($group->id)
        ->name->toBe('<PERSON>e')
        ->phone->toBe('00218911234567')
        ->sex->toBe('male')
        ->city->toBe('Tripoli');
});

it('validates required fields', function () {
    // Arrange
    $group = ContactGroup::factory()->create();
    $import = new ContactImport($group->id);

    // Assert
    expect($import->rules())->toHaveKey('name')
        ->and($import->rules())->toHaveKey('phone')
        ->and($import->rules()['name'])->toContain('required')
        ->and($import->rules()['phone'])->toContain('required');
});

it('validates phone number format', function () {
    // Arrange
    $group = ContactGroup::factory()->create();
    $import = new ContactImport($group->id);

    // Get the phone validation rules
    $rules = $import->rules()['phone'];

    // Assert that PhoneNumberRule is included
    expect($rules)->toBeArray()
        ->and($rules[1])->toBeInstanceOf(App\Rules\PhoneNumberRule::class);
});

it('skips invalid rows', function () {
    // Arrange
    $group = ContactGroup::factory()->create();

    $content = <<<'EOT'
    name,phone,sex,births,city
    "John Doe","00218911234567","male","1990-01-01","Tripoli"
    "Invalid Contact","invalid_phone","male","1990-01-01","Tripoli"
    "Jane Doe","00218921234567","female","1992-02-02","Benghazi"
    EOT;

    $filePath = storage_path('test.csv');
    file_put_contents($filePath, $content);

    try {
        // Act
        Excel::import(new ContactImport($group->id), $filePath);
    } catch (ValidationException $e) {
        // Expected exception for invalid rows
    }

    // Assert
    expect(Contact::count())->toBe(2)
        ->and(Contact::pluck('name')->toArray())->toEqual(['John Doe', 'Jane Doe']);

    // Cleanup
    unlink($filePath);
});

it('processes in chunks', function () {
    // Arrange
    $group = ContactGroup::factory()->create();
    $import = new ContactImport($group->id);

    // Assert
    expect($import->chunkSize())->toBe(200);
});

it('handles empty values correctly', function () {
    // Arrange
    $group = ContactGroup::factory()->create();
    $import = new ContactImport($group->id);

    $row = [
        'name' => 'John Doe',
        'phone' => '00218911234567',
        'sex' => null,
        'births' => null,
        'city' => null,
    ];

    // Act
    $contact = $import->model($row);

    // Assert
    expect($contact)
        ->name->toBe('John Doe')
        ->phone->toBe('00218911234567')
        ->sex->toBeNull()
        ->births->toBeNull()
        ->city->toBeNull();
});
