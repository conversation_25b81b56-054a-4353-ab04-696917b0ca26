<?php

declare(strict_types=1);

use App\Enums\PeriodicityType;
use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\FeaturePlan;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Subscription;
use Carbon\CarbonImmutable;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('gets feature by name', function () {
    $feature = Feature::factory()->create(['name' => 'sms']);
    $project = Project::factory()->create();

    expect($project->getFeature('sms'))
        ->toBeInstanceOf(Feature::class)
        ->name->toBe('sms');
});

it('throws exception for non-existent feature', function () {
    $project = Project::factory()->create();

    expect(fn () => $project->getFeature('non_existent'))
        ->toThrow(InvalidArgumentException::class, "Feature with name 'non_existent' does not exist.");
});

it('gets active subscription', function () {
    $project = Project::factory()->create();
    $subscription = Subscription::factory()
        ->for($project)
        ->create([
            'expired_at' => now()->addDays(30),
            'canceled_at' => null,
        ]);

    expect($project->getActiveSubscription())
        ->toBeInstanceOf(Subscription::class)
        ->id->toBe($subscription->id);
});

it('throws exception when no active subscription exists', function () {
    $project = Project::factory()->create();

    expect(fn () => $project->getActiveSubscription())
        ->toThrow(InvalidArgumentException::class, 'No active subscription found.');
});

it('gets active subscription by feature', function () {
    $feature = Feature::factory()->create(['name' => 'sms']);
    $plan = Plan::factory()->create();
    $project = Project::factory()->create();

    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
    ]);

    $subscription = Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    expect($project->getActiveSubscriptionByFeature('sms'))
        ->toBeInstanceOf(Subscription::class)
        ->id->toBe($subscription->id);
});

it('throws exception when no subscription found for feature', function () {
    $project = Project::factory()->create();

    expect(fn () => $project->getActiveSubscriptionByFeature('sms'))
        ->toThrow(InvalidArgumentException::class, 'No subscription found for feature sms');
});

it('subscribes to plan', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $plan = Plan::where('name', 'like', '%SMS%')
        ->where('periodicity_type', 'like', '%Quarterly%')->firstOrFail();
    $startDate = CarbonImmutable::now();

    $subscription = $project->subscribeTo($plan, $startDate);

    expect($subscription)
        ->toBeInstanceOf(Subscription::class)
        ->plan_id->toBe($plan->id)
        ->project_id->toBe($project->id)
        ->started_at->toDateTimeString()->toBe($startDate->toDateTimeString())
        ->expired_at->toDateTimeString()->toBe($startDate->addMonth(3)->toDateTimeString());
});

it('extends subscription', function () {
    $project = Project::factory()->create();
    $plan = Plan::factory()->create([
        'periodicity_type' => PeriodicityType::Monthly,
    ]);

    $expiredSubscription = Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->subDay(),
        ]);

    $project->extendSubscription();

    $expiredSubscription->refresh();
    expect($expiredSubscription->expired_at)->toBeGreaterThan(now());
});

it('throws exception when extending with active subscription', function () {
    $project = Project::factory()->create();
    Subscription::factory()
        ->for($project)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    expect(fn () => $project->extendSubscription())
        ->toThrow(InvalidArgumentException::class, 'There is an active subscription.');
});

it('throws exception when extending with no subscription', function () {
    $project = Project::factory()->create();
    Subscription::factory()
        ->for($project)
        ->create([
            'expired_at' => null,
        ]);

    expect(fn () => $project->extendSubscription())
        ->toThrow(InvalidArgumentException::class, 'There is no subscription funds to extend.');
});

it('cancels subscription', function () {
    $project = Project::factory()->create();
    $subscription = Subscription::factory()
        ->for($project)
        ->create([
            'expired_at' => now()->addDays(30),
            'canceled_at' => null,
        ]);

    $project->cancelSubscription();

    $subscription->refresh();
    expect($subscription->canceled_at)->not->toBeNull();
});

it('checks if can consume feature', function () {
    $feature = Feature::where('name', '=', 'SMS')
        ->where('consumable', '=', true)->firstOrFail();
    $plan = Plan::factory()->create();
    $project = Project::factory()->create();

    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
        'charges' => 100,
    ]);

    $subscription = Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    FeatureConsumption::factory()->create([
        'feature_id' => $feature->id,
        'subscription_id' => $subscription->id,
        'consumption' => 60,
    ]);

    expect($project->canConsume('SMS', 30))->toBeTrue()
        ->and($project->canConsume('SMS', 50))->toBeFalse();
});

it('throws exception when consume feature without enough balance', function () {
    $feature = Feature::where('name', '=', 'SMS')
        ->where('consumable', '=', true)->firstOrFail();
    $plan = Plan::factory()->create();
    $project = Project::factory()->create();

    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
        'charges' => 100,
    ]);

    $subscription = Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    FeatureConsumption::factory()->create([
        'feature_id' => $feature->id,
        'subscription_id' => $subscription->id,
        'consumption' => 60,
    ]);

    expect(fn () => $project->consume('SMS', 50, 'SMS'))
        ->toThrow(InvalidArgumentException::class, 'Insufficient consumption.');
});

it('consumes feature', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $plan = Plan::where('name', 'like', '%SMS%')->firstOrFail();
    $startDate = CarbonImmutable::now();

    $project->subscribeTo($plan, $startDate, 1);

    $consumption = $project->consume('SMS', 1, 'SMS');

    expect($consumption)
        ->toBeInstanceOf(FeatureConsumption::class)
        ->consumption->toBe(1)
        ->type->toBe('SMS');
});

it('feature is not consumable', function () {
    $project = Project::factory(['status' => 'active'])->create();
    $plan = Plan::where('name', 'like', '%SMS%')->firstOrFail();
    $startDate = CarbonImmutable::now();
    $feature = Feature::where('name', 'like', '%SMS%')->firstOrFail();
    $feature->update(['consumable' => false]);

    $project->subscribeTo($plan, $startDate, 1);

    expect($project->canConsume('SMS', 1))->toBeTrue();
});

it('gets feature balance', function () {
    $feature = Feature::where('name', '=', 'SMS')
        ->where('consumable', '=', true)->firstOrFail();
    $plan = Plan::factory()->create();
    $project = Project::factory()->create();

    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
        'charges' => 100,
    ]);

    $subscription = Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    FeatureConsumption::factory()->create([
        'feature_id' => $feature->id,
        'subscription_id' => $subscription->id,
        'consumption' => 60,
    ]);

    expect($project->balance('SMS'))->toBe(40);
});

it('gets feature limit', function () {
    $feature = Feature::where('name', '=', 'SMS')
        ->where('consumable', '=', true)->firstOrFail();
    $plan = Plan::factory()->create();
    $project = Project::factory()->create();

    FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
        'plan_id' => $plan->id,
        'charges' => 100,
    ]);

    Subscription::factory()
        ->for($project)
        ->for($plan)
        ->create([
            'expired_at' => now()->addDays(30),
        ]);

    expect($project->featureLimit('SMS'))->toBe(100);
});
