<?php

declare(strict_types=1);

use App\Jobs\MessageJob;
use App\Jobs\MessageJobCreator;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Services\Jasmin\JasminClient;
use Illuminate\Support\Facades\Bus;

it('dispatches message jobs for each message receipt', function () {
    Bus::fake();

    $message = Message::factory()->create();
    $receipts = MessageReceipt::factory()->count(3)->create(['message_id' => $message->id]);
    $message->setRelation('messages', $receipts);

    $jasmin = Mockery::mock(JasminClient::class);

    $job = new MessageJobCreator($message, $jasmin);
    $job->handle();

    foreach ($receipts as $receipt) {
        Bus::assertDispatched(MessageJob::class, function ($dispatchedJob) use ($receipt, $jasmin) {
            return $dispatchedJob->getMessageReceipt()->id === $receipt->id && $dispatchedJob->getJasmin() === $jasmin;
        });
    }
});
