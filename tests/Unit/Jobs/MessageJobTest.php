<?php

declare(strict_types=1);

use App\Jobs\MessageJob;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Provider;
use App\Models\Sender;
use App\Services\Jasmin\JasminClient;
use App\Services\Jasmin\Models\SentMessage;
use Illuminate\Support\Facades\Cache;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
    // Ensure required models exist
    $this->sender = Sender::factory()->create([
        'sender' => 'TestSender',
        'status' => 'active',
    ]);
    // Remove all providers and create the required one for the test
    Provider::query()->delete();
    Provider::factory()->create(['name' => 'Libyana']);
    $this->message = Message::factory()->create(['sender_id' => $this->sender->id, 'message_type' => 'sms']);
    $this->receipt = MessageReceipt::factory()->create([
        'message_id' => $this->message->id,
        'number' => '00218920000000',
        'status' => 'pending',
    ]);
    $this->message->setRelation('sender', $this->sender);
    $this->receipt->setRelation('message', $this->message);
});

it('marks message as sent on success', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-123'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-123');
});

it('marks message as failed if not sent', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Failed', null));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('failed');
});

it('fails if provider is unknown', function () {
    // Do not create provider for this test
    $receipt = MessageReceipt::factory()->create(['number' => '**********', 'message_id' => $this->message->id]);
    $receipt->setRelation('message', $this->message);
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($receipt, $jasmin);
    expect(fn () => $job->handle())->toThrow(Illuminate\Database\Eloquent\ModelNotFoundException::class);
});

it('verifies backoff method returns correct array', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);

    expect($job->backoff())->toBe([1, 5, 10]);
});

it('verifies tries property is set correctly', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);

    expect($job->tries)->toBe(30);
});

it('encodes UCS2 for non-GSM7 message', function () {
    $this->message->update(['short_message' => 'مرحبا']); // Arabic, not GSM7
    $this->receipt->refresh();
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-456'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-456');
    expect($this->receipt->sent_at)->not->toBeNull();
});

it('handles flash message type', function () {
    $this->message->update(['message_type' => 'flash']);
    $this->receipt->refresh();
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-789'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-789');
    expect($this->receipt->sent_at)->not->toBeNull();
});

it('getMessageReceipt and getJasmin return correct objects', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);
    expect($job->getMessageReceipt())->toBeInstanceOf(MessageReceipt::class);
    expect($job->getJasmin())->toBe($jasmin);
});

it('failed method updates message receipt status', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);
    $exception = new Exception('Test exception');

    $job->failed($exception);
    $this->receipt->refresh();

    expect($this->receipt->status)->toBe('failed');
});

it('detects ESME_RSUBMITFAIL in exception message', function () {
    $exceptionMessage = 'Failed with ESME_RSUBMITFAIL error';
    expect(str_contains($exceptionMessage, 'ESME_RSUBMITFAIL'))->toBeTrue();

    $normalException = 'Connection timeout';
    expect(str_contains($normalException, 'ESME_RSUBMITFAIL'))->toBeFalse();
});

it('tests basic cache functionality', function () {
    // Test cache storage and retrieval
    Cache::put('test_key', 'test_value', now()->addMinutes(30));
    expect(Cache::get('test_key'))->toBe('test_value');

    // Test cache deletion
    Cache::forget('test_key');
    expect(Cache::get('test_key'))->toBeNull();
});

it('tests numeric validation', function () {
    expect(is_numeric('123'))->toBeTrue();
    expect(is_numeric('invalid'))->toBeFalse();
    expect(is_numeric(null))->toBeFalse();
});

it('handles JasminClientException and updates status to pending', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andThrow(new App\Services\Jasmin\Exceptions\JasminClientException('Connection timeout'));

    $job = new MessageJob($this->receipt, $jasmin);

    // Expect the exception to be rethrown
    expect(fn () => $job->handle())->toThrow(App\Services\Jasmin\Exceptions\JasminClientException::class);

    // Verify the status was updated to pending
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('pending');
});

it('tests instanceof JasminClientException check', function () {
    $jasminException = new App\Services\Jasmin\Exceptions\JasminClientException('timeout');
    $regularException = new Exception('regular error');

    expect($jasminException instanceof App\Services\Jasmin\Exceptions\JasminClientException)->toBeTrue();
    expect($regularException instanceof App\Services\Jasmin\Exceptions\JasminClientException)->toBeFalse();
});

it('handles non-JasminClientException and marks as failed', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andThrow(new RuntimeException('Database connection failed'));

    // Create a job that will capture the fail() call
    $job = new class($this->receipt, $jasmin) extends MessageJob
    {
        public bool $failCalled = false;

        public ?string $failMessage = null;

        public function fail($exception = null): void
        {
            $this->failCalled = true;
            $this->failMessage = $exception;
            // Don't call parent::fail() to avoid actual job failure in test
        }
    };

    $job->handle();

    // Verify the status was updated to failed (lines 111-113)
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('failed');

    // Verify fail() was called with correct message (line 114)
    expect($job->failCalled)->toBeTrue();
    expect($job->failMessage)->toBe('Failed to send message');
});
