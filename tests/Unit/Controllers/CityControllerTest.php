<?php

declare(strict_types=1);

use App\Http\Controllers\CityController;
use App\Http\Resources\CityResource;
use App\Models\City;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('index method returns AnonymousResourceCollection', function () {
    $controller = new CityController();

    $result = $controller->index();

    expect($result)->toBeInstanceOf(AnonymousResourceCollection::class);
});

it('index method returns all cities', function () {
    $cities = City::factory()->count(3)->create();
    $controller = new CityController();

    $result = $controller->index();

    expect($result->collection)->toHaveCount(3);
});

it('index method returns empty collection when no cities exist', function () {
    $controller = new CityController();

    $result = $controller->index();

    expect($result->collection)->toHaveCount(0);
});

it('index method uses CityResource for transformation', function () {
    $city = City::factory()->create();
    $controller = new CityController();

    $result = $controller->index();

    expect($result->collects)->toBe(CityResource::class);
});

it('index method retrieves cities from database', function () {
    $cities = City::factory()->count(2)->create();
    $controller = new CityController();

    $result = $controller->index();

    $cityIds = $result->collection->pluck('id')->toArray();
    $expectedIds = $cities->pluck('id')->toArray();

    expect($cityIds)->toEqual($expectedIds);
});
