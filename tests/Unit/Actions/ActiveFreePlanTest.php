<?php

declare(strict_types=1);

use App\Actions\ActiveFreePlan;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('returns null if free_plan setting is not found', function () {
    $action = new ActiveFreePlan();
    $plans = $action->getFreePlans();
    expect($plans)->toBeNull();
});

it('returns null if free_plan setting is empty json array', function () {
    Setting::create(['key' => 'free_plan', 'value' => '']);
    $action = new ActiveFreePlan();
    $plans = $action->getFreePlans();
    expect($plans)->toBe(null);
});

it('returns free plans based on settings', function () {
    $plan1 = Plan::factory()->create();
    $plan2 = Plan::factory()->create();

    Setting::where('key', 'free_plan')->update(['value' => [$plan1->id, $plan2->id]]);

    $action = new ActiveFreePlan();
    $plans = $action->getFreePlans();
    expect($plans->count())->toBe(2);
    expect($plans->pluck('id')->all())->toEqualCanonicalizing([$plan1->id, $plan2->id]);
});

it('does not subscribe if no free plans are found', function () {
    $project = Project::factory()->create();
    $action = new ActiveFreePlan();

    // Mock getFreePlans to return null
    $action->activateFreePlans($project);

    // Assert that no subscriptions were created for the project
    expect($project->subscription()->count())->toBe(0);
});

it('activates free plans for a project', function () {
    $project = Project::factory()->create();
    $plan1 = Plan::factory()->create();
    $plan2 = Plan::factory()->create();

    Setting::where('key', 'free_plan')->update(['value' => [$plan1->id, $plan2->id]]);

    $action = new ActiveFreePlan();
    $action->activateFreePlans($project);

    // Assert that subscriptions were created for the project
    expect($project->subscription()->count())->toBe(2);
    $subscribedPlanIds = $project->subscription->pluck('plan_id')->all();
    expect($subscribedPlanIds)->toEqualCanonicalizing([$plan1->id, $plan2->id]);
});

it('does not activate plans if getFreePlans returns an empty collection', function () {
    $project = Project::factory()->create();
    $plan1 = Plan::factory()->create();
    $plan2 = Plan::factory()->create();

    Setting::where('key', 'free_plan')->update(['value' => []]);

    $action = new ActiveFreePlan();
    $action->activateFreePlans($project);

    // Assert that no subscriptions were created for the project
    expect($project->subscription()->count())->toBe(0);
});
