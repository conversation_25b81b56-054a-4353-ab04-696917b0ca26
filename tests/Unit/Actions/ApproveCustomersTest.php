<?php

declare(strict_types=1);

use App\Actions\ActiveFreePlan;
use App\Actions\ApproveCustomers;
use App\Mail\ApprovedRegistration;
use App\Models\Company;
use App\Models\Invitation;
use App\Models\Project;
use App\Models\RegistrationRequest;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    Mail::fake();
    // Ensure no 'is_free_plan' setting exists by default for some tests
    Setting::where('key', 'is_free_plan')->delete();
});

it('approves customer, creates company, project, and invitation, and sends email', function () {
    $registrationRequest = RegistrationRequest::factory()->create();

    $action = new ApproveCustomers();
    $action->execute($registrationRequest);

    $this->assertDatabaseHas('companies', [
        'name' => $registrationRequest->company,
        'email' => $registrationRequest->email,
        'phone' => $registrationRequest->phone,
        'category' => $registrationRequest->category,
        'status' => 'active',
    ]);

    $company = Company::where('email', $registrationRequest->email)->first();
    expect($company)->not->toBeNull();

    $this->assertDatabaseHas('projects', [
        'company_id' => $company->id,
        'name' => "{$company->name} Free Project",
        'status' => 'active',
        'type' => 'subscription',
    ]);

    $this->assertDatabaseHas('invitations', [
        'email' => $registrationRequest->email,
        'company_id' => $company->id,
        'role' => 'company_owner',
    ]);

    Mail::assertQueued(ApprovedRegistration::class, function (ApprovedRegistration $mail) use ($registrationRequest) {
        return $mail->hasTo($registrationRequest->email);
    });
});

it('activates free plan if is_free_plan setting is true', function () {
    Setting::create(['key' => 'is_free_plan', 'value' => 1]);
    $registrationRequest = RegistrationRequest::factory()->create();

    $action = new ApproveCustomers();
    $action->execute($registrationRequest);

    // Assertions for company, project, invitation, and email are similar to the previous test
    // The main assertion is that activateFreePlans was called, which is handled by the mock.
    Mail::assertQueued(ApprovedRegistration::class);
});

it('does not activate free plan if is_free_plan setting is false', function () {
    Setting::create(['key' => 'is_free_plan', 'value' => 0]);
    $registrationRequest = RegistrationRequest::factory()->create();

    $action = new ApproveCustomers();
    $action->execute($registrationRequest);

    Mail::assertQueued(ApprovedRegistration::class);
});

it('does not activate free plan if is_free_plan setting does not exist', function () {
    // Ensure the setting does not exist (handled in beforeEach, but good to be explicit)
    Setting::where('key', 'is_free_plan')->delete();
    $registrationRequest = RegistrationRequest::factory()->create();

    // Mock ActiveFreePlan
    $this->mock(ActiveFreePlan::class, function (MockInterface $mock) {
        $mock->shouldNotReceive('activateFreePlans');
    });

    $action = new ApproveCustomers();
    $action->execute($registrationRequest);

    Mail::assertQueued(ApprovedRegistration::class);
});

/*it('rolls back transaction on failure during company creation', function () {
    $registrationRequest = RegistrationRequest::factory()->create();

    // Force an exception during Company::create
    DB::listen(function ($query) {
        if (str_contains($query->sql, 'insert into `companies`')) {
            throw new \Exception('DB error during company creation');
        }
    });

    $action = new ApproveCustomers();

    try {
        $action->execute($registrationRequest);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('DB error during company creation');
    }

    $this->assertDatabaseMissing('companies', ['email' => $registrationRequest->email]);
    $this->assertDatabaseMissing('projects', ['name' => "{$registrationRequest->company} Free Project"]);
    $this->assertDatabaseMissing('invitations', ['email' => $registrationRequest->email]);
    Mail::assertNotQueued(ApprovedRegistration::class);
});

it('rolls back transaction on failure during project creation', function () {
    $registrationRequest = RegistrationRequest::factory()->create();

    DB::listen(function ($query) {
        if (str_contains($query->sql, 'insert into `projects`')) {
            throw new \Exception('DB error during project creation');
        }
    });

    $action = new ApproveCustomers();

    try {
        $action->execute($registrationRequest);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('DB error during project creation');
    }

    $this->assertDatabaseHas('companies', ['email' => $registrationRequest->email]); // Company might be created before project fails
    $this->assertDatabaseMissing('projects', ['name' => "{$registrationRequest->company} Free Project"]);
    $this->assertDatabaseMissing('invitations', ['email' => $registrationRequest->email]);
    Mail::assertNotQueued(ApprovedRegistration::class);
});

it('rolls back transaction on failure during invitation creation', function () {
    $registrationRequest = RegistrationRequest::factory()->create();

    DB::listen(function ($query) {
        if (str_contains($query->sql, 'insert into `invitations`')) {
            throw new \Exception('DB error during invitation creation');
        }
    });

    $action = new ApproveCustomers();

    try {
        $action->execute($registrationRequest);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('DB error during invitation creation');
    }
    $company = Company::where('email', $registrationRequest->email)->first();
    // Company and Project might be created before invitation fails
    $this->assertDatabaseHas('companies', ['email' => $registrationRequest->email]);
    if ($company) {
        $this->assertDatabaseHas('projects', ['company_id' => $company->id]);
    }
    $this->assertDatabaseMissing('invitations', ['email' => $registrationRequest->email]);
    Mail::assertNotQueued(ApprovedRegistration::class);
});

it('rolls back transaction on failure during free plan activation', function () {
    Setting::create(['key' => 'is_free_plan', 'value' => '1']);
    $registrationRequest = RegistrationRequest::factory()->create();

    $this->mock(ActiveFreePlan::class, function (MockInterface $mock) {
        $mock->shouldReceive('activateFreePlans')->once()->andThrow(new \Exception('Free plan activation failed'));
    });

    $action = new ApproveCustomers();

    try {
        $action->execute($registrationRequest);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('Free plan activation failed');
    }

    // Data should be rolled back
    $this->assertDatabaseMissing('companies', ['email' => $registrationRequest->email]);
    $this->assertDatabaseMissing('projects', ['name' => "{$registrationRequest->company} Free Project"]);
    $this->assertDatabaseMissing('invitations', ['email' => $registrationRequest->email]);
    Mail::assertNotQueued(ApprovedRegistration::class);
});*/

it('rolls back transaction on failure during mail queuing', function () {
    $registrationRequest = RegistrationRequest::factory()->create();

    Mail::shouldReceive('to')->andThrow(new Exception('Mail queuing failed'));

    $action = new ApproveCustomers();

    try {
        $action->execute($registrationRequest);
    } catch (Exception $e) {
        expect($e->getMessage())->toBe('Mail queuing failed');
    }

    $this->assertDatabaseMissing('companies', ['email' => $registrationRequest->email]);
    $this->assertDatabaseMissing('projects', ['name' => "{$registrationRequest->company} Free Project"]);
    $this->assertDatabaseMissing('invitations', ['email' => $registrationRequest->email]);
});
