<?php

declare(strict_types=1);

use App\Models\Provider;
use App\Rules\PhoneNumberRule;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('validates phone number against active provider patterns', function () {
    $rule = new PhoneNumberRule();
    $fail = fn (string $message) => throw new Exception($message);

    // Valid phone numbers
    // almadar
    expect(fn () => $rule->validate('phone', '+218912345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '00218912345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '+218932345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '00218932345678', $fail))->not->toThrow(Exception::class);

    // libyana
    expect(fn () => $rule->validate('phone', '+218922345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '00218922345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '+218924345678', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '00218942345678', $fail))->not->toThrow(Exception::class);

    // Invalid phone numbers
    expect(fn () => $rule->validate('phone', '+33**********', $fail))->toThrow(Exception::class); // Inactive provider
    expect(fn () => $rule->validate('phone', '**********', $fail))->toThrow(Exception::class); // No country code
    expect(fn () => $rule->validate('phone', '+11234', $fail))->toThrow(Exception::class); // Too short
    expect(fn () => $rule->validate('phone', 'invalid', $fail))->toThrow(Exception::class); // Invalid format
    expect(fn () => $rule->validate('phone', '**********', $fail))->toThrow(Exception::class); // Invalid format
    expect(fn () => $rule->validate('phone', '**********', $fail))->toThrow(Exception::class); // Invalid format
});

it('validates that value is a string', function () {
    $rule = new PhoneNumberRule();
    $messages = [];
    $fail = function (string $message) use (&$messages) {
        $messages[] = $message;
    };

    // Test with non-string values
    $rule->validate('phone', 123, $fail);
    $rule->validate('phone', ['phone' => '+1**********'], $fail);
    $rule->validate('phone', null, $fail);

    expect($messages)->toHaveCount(3)
        ->each(fn ($message) => $message->toBe('The :attribute must be a string.'));
});

it('returns correct validation message', function () {
    $rule = new PhoneNumberRule();
    $messages = [];
    $fail = function (string $message) use (&$messages) {
        $messages[] = $message;
    };

    $rule->validate('phone', 'invalid-phone', $fail);

    expect($messages)->toHaveCount(1)
        ->and($messages[0])->toBe(__('The :attribute must be valid in system providers.'));
});

it('handles empty provider list gracefully', function () {
    // Delete all providers
    Provider::query()->delete();

    $rule = new PhoneNumberRule();
    $fail = fn (string $message) => throw new Exception($message);

    // Should fail because no providers exist
    expect(fn () => $rule->validate('phone', '+1**********', $fail))->toThrow(Exception::class);
});

it('validates multiple phone numbers in sequence', function () {
    Provider::factory()->create([
        'status' => 'active',
        'pattern' => '^\+1\d{10}$',
    ]);

    $rule = new PhoneNumberRule();
    $fail = fn (string $message) => throw new Exception($message);

    // Test multiple validations with the same rule instance
    expect(fn () => $rule->validate('phone', '+1**********', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '+11234567891', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', 'invalid', $fail))->toThrow(Exception::class);
});

it('handles special characters in provider patterns', function () {
    Provider::factory()->create([
        'status' => 'active',
        'pattern' => '^(?:\+1\s|\b1[-\s]?|\()?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}$', // Allows +, -, (, ), and spaces
    ]);

    $rule = new PhoneNumberRule();
    $fail = fn (string $message) => throw new Exception($message);

    // Test various formats
    expect(fn () => $rule->validate('phone', '+****************', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '**************', $fail))->not->toThrow(Exception::class);
    expect(fn () => $rule->validate('phone', '(*************', $fail))->not->toThrow(Exception::class);
});
