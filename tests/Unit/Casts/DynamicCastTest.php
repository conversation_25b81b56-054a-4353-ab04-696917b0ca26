<?php

declare(strict_types=1);

use App\Casts\DynamicCast;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Test cases for the 'get' method
it('get returns decoded json for free_plan key', function () {
    $model = new Setting(['key' => 'free_plan']);
    $cast = new DynamicCast();
    $value = json_encode(['plan1', 'plan2']);
    $result = $cast->get($model, 'value', $value, []);
    expect($result)->toBe(['plan1', 'plan2']);
});

it('get returns boolean for is_free_plan key', function () {
    $model = new Setting(['key' => 'is_free_plan']);
    $cast = new DynamicCast();

    expect($cast->get($model, 'value', '1', []))->toBeTrue();
    expect($cast->get($model, 'value', 1, []))->toBeTrue();
    expect($cast->get($model, 'value', true, []))->toBeTrue();

    expect($cast->get($model, 'value', '0', []))->toBeFalse();
    expect($cast->get($model, 'value', 0, []))->toBeFalse();
    expect($cast->get($model, 'value', false, []))->toBeFalse();
    expect($cast->get($model, 'value', '', []))->toBeFalse(); // Empty string cast to false
});

it('get returns original value for other keys', function () {
    $model = new Setting(['key' => 'other_key']);
    $cast = new DynamicCast();
    $value = 'some_value';
    $result = $cast->get($model, 'value', $value, []);
    expect($result)->toBe('some_value');
});

it('get returns null if value is null for other keys', function () {
    $model = new Setting(['key' => 'other_key']);
    $cast = new DynamicCast();
    $result = $cast->get($model, 'value', null, []);
    expect($result)->toBeNull();
});

// Test cases for the 'set' method
it('set returns encoded json for free_plan key', function () {
    $model = new Setting(['key' => 'free_plan']);
    $cast = new DynamicCast();
    $value = ['plan1', 'plan2'];
    $result = $cast->set($model, 'value', $value, []);
    expect($result)->toBe(json_encode(['plan1', 'plan2']));
});

it('set returns integer for is_free_plan key', function () {
    $model = new Setting(['key' => 'is_free_plan']);
    $cast = new DynamicCast();

    expect($cast->set($model, 'value', true, []))->toBe(1);
    expect($cast->set($model, 'value', false, []))->toBe(0);
});

it('set returns original value for other keys', function () {
    $model = new Setting(['key' => 'other_key']);
    $cast = new DynamicCast();
    $value = 'some_value';
    $result = $cast->set($model, 'value', $value, []);
    expect($result)->toBe('some_value');
});

it('set returns null if value is null for other keys', function () {
    $model = new Setting(['key' => 'other_key']);
    $cast = new DynamicCast();
    $result = $cast->set($model, 'value', null, []);
    expect($result)->toBeNull();
});
