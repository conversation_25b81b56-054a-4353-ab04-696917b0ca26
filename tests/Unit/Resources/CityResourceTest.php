<?php

declare(strict_types=1);

use App\Http\Resources\CityResource;
use App\Models\City;
use Illuminate\Http\Request;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('transforms city model to array with correct structure', function () {
    $city = City::factory()->create([
        'name' => 'Test City',
        'description' => 'Test Description',
    ]);

    $resource = new CityResource($city);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toBeArray()
        ->toHaveKeys(['name', 'description'])
        ->name->toBe('Test City')
        ->description->toBe('Test Description');
});

it('handles null resource gracefully', function () {
    $resource = new CityResource(null);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toBeArray()
        ->toBeEmpty();
});

it('includes only name and description fields', function () {
    $city = City::factory()->create();

    $resource = new CityResource($city);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toHaveKeys(['name', 'description'])
        ->not->toHaveKey('id')
        ->not->toHaveKey('created_at')
        ->not->toHaveKey('updated_at');
});

it('handles city with null description', function () {
    $city = City::factory()->create([
        'name' => 'Test City',
        'description' => null,
    ]);

    $resource = new CityResource($city);
    $request = Request::create('/');

    $result = $resource->toArray($request);

    expect($result)->toBeArray()
        ->name->toBe('Test City')
        ->description->toBeNull();
});

it('transforms collection of cities correctly', function () {
    $cities = City::factory()->count(3)->create();

    $collection = CityResource::collection($cities);
    $request = Request::create('/');

    $result = $collection->toArray($request);

    expect($result)->toHaveCount(3);

    foreach ($result as $index => $cityData) {
        expect($cityData)->toHaveKeys(['name', 'description'])
            ->name->toBe($cities[$index]->name)
            ->description->toBe($cities[$index]->description);
    }
});
