<?php

declare(strict_types=1);

beforeEach(function (): void {
    $this->seed();
});

it('User Model canAccessTenant function', function () {

    $user = App\Models\User::factory()->create();

    $company = App\Models\User::factory()->create();

    expect($user->canAccessTenant($company))->toBeFalse();
});

it('Transaction Model project function', function () {

    $company = App\Models\Company::factory()->create();

    $project = App\Models\Project::factory()
        ->withSubscription('SMS Starter 1')
        ->create();

    $project->company()->associate($company);

    $transaction = App\Models\Transaction::factory()->create([
        'company_id' => $company->id,
        'project_id' => $project->id,
    ]);

    expect($transaction->project)->toBeInstanceOf(App\Models\Project::class);
});

it('Subscription Model isActive function', function () {
    $subscription = App\Models\Subscription::factory()->create([
        'expired_at' => now()->addMonth(),
    ]);

    expect($subscription->isActive())->toBeTrue();
});

it('SenderProvider Model sender function', function () {
    $sender = App\Models\Sender::factory()->create();

    $senderProvider = App\Models\SenderProvider::create([
        'sender_id' => $sender->id,
        'provider_id' => App\Models\Provider::factory()->create()->id,
        'expired_at' => now()->addMonth(),
    ]);

    expect($senderProvider->sender)->toBeInstanceOf(App\Models\Sender::class);
});

it('Representative Model company function', function () {
    $company = App\Models\Company::factory()->create();

    $representative = App\Models\Representative::create([
        'name' => fake()->name(),
        'phone' => fake()->phoneNumber(),
        'email' => fake()->email(),
        'position' => fake()->jobTitle(),
        'company_id' => $company->id,
    ]);

    expect($representative->company)->toBeInstanceOf(App\Models\Company::class);
});

it('Provider Model sender function', function () {
    $provider = App\Models\Provider::factory()->create();

    App\Models\SenderProvider::create([
        'sender_id' => App\Models\Sender::factory()->create()->id,
        'provider_id' => $provider->id,
        'expired_at' => now()->addMonth(),
    ]);

    expect($provider->sender()->first())->toBeInstanceOf(App\Models\SenderProvider::class);
});

it('Provider Model campaign_messages function', function () {
    $provider = App\Models\Provider::factory()->create();
    $campaignMessage = App\Models\CampaignMessage::factory()->create();

    // Attach the relationship so the query doesn't return null
    $provider->campaign_messages()->attach($campaignMessage->id);

    expect($provider->campaign_messages()->first())->toBeInstanceOf(App\Models\CampaignMessage::class);
});

it('Plan Model feature function', function () {
    $plan = App\Models\Plan::factory()->create();

    App\Models\FeaturePlan::factory()->create([
        'plan_id' => $plan->id,
    ]);

    expect($plan->featurePlans()->first())->toBeInstanceOf(App\Models\FeaturePlan::class);
});

it('Plan Model subscriptions function', function () {
    $plan = App\Models\Plan::factory()->create();

    App\Models\Subscription::factory()->create([
        'plan_id' => $plan->id,
    ]);

    expect($plan->subscriptions()->first())->toBeInstanceOf(App\Models\Subscription::class);
});

it('OneTimePassword Model message function', function () {
    $message = App\Models\Message::factory()->create([
        'short_message' => '123456',
        'message_type' => 'otp',
    ]);

    $otp = App\Models\OneTimePassword::create([
        'message_id' => $message->id,
        'expiration_period' => 10,
        'length' => 6,
        'code' => '123456',
    ]);

    expect($otp->message)->toBeInstanceOf(App\Models\Message::class);
});

it('Massage Receipt Model message function', function () {
    $message = App\Models\Message::factory()->create([
        'short_message' => 'test message',
        'message_type' => 'sms',
    ]);

    $messageReceipt = App\Models\MessageReceipt::create([
        'message_id' => $message->id,
        'number' => '00218912345678',
        'status' => 'pending',
    ]);

    expect($messageReceipt->message)->toBeInstanceOf(App\Models\Message::class);
});

it('Message Model contactGroup function', function () {
    $contactGroup = App\Models\ContactGroup::factory()->create();

    $message = App\Models\Message::factory()->create([
        'contact_group_id' => $contactGroup->id,
    ]);

    expect($message->contactGroup)->toBeInstanceOf(App\Models\ContactGroup::class);
});

it('Message Model contacts function', function () {
    $contact = App\Models\Contact::factory()->create();

    $message = App\Models\Message::factory()->create();

    App\Models\MessageReceipt::create([
        'message_id' => $message->id,
        'number' => '00218912345678',
        'status' => 'pending',
        'contact_id' => $contact->id,
    ]);

    expect($message->contacts()->first())->toBeInstanceOf(App\Models\Contact::class);
});

it('Invitation Model company function', function () {
    $invitation = App\Models\Invitation::create([
        'email' => fake()->email(),
        'company_id' => App\Models\Company::factory()->create()->id,
        'expires_at' => now()->addDays(7),
    ]);

    expect($invitation->company)->toBeInstanceOf(App\Models\Company::class);
});

it('Feature Plan Model plan function', function () {
    $featurePlan = App\Models\FeaturePlan::factory()->create();

    expect($featurePlan->plan)->toBeInstanceOf(App\Models\Plan::class);
});

it('Feature Plan Model feature function', function () {
    $featurePlan = App\Models\FeaturePlan::factory()->create();

    expect($featurePlan->feature)->toBeInstanceOf(App\Models\Feature::class);
});

it('Feature Consumption Model feature function', function () {
    $featureConsumption = App\Models\FeatureConsumption::factory()->create();

    expect($featureConsumption->feature)->toBeInstanceOf(App\Models\Feature::class);
});

it('Feature Consumption Model subscription function', function () {
    $featureConsumption = App\Models\FeatureConsumption::factory()->create();

    expect($featureConsumption->subscription)->toBeInstanceOf(App\Models\Subscription::class);
});

it('Feature Model featurePlans function', function () {
    $feature = App\Models\Feature::factory()->create();

    App\Models\FeaturePlan::factory()->create([
        'feature_id' => $feature->id,
    ]);

    expect($feature->featurePlans()->first())->toBeInstanceOf(App\Models\FeaturePlan::class);
});

it('Feature Model consumption function', function () {
    $feature = App\Models\Feature::factory()->create();

    App\Models\FeatureConsumption::factory()->create([
        'feature_id' => $feature->id,
    ]);

    expect($feature->consumption()->first())->toBeInstanceOf(App\Models\FeatureConsumption::class);
});

it('Contact Group Model projects function', function () {
    $contactGroup = App\Models\ContactGroup::factory()->create();

    $project = App\Models\Project::factory()->create();

    $project->contact_groups()->attach($contactGroup);

    expect($contactGroup->projects()->first())->toBeInstanceOf(App\Models\Project::class);
});

it('Contact Model contactGroup function', function () {
    $contact = App\Models\Contact::factory()->create();

    expect($contact->contactGroup)->toBeInstanceOf(App\Models\ContactGroup::class);
});

it('Company Model roles function', function () {
    $company = App\Models\Company::factory()->create();

    $company->roles()->attach(App\Models\Role::create(['name' => 'test']));

    expect($company->roles()->first())->toBeInstanceOf(App\Models\Role::class);
});

it('Company Model messages function', function () {
    $company = App\Models\Company::factory()->create();
    $message = App\Models\Message::factory()->create([
        'company_id' => $company->id,
    ]);

    expect($company->messages()->first())->toBeInstanceOf(App\Models\Message::class);
});

it('Can Create Message Template', function () {
    $messageTemplate = App\Models\MessageTemplate::factory()->create();

    expect($messageTemplate)->toBeInstanceOf(App\Models\MessageTemplate::class);
});

it('Can Create Message Template Parameter', function () {
    $messageTemplateParameter = App\Models\MessageTemplateParameter::factory()->create();

    expect($messageTemplateParameter)->toBeInstanceOf(App\Models\MessageTemplateParameter::class);
});

it('Can get Message Template Company', function () {
    $messageTemplate = App\Models\MessageTemplate::factory()->create();

    expect($messageTemplate->company)->toBeInstanceOf(App\Models\Company::class);
});

it('Can get Message Template', function () {
    $messageParameter = App\Models\MessageTemplateParameter::factory()->create();

    expect($messageParameter->messageTemplate)->toBeInstanceOf(App\Models\MessageTemplate::class);
});

it('Message Template Model project function', function () {
    $messageTemplate = App\Models\MessageTemplate::factory()->create();

    expect($messageTemplate->project)->toBeInstanceOf(App\Models\Project::class);
});

it('Message Template Model parameters function', function () {
    $messageTemplate = App\Models\MessageTemplate::factory()->create();

    App\Models\MessageTemplateParameter::factory()->create([
        'message_template_id' => $messageTemplate->id,
    ]);

    expect($messageTemplate->parameters()->first())->toBeInstanceOf(App\Models\MessageTemplateParameter::class);
});
