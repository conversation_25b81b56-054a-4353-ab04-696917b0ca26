<?php

declare(strict_types=1);

use App\Models\IpWhitelist;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\seed;

uses(RefreshDatabase::class);

beforeEach(function () {
    seed();
});

it('can create an ip whitelist', function () {
    $ipWhitelist = IpWhitelist::factory()->create();

    expect($ipWhitelist)
        ->toBeInstanceOf(IpWhitelist::class)
        ->ip_address->not->toBeEmpty()
        ->description->not->toBeEmpty()
        ->project_id->not->toBeNull();
});

it('has fillable attributes', function () {
    $ipWhitelist = new IpWhitelist();

    expect($ipWhitelist->getFillable())->toBe([
        'ip_address',
        'description',
        'project_id',
    ]);
});

it('has correct date casting', function () {
    $ipWhitelist = new IpWhitelist();

    expect($ipWhitelist->getCasts())
        ->toHave<PERSON>ey('created_at', 'datetime')
        ->toHaveKey('updated_at', 'datetime');
});

it('belongs to a project', function () {
    $project = Project::factory()->create();

    $ipWhitelist = IpWhitelist::factory()
        ->create(['project_id' => $project->id]);

    expect($ipWhitelist->project)
        ->toBeInstanceOf(Project::class)
        ->id->toBe($project->id);
});

it('uses UUIDs as primary keys', function () {
    $ipWhitelist = IpWhitelist::factory()->create();

    expect($ipWhitelist->id)
        ->toBeString()
        ->toMatch('/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i');
});

it('can update ip whitelist', function () {
    $ipWhitelist = IpWhitelist::factory()->create();

    $newData = [
        'ip_address' => '***********00',
        'description' => 'Updated description',
    ];

    $ipWhitelist->update($newData);
    $ipWhitelist->refresh();

    expect($ipWhitelist)
        ->ip_address->toBe($newData['ip_address'])
        ->description->toBe($newData['description']);
});

it('can delete ip whitelist', function () {
    $ipWhitelist = IpWhitelist::factory()->create();

    $id = $ipWhitelist->id;
    $ipWhitelist->delete();

    expect(IpWhitelist::find($id))->toBeNull();
});

it('validates ip address format', function () {
    $validIps = [
        '***********',
        '10.0.0.0',
        '************',
        '256.1.2.3', // Invalid IP
        '*******.5', // Invalid IP
        'invalid-ip', // Invalid IP
    ];

    foreach ($validIps as $ip) {
        try {
            IpWhitelist::factory()->create(['ip_address' => $ip]);
            if (! filter_var($ip, FILTER_VALIDATE_IP)) {
                $this->fail("Invalid IP address $ip was accepted");
            }
        } catch (Exception $e) {
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                $this->fail("Valid IP address $ip was rejected");
            }
        }
    }
});

it('can retrieve multiple ip whitelists for a project', function () {
    $project = Project::factory()->create();

    $ipWhitelists = IpWhitelist::factory()
        ->count(3)
        ->create(['project_id' => $project->id]);

    expect($project->ipWhitelists)
        ->toHaveCount(3)
        ->each->toBeInstanceOf(IpWhitelist::class);
});

it('can be created with minimal data', function () {
    $project = Project::factory()->create();

    $ipWhitelist = IpWhitelist::create([
        'ip_address' => '***********',
        'project_id' => $project->id,
    ]);

    expect($ipWhitelist)
        ->toBeInstanceOf(IpWhitelist::class)
        ->ip_address->toBe('***********')
        ->project_id->toBe($project->id)
        ->description->toBeNull();
});
