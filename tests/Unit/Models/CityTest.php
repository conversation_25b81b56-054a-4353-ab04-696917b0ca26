<?php

declare(strict_types=1);

use App\Models\CampaignMessage;
use App\Models\City;
use App\Models\Company;

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('has fillable attributes', function () {
    $city = new City();

    expect($city->getFillable())->toBe([
        'name',
        'description',
    ]);
});

it('uses timestamps', function () {
    $city = City::factory()->create();

    expect($city->created_at)->toBeInstanceOf(Carbon\CarbonImmutable::class)
        ->and($city->updated_at)->toBeInstanceOf(Carbon\CarbonImmutable::class);
});

it('uses uuid as primary key', function () {
    $city = City::factory()->create();

    expect($city->id)->toBeString()
        ->and(strlen($city->id))->toBe(36)
        ->and(Str::isUuid($city->id))->toBeTrue();
});

it('can create a city', function () {
    $cityData = [
        'name' => 'Test City',
        'description' => 'Test Description',
    ];

    $city = City::create($cityData);

    assertDatabaseHas('cities', $cityData);
    expect($city)->toBeInstanceOf(City::class)
        ->and($city->name)->toBe('Test City')
        ->and($city->description)->toBe('Test Description');
});

it('can update a city', function () {
    $city = City::factory()->create();

    $city->update([
        'name' => 'Updated City',
        'description' => 'Updated Description',
    ]);

    $city->refresh();

    expect($city->name)->toBe('Updated City')
        ->and($city->description)->toBe('Updated Description');
});

it('can delete a city', function () {
    $city = City::factory()->create();

    $cityId = $city->id;
    $city->delete();

    expect(City::find($cityId))->toBeNull();
});

it('has many companies', function () {
    $city = City::factory()
        ->has(Company::factory()->count(3))
        ->create();

    expect($city->companies)
        ->toHaveCount(3)
        ->each->toBeInstanceOf(Company::class);
});

it('validates required attributes', function () {
    try {
        City::create([]);
    } catch (Exception $e) {
        expect($e)->toBeInstanceOf(Exception::class);
    }
});

it('belongs to many campaign messages', function () {
    $city = City::factory()
        ->has(CampaignMessage::factory()->count(3), 'campaign_messages')
        ->create();

    expect($city->campaign_messages)
        ->toHaveCount(3)
        ->each->toBeInstanceOf(CampaignMessage::class);
});

it('can attach and detach campaign messages', function () {
    $city = City::factory()->create();
    $campaignMessages = CampaignMessage::factory()->count(3)->create();

    // Attach
    $city->campaign_messages()->attach($campaignMessages->pluck('id'));
    expect($city->campaign_messages)->toHaveCount(3);

    // Detach
    $city->campaign_messages()->detach($campaignMessages->first()->id);
    $city->refresh();
    expect($city->campaign_messages)->toHaveCount(2);
});

it('can eager load relationships', function () {
    $city = City::factory()
        ->has(Company::factory()->count(3))
        ->has(CampaignMessage::factory()->count(3), 'campaign_messages')
        ->create();

    $loadedCity = City::with(['companies', 'campaign_messages'])->find($city->id);

    expect($loadedCity->relationLoaded('companies'))->toBeTrue()
        ->and($loadedCity->relationLoaded('campaign_messages'))->toBeTrue();
});

it('can retrieve companies with specific conditions', function () {
    $city = City::factory()
        ->has(Company::factory()->count(3)->state(['status' => 'active']))
        ->has(Company::factory()->count(2)->state(['status' => 'inactive']))
        ->create();

    expect($city->companies()->where('status', 'active')->count())->toBe(3)
        ->and($city->companies()->where('status', 'inactive')->count())->toBe(2);
});
