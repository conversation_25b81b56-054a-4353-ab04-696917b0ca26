<?php

declare(strict_types=1);

use Filament\Facades\Filament;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    $url = Filament::getPanel('company')->getUrl();

    return view('welcome', [
        'url' => $url,
    ]);
});

Route::middleware('signed')
    ->get(
        'invitation/{invitation}/accept',
        App\Livewire\CompanyUserInvitation::class,
    )
    ->name('invitation.accept');

/*function generateRandomNumber($length)
{
    $result = '';
    $characters = '0123456789';
    $charactersLength = strlen($characters);
    for ($i = 0; $i < $length; $i++) {
        $result .= $characters[rand(0, $charactersLength - 1)];
    }
    return $result;
}*/
/* Route::get('/test', function () {
    $receivers = [];
    $prefixes = ['091', '092', '093', '094'];

    for ($i = 0; $i < 30; $i++) {
        $receivers[] =
            $prefixes[array_rand($prefixes)] . generateRandomNumber(7);
    }

    return $receivers;
});*/
