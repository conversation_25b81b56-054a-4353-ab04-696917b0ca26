<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('senders', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('sender');
            $table->enum('category', ['SenderId', 'ShortNumber']);
            $table->enum('type', ['private', 'multi']);
            $table->enum('status', ['pending', 'active', 'rejected']);
            $table->text('notes')->nullable();
            $table
                ->foreignUuid('company_id')
                ->nullable()
                ->constrained('companies')
                ->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('senders');
    }
};
