<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_recipients', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table
                ->foreignUuid('message_id')
                ->constrained('messages')
                ->onDelete('cascade');
            $table->string('number');
            $table
                ->foreignUuid('contact_id')
                ->nullable()
                ->constrained('contacts')
                ->onDelete('cascade');
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('delivered_at')->nullable();
            $table
                ->enum('status', [
                    'pending',
                    'sent',
                    'delivered',
                    'failed',
                    'rejected',
                    'accepted',
                    'undeliverable',
                    'unknown',
                    'deleted',
                    'enroute',
                    'scheduled',
                    'expired',
                ])
                ->default('pending');
            $table->string('smpp_message_id')->nullable();
            $table->text('delivery_report')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_receipts');
    }
};
