<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->integer('amount');
            $table->string('description')->nullable();
            $table->enum('action_type', ['deposit', 'withdraw', 'charge', 'refund']);
            $table->enum('status', ['pending', 'completed', 'rejected'])->default('pending');
            $table->foreignUuid('company_id')->constrained('companies')->onDelete('cascade');
            $table
                ->foreignUuid('subscription_id')
                ->nullable()
                ->constrained('subscriptions')
                ->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
