<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_group_project', function (Blueprint $table): void {
            $table
                ->foreignUuid('contact_group_id')
                ->constrained('contact_groups')
                ->cascadeOnDelete();
            $table
                ->foreignUuid('project_id')
                ->constrained('projects')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_group_project');
    }
};
