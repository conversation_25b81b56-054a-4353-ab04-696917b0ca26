<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sender_provider', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table
                ->foreignUuid('sender_id')
                ->constrained('senders')
                ->onDelete('cascade');
            $table
                ->foreignUuid('provider_id')
                ->constrained('providers')
                ->onDelete('cascade');
            $table->timestamp('expired_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sender_provider');
    }
};
