<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_templates', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('display_name');
            $table->string('short_name');
            $table->text('content');
            $table->string('type');
            $table->string('status');
            $table->foreignUuid('company_id')->constrained('companies');
            $table->foreignUuid('project_id')->constrained('projects');
            $table->timestamps();
            $table->unique(['short_name', 'company_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_templates');
    }
};
