<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->text('short_message');
            $table
                ->foreignUuid('sender_id')
                ->constrained('senders')
                ->onDelete('set null');
            $table
                ->foreignUuid('company_id')
                ->nullable()
                ->constrained('companies')
                ->onDelete('set null');
            $table
                ->foreignUuid('project_id')
                ->nullable()
                ->constrained('projects')
                ->onDelete('set null');
            $table->enum('message_type', ['flash', 'sms', 'otp']);
            $table->enum('send_type', ['single', 'multiple']);
            $table
                ->foreignUuid('contact_group_id')
                ->nullable()
                ->constrained('contact_groups')
                ->onDelete('set null');
            $table->unsignedBigInteger('message_consumption');
            $table
                ->foreignUuid('transaction_id')
                ->nullable()
                ->constrained('transactions')
                ->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
