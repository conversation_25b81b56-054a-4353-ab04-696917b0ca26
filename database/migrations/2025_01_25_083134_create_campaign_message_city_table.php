<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_message_cities', function (Blueprint $table): void {
            $table
                ->foreignUuid('campaign_message_id')
                ->constrained('campaign_messages');
            $table
                ->foreignUuid('city_id')
                ->constrained('cities');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_message_cities');
    }
};
