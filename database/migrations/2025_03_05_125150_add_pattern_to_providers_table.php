<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table): void {
            $table->string('pattern')->nullable()->after('system_type');
            $table->string('smpp_pattern')->nullable()->after('pattern');
            $table->string('smpp_pattern_replace')->nullable()->after('smpp_pattern');
        });
    }

    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table): void {
            $table->dropColumn('pattern');
            $table->dropColumn('smpp_pattern');
            $table->dropColumn('smpp_pattern_replace');
        });
    }
};
