<?php

declare(strict_types=1);

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('registration_requests', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->string('city')->nullable();
            $table->string('company')->nullable();
            $table->string('category')->nullable();
            $table->enum('have_smsapi', ['yes', 'no', 'developing'])->nullable();
            $table->json('target')->nullable();
            $table->string('system_type')->nullable();
            $table->string('notes')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');

            $role = Role::firstOrCreate([
                'name' => 'super_admin',
            ]);

            $permissions = [
                'view_any_registration_request',
                'update_registration_request',
                'delete_any_registration_request',
            ];

            foreach ($permissions as $permission) {
                Permission::firstOrCreate([
                    'name' => $permission,
                ]);
            }

            $role->givePermissionTo($permissions);

            $role->save();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('registration_requests');
    }
};
