<?php

declare(strict_types=1);

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        App\Models\Setting::updateOrCreate([
            'key' => 'free_plan',
            'value' => '',
        ]);

        App\Models\Setting::updateOrCreate([
            'key' => 'is_free_plan',
            'value' => 0,
        ]);

        App\Models\Setting::updateOrCreate([
            'key' => 'default_sender',
            'value' => '',
        ]);

        $role = Role::firstOrCreate([
            'name' => 'super_admin',
        ]);

        $permissions = [
            'view_any_setting',
            'view_setting',
            'update_setting',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
            ]);
        }

        $role->givePermissionTo($permissions);

        $role->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        App\Models\Setting::where('key', 'free_plan')->delete();
        App\Models\Setting::where('key', 'is_free_plan')->delete();
        App\Models\Setting::where('key', 'default_sender')->delete();
    }
};
