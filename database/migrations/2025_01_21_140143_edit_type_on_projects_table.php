<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table): void {
            $table->dropColumn('type');
            $table
                ->enum('type', ['subscription', 'budget', 'mixed'])
                ->default('mixed');
        });
    }

    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table): void {
            $table->dropColumn('type');
            $table->enum('type', ['subscription', 'budget']);
        });
    }
};
