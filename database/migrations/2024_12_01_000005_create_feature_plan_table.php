<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feature_plan', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->decimal('charges')->nullable();
            $table
                ->foreignUuid('feature_id')
                ->constrained('features')
                ->nullOnDelete();
            $table
                ->foreignUuid('plan_id')
                ->constrained('plans')
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feature_plan');
    }
};
