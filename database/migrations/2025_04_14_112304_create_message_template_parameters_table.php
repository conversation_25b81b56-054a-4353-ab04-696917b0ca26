<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_template_parameters', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->foreignUuid('message_template_id')->constrained('message_templates')->onDelete('cascade');
            $table->string('name');
            $table->string('type');
            $table->integer('max_limit');
            $table->timestamps();
            $table->unique(['name', 'message_template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_template_parameters');
    }
};
