<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feature_consumptions', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('consumption');
            $table->enum('type', ['SMS', 'OTP']);
            $table
                ->foreignUuid('feature_id')
                ->constrained('features')
                ->nullOnDelete();
            $table
                ->foreignUuid('subscription_id')
                ->constrained()
                ->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feature_consumptions');
    }
};
