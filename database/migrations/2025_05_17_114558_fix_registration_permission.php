<?php

declare(strict_types=1);

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $role = Role::firstOrCreate([
            'name' => 'super_admin',
        ]);

        $permissions = [
            'view_any_registration_request',
            'update_registration_request',
            'delete_any_registration_request',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
            ]);
        }

        $role->givePermissionTo($permissions);

        $role->save();
    }

    public function down(): void
    {
        Schema::table('', function (Blueprint $table): void {
            //
        });
    }
};
