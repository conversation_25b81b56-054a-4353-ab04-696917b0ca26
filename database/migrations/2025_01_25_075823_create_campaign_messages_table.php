<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_messages', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('short_message');
            $table->integer('age_from');
            $table->integer('age_to');
            $table->enum('sex', ['male', 'female', 'both'])->default('both');
            $table->integer('quantity');
            $table
                ->enum('status', [
                    'active',
                    'inactive',
                    'pending',
                    'processing',
                    'rejected',
                ])
                ->default('pending');
            $table
                ->foreignUuid('company_id')
                ->nullable()
                ->constrained('companies');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_messages');
    }
};
