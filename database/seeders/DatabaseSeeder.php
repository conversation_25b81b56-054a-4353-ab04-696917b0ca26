<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Setting;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Setting::create([
            'key' => 'single_sms_cost',
            'value' => 235,
        ]);

        Setting::create([
            'key' => 'multiple_sms_cost',
            'value' => 200,
        ]);

        Setting::create([
            'key' => 'multiple_sms_threshold',
            'value' => 1000,
        ]);

        if (app()->environment('testing')) {
            $this->call(TestSeeder::class);

            return;
        }

        User::factory()->create([
            'name' => 'Monsef Eledrisse',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            // ShieldSeeder::class,
            PermissionSeeder::class,
            SubscriptionSeeder::class,
            ProvidersSeeder::class,
            DataSeeder::class,
            CompanySeeder::class,
            // PermissionAdminMessageSeeder::class,
        ]);
    }
}
