<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Provider;
use Illuminate\Database\Seeder;

final class ProvidersSeeder extends Seeder
{
    public function run(): void
    {
        Provider::create([
            'name' => 'libyana',
            'host' => 'localhost',
            'port' => 2775,
            'system_id' => 'libyana',
            'password' => 'password',
            'system_type' => 'www',
            'status' => 'active',
            'pattern' => '^(?:\+218|00218)?\s?((92|94)\d{7})$',
            'smpp_pattern' => '^00218',
            'smpp_pattern_replace' => '0',
        ]);

        Provider::create([
            'name' => 'almadar',
            'host' => 'localhost',
            'port' => 2775,
            'system_id' => 'almadar',
            'password' => 'password',
            'system_type' => 'www',
            'status' => 'active',
            'pattern' => '^(?:\+218|00218)?\s?((91|93)\d{7})$',
            'smpp_pattern' => '^00',
            'smpp_pattern_replace' => '',
        ]);
    }
}
