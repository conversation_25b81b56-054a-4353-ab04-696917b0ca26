<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\TransactionStatus;
use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Transaction;
use Carbon\CarbonImmutable;
use Illuminate\Database\Seeder;

final class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $company = Company::first();

        $project = Project::create([
            'name' => 'Test Project',
            'description' => 'Test Project Description',
            'company_id' => $company->id,
            'status' => 'active',
            'type' => 'mixed',
            'limit' => 2000,
        ]);

        $token = $project->createToken('projects');
        $project->token = $token->plainTextToken;
        $project->save();

        $plan1 = Plan::where('name', 'like', '%SMS%')->first();
        $plan2 = Plan::where('name', 'like', '%OTP%')->first();

        $subscription = $project->subscribeTo(
            $plan1,
            CarbonImmutable::now(),
            $plan1->periodicity_quantity,
        );

        $subscription2 = $project->subscribeTo(
            $plan2,
            CarbonImmutable::now(),
            $plan2->periodicity_quantity,
        );

        Transaction::create([
            'amount' => $plan1->price * -1,
            'action_type' => 'charge',
            'status' => TransactionStatus::Completed,
            'description' => 'Subscription to '.$plan1->name,
            'company_id' => $company->id,
            'subscription_id' => $subscription->id,
        ]);

        Transaction::create([
            'amount' => $plan2->price * -1,
            'action_type' => 'charge',
            'status' => TransactionStatus::Completed,
            'description' => 'Subscription to '.$plan2->name,
            'company_id' => $company->id,
            'subscription_id' => $subscription2->id,
        ]);

        $contact_group = ContactGroup::create([
            'name' => 'Test Group',
            'description' => 'Test Group Description',
            'status' => 'active',
            'reference' => 'test_group',
            'company_id' => $company->id,
        ]);

        $project->contact_groups()->attach($contact_group);

        $contacts = [
            '00218911234567',
            '00218911234568',
            '00218911234569',
            '00218911234570',
            '00218911234571',
            '00218911234572',
            '00218911234573',
            '00218911234574',
            '00218911234575',
            '00218911234576',
            '00218911234577',
            '00218911234578',
            '00218911234579',
            '00218911234580',
            '00218911234581',
            '00218911234582',
            '00218911234583',
            '00218911234584',
            '00218911234585',
            '00218911234586',
            '00218911234587',
            '00218911234588',
            '00218911234589',
            '00218911234590',
            '00218911234591',
            '00218911234592',
            '00218911234593',
            '00218911234594',
            '00218921234595',
            '00218921234596',
            '00218921234597',
            '00218921234598',
            '00218921234599',
            '00218921234600',
            '00218921234601',
            '00218921234602',
            '00218931234603',
            '00218931234604',
            '00218931234605',
            '00218931234606',
            '00218931234607',
            '00218931234608',
            '00218931234609',
            '00218931234610',
            '00218931234611',
            '00218931234612',
            '00218931234613',
            '00218931234614',
            '00218931234615',
            '00218941234616',
            '00218941234617',
            '00218941234618',
            '00218941234619',
            '00218941234620',
            '00218941234621',
            '00218941234622',
            '00218941234623',
            '00218941234624',
            '00218941234625',
            '00218941234626',
            '00218941234627',
        ];

        foreach ($contacts as $contact => $index) {
            Contact::create([
                'contact_group_id' => $contact_group->id,
                'name' => 'Test Contact'.$contact,
                'phone' => $index,
                'sex' => 'male',
                'births' => '1990-01-01',
            ]);
        }
    }
}
