<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\City;
use App\Models\Company;
use App\Models\Provider;
use App\Models\Sender;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;

final class DataSeeder extends Seeder
{
    public function run(): void
    {
        $city = City::create([
            'name' => 'Misurata',
        ]);

        $user = User::create([
            'name' => 'Company User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $company = Company::create([
            'name' => 'Lamah co.',
            'address' => 'Test Address',
            'phone' => '+218912345678',
            'email' => '<EMAIL>',
            'category' => 'IT company',
            'description' => 'Test Company Description',
            'city_id' => $city->id,
            'status' => 'active',
            'default_user_id' => $user->id,
        ]);

        $libyana = Provider::where('name', 'libyana')->firstOrFail();

        $almadar = Provider::where('name', 'almadar')->firstOrFail();

        $sender = Sender::create([
            'sender' => 'Lamah',
            'category' => 'SenderId',
            'type' => 'multi',
            'status' => 'active',
            'notes' => 'Test Sender Notes',
        ]);

        $sender->provider()->create([
            'provider_id' => $libyana->id,
            'expired_at' => now()->addYear(),
        ]);

        $sender->provider()->create([
            'provider_id' => $almadar->id,
            'expired_at' => now()->addYear(),
        ]);

        $company->users()->attach($user);

        $company->senders()->attach($sender);

        $user->assignRole('company_owner');

        Transaction::create([
            'amount' => 1500,
            'status' => 'completed',
            'action_type' => 'deposit',
            'company_id' => $company->id,
        ]);
    }
}
