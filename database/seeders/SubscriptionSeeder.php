<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\PeriodicityType;
use App\Models\Feature;
use App\Models\Plan;
use Illuminate\Database\Seeder;

final class SubscriptionSeeder extends Seeder
{
    public function run(): void
    {
        $otp_plans = [
            [
                'name' => 'OTP Starter 1',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 59,
                'over_quota_price' => null,
                'charges' => 200,
            ],
            [
                'name' => 'OTP Starter 2',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 133,
                'over_quota_price' => null,
                'charges' => 500,
            ],
            [
                'name' => 'OTP Starter 3',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 195,
                'over_quota_price' => null,
                'charges' => 750,
            ],
            [
                'name' => 'OTP Starter 4',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 369,
                'over_quota_price' => null,
                'charges' => 1500,
            ],

            [
                'name' => 'OTP Professional 1',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 590,
                'over_quota_price' => 246,
                'charges' => 2500,
            ],
            [
                'name' => 'OTP Professional 2',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 1130,
                'over_quota_price' => 235,
                'charges' => 5000,
            ],
            [
                'name' => 'OTP Professional 3',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 1650,
                'over_quota_price' => 235,
                'charges' => 7500,
            ],
            [
                'name' => 'OTP Professional 4',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 1970,
                'over_quota_price' => 221,
                'charges' => 10000,
            ],

            [
                'name' => 'OTP Enterprise 1',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 2790,
                'over_quota_price' => 200,
                'charges' => 15000,
            ],
            [
                'name' => 'OTP Enterprise 2',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 4450,
                'over_quota_price' => 200,
                'charges' => 25000,
            ],
            [
                'name' => 'OTP Enterprise 3',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 8700,
                'over_quota_price' => 197,
                'charges' => 50000,
            ],
            [
                'name' => 'OTP Enterprise 4',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 17200,
                'over_quota_price' => 197,
                'charges' => 100000,
            ],
        ];

        $sms_plans = [
            [
                'name' => 'SMS Starter 1',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 120,
                'over_quota_price' => null,
                'charges' => 500,
            ],
            [
                'name' => 'SMS Starter 2',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 177,
                'over_quota_price' => null,
                'charges' => 750,
            ],
            [
                'name' => 'SMS Starter 3',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 327,
                'over_quota_price' => null,
                'charges' => 1500,
            ],
            [
                'name' => 'SMS Starter 4',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 1,
                'price' => 639,
                'over_quota_price' => null,
                'charges' => 3000,
            ],

            [
                'name' => 'SMS Professional 1',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 920,
                'over_quota_price' => 195,
                'charges' => 5000,
            ],
            [
                'name' => 'SMS Professional 2',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 1350,
                'over_quota_price' => 195,
                'charges' => 7500,
            ],
            [
                'name' => 'SMS Professional 3',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 1780,
                'over_quota_price' => 192,
                'charges' => 10000,
            ],
            [
                'name' => 'SMS Professional 4',
                'periodicity' => PeriodicityType::Quarterly,
                'qty' => 2,
                'price' => 2640,
                'over_quota_price' => 192,
                'charges' => 15000,
            ],

            [
                'name' => 'SMS Enterprise 1',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 5160,
                'over_quota_price' => 170,
                'charges' => 30000,
            ],
            [
                'name' => 'SMS Enterprise 2',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 8300,
                'over_quota_price' => 170,
                'charges' => 50000,
            ],
            [
                'name' => 'SMS Enterprise 3',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 16100,
                'over_quota_price' => 165,
                'charges' => 100000,
            ],
            [
                'name' => 'SMS Enterprise 4',
                'periodicity' => PeriodicityType::Yearly,
                'qty' => 1,
                'price' => 23250,
                'over_quota_price' => 160,
                'charges' => 150000,
            ],
        ];

        $sms_Feature = Feature::firstOrCreate([
            'name' => 'SMS',
            'consumable' => true,
        ]);

        $otp_Feature = Feature::firstOrCreate([
            'name' => 'OTP',
            'consumable' => true,
        ]);

        foreach ($sms_plans as $sms_plan) {
            $plan = Plan::firstOrCreate([
                'name' => $sms_plan['name'],
                'periodicity_type' => $sms_plan['periodicity'],
                'price' => $sms_plan['price'],
                'periodicity_quantity' => $sms_plan['qty'],
                'over_quota_price' => $sms_plan['over_quota_price'] / 1000,
            ]);

            $plan->feature()->attach($sms_Feature, [
                'charges' => (int) $sms_plan['charges'],
            ]);
        }

        foreach ($otp_plans as $otp_plan) {
            $plan = Plan::firstOrCreate([
                'name' => $otp_plan['name'],
                'periodicity_type' => $otp_plan['periodicity'],
                'price' => $otp_plan['price'],
                'periodicity_quantity' => $otp_plan['qty'],
                'over_quota_price' => $otp_plan['over_quota_price'] / 1000,
            ]);

            $plan->feature()->attach($otp_Feature, [
                'charges' => (int) $otp_plan['charges'],
            ]);
        }
    }
}
