<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class PermissionAdminMessageSeeder extends Seeder
{
    public function run(): void
    {
        $super_admin = Role::firstOrCreate(['name' => 'super_admin']);
        $permissions = ['view_message', 'view_any_message', 'create_message', 'update_message', 'restore_message', 'restore_any_message'];
        $super_admin->givePermissionTo($permissions);
    }
}
