<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use Artisan;
use Illuminate\Database\Seeder;

final class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Run Artisan Command to Add Permissions

        //        Artisan::call('shield:generate --panel admin');

        if (! app()->environment('testing')) {
            $user1 = User::first();
            $user2 = User::latest()->first();

            $user1->assignRole('super_admin');
            $user2->assignRole('super_admin');
        }
    }
}
