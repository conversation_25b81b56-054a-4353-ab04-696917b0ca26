<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\Plan;
use App\Models\Project;
use App\Models\Sender;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Project>
 */
final class ProjectFactory extends Factory
{
    protected $model = Project::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'description' => $this->faker->text(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'limit' => $this->faker->numberBetween(10000, 100000),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'type' => $this->faker->randomElement([
                'subscription',
                'budget',
                'mixed',
            ]),

            'company_id' => Company::factory(),
        ];
    }

    public function withSubscription(string $planName): self
    {
        return $this->afterCreating(function (Project $project) use (
            $planName,
        ): void {
            $project->subscribeTo(
                Plan::where('name', $planName)->firstOrFail(),
                CarbonImmutable::now(),
            );
        });
    }

    public function withBalance(int $balance): self
    {
        return $this->afterCreating(function (Project $project) use (
            $balance,
        ): void {
            $project->transactions()->create([
                'amount' => $balance,
                'action_type' => 'deposit',
                'status' => 'completed',
                'company_id' => $project->company_id,
            ]);
        });
    }

    /**
     * @param  array<string>  $providers
     */
    public function withSenderId(
        string $senderId,
        array $providers = ['almadar', 'libyana'],
    ): self {
        return $this->afterCreating(function (Project $project) use (
            $senderId,
            $providers,
        ): void {
            assert($project->company instanceof Company);
            $sender = Sender::factory([
                'status' => 'active',
                'sender' => $senderId,
            ])
                ->withProvider($providers)
                ->create();
            $project->company->senders()->attach($sender, [
                'expired_at' => now()->addYear(),
            ]);
        });
    }

    /**
     * @param  array<string>  $providers
     */
    public function withContactGroup(
        array $providers = ['almadar', 'libyana'],
    ): self {
        return $this->afterCreating(function (Project $project) use (
            $providers,
        ): void {
            $contactGroup = ContactGroup::factory([
                'company_id' => $project->company_id,
                'status' => 'active',
            ])
                ->withContacts(providers: $providers)
                ->create();
            $project->contact_groups()->attach($contactGroup);
        });
    }
}
