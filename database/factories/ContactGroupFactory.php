<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

final class ContactGroupFactory extends Factory
{
    protected $model = ContactGroup::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'description' => $this->faker->text(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'reference' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'company_id' => Company::factory(),
        ];
    }

    /**
     * @param  array<string>  $providers
     */
    public function withContacts(int $count = 10, array $providers = ['almadar', 'libyana']): self
    {
        return $this->has(Contact::factory()->withProviders($providers)->count($count));
    }
}
