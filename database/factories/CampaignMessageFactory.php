<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\CampaignMessage;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<CampaignMessage>
 */
final class CampaignMessageFactory extends Factory
{
    protected $model = CampaignMessage::class;

    public function definition(): array
    {
        return [
            'short_message' => $this->faker->word(),
            'age_from' => $this->faker->numberBetween(6, 90),
            'age_to' => $this->faker->numberBetween('age_from', 90),
            'sex' => $this->faker->randomElement(['male', 'female', 'both']),
            'quantity' => $this->faker->randomNumber(5, true),
            'status' => $this->faker->randomElement([
                'active',
                'inactive',
                'pending',
                'processing',
                'rejected',
            ]),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'company_id' => Company::factory(),
        ];
    }
}
