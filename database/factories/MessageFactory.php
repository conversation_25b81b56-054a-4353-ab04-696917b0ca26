<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Company;
use App\Models\ContactGroup;
use App\Models\Message;
use App\Models\Sender;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Message>
 */
class MessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'short_message' => $this->faker->text,
            'sender_id' => Sender::factory(),
            'company_id' => Company::factory(),
            'project_id' => null,
            'message_type' => $this->faker->randomElement(['flash', 'sms', 'otp']), // flash or sms or otp
            'send_type' => $this->faker->randomElement(['single', 'multiple']), // single or multiple
            'contact_group_id' => ContactGroup::factory(),
            'message_consumption' => $this->faker->randomNumber(2),
            'transaction_id' => Transaction::factory(),
        ];
    }
}
