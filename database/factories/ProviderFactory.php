<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Provider;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Provider>
 */
class ProviderFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'host' => 'localhost',
            'port' => $this->faker->numberBetween(2500, 2800),
            'system_id' => 'system_id',
            'password' => 'password',
            'system_type' => 'www',
            'connection_timeout' => $this->faker->numberBetween(30, 60),
            'enquire_link_interval' => $this->faker->numberBetween(30, 60),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'pattern' => '^(?:\+218|00218)?\s?((92|94)\d{7})$',
            'smpp_pattern' => '^00',
            'smpp_pattern_replace' => '0',
        ];
    }
}
