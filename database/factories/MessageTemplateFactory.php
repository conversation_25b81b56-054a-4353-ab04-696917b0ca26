<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\MessageTemplateStatus;
use App\Enums\MessageTemplateType;
use App\Models\Company;
use App\Models\MessageTemplate;
use App\Models\MessageTemplateParameter;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MessageTemplate>
 */
class MessageTemplateFactory extends Factory
{
    protected $model = MessageTemplate::class;

    public function definition()
    {
        return [
            'display_name' => $this->faker->word(),
            'short_name' => $this->faker->slug(),
            'content' => $this->faker->text(),
            'type' => $this->faker->randomElement(MessageTemplateType::all()),
            'status' => $this->faker->randomElement(MessageTemplateStatus::all()),
            'company_id' => Company::factory(),
            'project_id' => Project::factory(),
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }

    public function withParameters(int $count = 1): self
    {
        return $this->afterCreating(function (MessageTemplate $messageTemplate) use ($count): void {
            MessageTemplateParameter::factory()->count($count)->create([
                'message_template_id' => $messageTemplate->id,
            ]);
        });
    }
}
