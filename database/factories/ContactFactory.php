<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Contact;
use App\Models\ContactGroup;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Contact>
 */
final class ContactFactory extends Factory
{
    protected $model = Contact::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->faker->numerify('002189########'),
            'sex' => $this->faker->randomElement(['male', 'female']),
            'births' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'contact_group_id' => ContactGroup::factory(),
        ];
    }

    /**
     * @param  array<string>  $providers
     */
    public function withProviders(array $providers = ['almadar', 'libyana']): self
    {
        assert($providers !== []);

        // Depending on the providers, generate a phone number using a random provider prefix
        return $this->state(function (array $attributes) use ($providers): array {
            // TODO: make providers dynamic with Regex pattern in the database
            $providerPrefixes = [
                'almadar' => ['0021891', '0021893'],
                'libyana' => ['0021892', '0021894'],
            ];
            $randProvider = $providers[array_rand($providers)];
            $randPrefix = $providerPrefixes[$randProvider][array_rand($providerPrefixes[$randProvider])];

            return [
                'phone' => $randPrefix.$this->faker->numerify('#######'),
            ];
        });
    }
}
