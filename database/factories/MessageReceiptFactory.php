<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Contact;
use App\Models\Message;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageReceipt>
 */
class MessageReceiptFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'message_id' => Message::factory(),
            'number' => $this->faker->numerify('0021891#######'),
            'contact_id' => Contact::factory(),
            'sent_at' => null,
            'delivered_at' => null,
            'status' => $this->faker->randomElement([
                'pending',
                'sent',
                'delivered',
                'failed',
            ]),
            'smpp_message_id' => null,
            'delivery_report' => null,
        ];
    }
}
