<?php

declare(strict_types=1);

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class RegistrationRequestFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'city' => $this->faker->city(),
            'company' => $this->faker->company(),
            'category' => $this->faker->word(),
            'have_smsapi' => $this->faker->randomElement(['yes', 'no', 'developing']),
            'target' => $this->faker->randomElement(['OTP', 'Campaign', 'Notification']),
            'system_type' => $this->faker->word(),
            'notes' => $this->faker->text(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
        ];
    }
}
