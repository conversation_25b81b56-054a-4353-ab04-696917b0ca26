<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Company;
use App\Models\Provider;
use App\Models\Sender;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Sender>
 */
final class SenderFactory extends Factory
{
    protected $model = Sender::class;

    public function definition(): array
    {
        return [
            'sender' => $this->faker->regexify('[A-Za-z]{3,11}'),
            'category' => $this->faker->randomElement(['SenderId', 'ShortNumber']),
            'type' => $this->faker->randomElement(['private', 'multi']),
            'status' => $this->faker->randomElement(['pending', 'active', 'rejected']),
            'notes' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'company_id' => Company::factory(),
        ];
    }

    /**
     * @param  array<string>  $providers
     */
    public function withProvider(array $providers = ['libyana', 'almadar']): self
    {
        return $this->afterCreating(function (Sender $sender) use ($providers): void {
            foreach ($providers as $provider) {
                $sender->provider()->create([
                    'provider_id' => Provider::where('name', $provider)->firstOrFail()->id,
                    'expired_at' => now()->addYear(),
                ]);
            }
        });
    }
}
