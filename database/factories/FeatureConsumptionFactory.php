<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Feature;
use App\Models\FeatureConsumption;
use App\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<FeatureConsumption>
 */
final class FeatureConsumptionFactory extends Factory
{
    protected $model = FeatureConsumption::class;

    public function definition(): array
    {
        return [
            'consumption' => $this->faker->numberBetween(1, 100),
            'type' => $this->faker->randomElement(['SMS', 'OTP']),
            'feature_id' => Feature::factory(),
            'subscription_id' => Subscription::factory(),
        ];
    }
}
