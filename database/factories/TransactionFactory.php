<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\TransactionStatus;
use App\Models\Company;
use App\Models\Project;
use App\Models\Subscription;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Transaction>
 */
final class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    public function definition(): array
    {
        return [
            'amount' => $this->faker->randomNumber(6, true),
            'description' => $this->faker->text(),
            'action_type' => $this->faker->randomElement(['deposit', 'withdraw', 'charge', 'refund']),
            'status' => $this->faker->randomElement(TransactionStatus::all()),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'company_id' => Company::factory(),
            'subscription_id' => Subscription::factory(),
            'project_id' => Project::factory(),
        ];
    }
}
