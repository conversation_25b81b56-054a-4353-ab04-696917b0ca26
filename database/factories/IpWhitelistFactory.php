<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\IpWhitelist;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IpWhitelist>
 */
class IpWhitelistFactory extends Factory
{
    protected $model = IpWhitelist::class;

    public function definition(): array
    {
        return [
            'ip_address' => $this->faker->ipv4(),
            'description' => $this->faker->text(),
            'project_id' => Project::factory(),
        ];
    }
}
