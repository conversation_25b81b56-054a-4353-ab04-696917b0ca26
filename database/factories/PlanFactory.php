<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\PeriodicityType;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Plan>
 */
final class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'periodicity_type' => $this->faker->randomElement(PeriodicityType::all()),
            'periodicity_quantity' => $this->faker->numberBetween(1, 10),
            'price' => $this->faker->randomNumber(6),
            'over_quota_price' => $this->faker->randomNumber(2),
            'extended_price' => $this->faker->randomNumber(6),
            'deleted_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
        ];
    }
}
