<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\City;
use App\Models\Company;
use App\Models\Sender;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends Factory<Company>
 */
final class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'address' => $this->faker->address(),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'category' => $this->faker->word(),
            'description' => $this->faker->text(),
            'default_user_id' => User::factory(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'verified_at' => Carbon::now(),
            //            'expired_at' => Carbon::now(),
            //            'registerMediaConversionsUsingModelInstance' => $this->faker->boolean(),

            'city_id' => City::factory(),
        ];
    }

    public function withSenderId(): self
    {
        return $this->afterCreating(function (Company $company): void {
            $company->senders()->attach(Sender::factory()->create());
        });
    }
}
