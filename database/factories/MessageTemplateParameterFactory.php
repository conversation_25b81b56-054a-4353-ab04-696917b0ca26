<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\MessageTemplateParameterType;
use App\Models\MessageTemplate;
use App\Models\MessageTemplateParameter;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MessageTemplateParameter>
 */
class MessageTemplateParameterFactory extends Factory
{
    protected $model = MessageTemplateParameter::class;

    public function definition()
    {
        return [
            'name' => $this->faker->word(),
            'type' => $this->faker->randomElement(MessageTemplateParameterType::all()),
            'message_template_id' => MessageTemplate::factory(),
            'max_limit' => $this->faker->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
