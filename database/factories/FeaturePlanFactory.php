<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Feature;
use App\Models\FeaturePlan;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<FeaturePlan>
 */
final class FeaturePlanFactory extends Factory
{
    protected $model = FeaturePlan::class;

    public function definition(): array
    {
        return [
            'charges' => $this->faker->numberBetween(1, 100),
            'feature_id' => Feature::factory(),
            'plan_id' => Plan::factory(),
        ];
    }
}
