services:
  redis:
    image: redis:alpine
    restart: unless-stopped
    healthcheck:
      test: redis-cli ping | grep PONG
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
    security_opt:
      - no-new-privileges:true

  rabbit-mq:
    image: rabbitmq:3.10-management-alpine
    restart: unless-stopped
    healthcheck:
      test: rabbitmq-diagnostics -q ping
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 525M
    security_opt:
      - no-new-privileges:true

  jasmin:
    image: jookies/jasmin:latest
    restart: unless-stopped
    ports:
#      - 2775:2775
      - 8990:8990
      - 1401:1401
    volumes:
      - ./jasmin_logs:/var/log/jasmin/
      - ./jasmin_config:/etc/jasmin
    extra_hosts:
      - "sms.test:host-gateway"
    depends_on:
      redis:
        condition: service_healthy
      rabbit-mq:
        condition: service_healthy
    environment:
      REDIS_CLIENT_HOST: redis
      AMQP_BROKER_HOST: rabbit-mq
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 256M
    security_opt:
      - no-new-privileges:true

  smppsim:
    image: blackorder/smppsim:latest
    restart: unless-stopped
    ports:
      - "2775:2775"
      - '8884:${HTTP_PORT:-8884}'
#    volumes:
#      - smppsim_conf:/app/conf
#      - smppsim_mo:/app/mo
    environment:
      SMPP_PORT: ${SMPP_PORT:-2775}
      SMPP_CONNECTION_HANDLERS: ${SMPP_CONNECTION_HANDLERS:-4}
      MESSAGE_STATE_CHECK_FREQUENCY: ${MESSAGE_STATE_CHECK_FREQUENCY:-5000}
      MAX_TIME_ENROUTE: ${MAX_TIME_ENROUTE:-10000}
      DELAY_DELIVERY_RECEIPTS_BY: ${DELAY_DELIVERY_RECEIPTS_BY:-3000}
      PERCENTAGE_THAT_TRANSITION: ${PERCENTAGE_THAT_TRANSITION:-75}
      PERCENTAGE_DELIVERED: ${PERCENTAGE_DELIVERED:-90}
      PERCENTAGE_UNDELIVERABLE: ${PERCENTAGE_UNDELIVERABLE:-6}
      PERCENTAGE_ACCEPTED: ${PERCENTAGE_ACCEPTED:-2}
      PERCENTAGE_REJECTED: ${PERCENTAGE_REJECTED:-2}
      DISCARD_FROM_QUEUE_AFTER: ${DISCARD_FROM_QUEUE_AFTER:-60000}
      HTTP_PORT: ${HTTP_PORT:-8884}
      HTTP_THREADS: ${HTTP_THREADS:-1}
      SYSTEM_IDS: lamah,almadar,libyana
      PASSWORDS: lamah,password,password
      OUTBIND_ENABLED: ${OUTBIND_ENABLED:-false}
      OUTBIND_ESME_IP_ADDRESS: ${OUTBIND_ESME_IP_ADDRESS:-'127.0.0.1'}
      OUTBIND_ESME_PORT: ${OUTBIND_ESME_PORT:-2776}
      OUTBIND_ESME_SYSTEMID: ${OUTBIND_ESME_SYSTEMID:-smppclient1}
      OUTBIND_ESME_PASSWORD: ${OUTBIND_ESME_PASSWORD:-password}
      DELIVERY_MESSAGES_PER_MINUTE: ${DELIVERY_MESSAGES_PER_MINUTE:-0}
      OUTBOUND_QUEUE_MAX_SIZE: ${OUTBOUND_QUEUE_MAX_SIZE:-500000}
      INBOUND_QUEUE_MAX_SIZE: ${INBOUND_QUEUE_MAX_SIZE:-500000}
      DELAYED_INBOUND_QUEUE_PROCESSING_PERIOD: ${DELAYED_INBOUND_QUEUE_PROCESSING_PERIOD:-60}
      DELAYED_INBOUND_QUEUE_MAX_ATTEMPTS: ${DELAYED_INBOUND_QUEUE_MAX_ATTEMPTS:-100}
