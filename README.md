<div align="center">
    <img src="public/logo/Logo 03.svg" alt="logo"/>
    <h1><PERSON>h SMS Gateway</h1> 
    <h3>
        System to manage SMS operations
    </h3>
</div>
<br>

`Note` This project only works on macOS, Linux or WSL.

Make sure you have installed Docker and Docker Compose and its works in `WSL` if you are using windows.

```bash
<NAME_EMAIL>:lamah-co/lamah-sms-gateway.git
```

Go to the project directory

```bash
cd lamah-sms-gateway/
```

Copy .env.example to new file .env

```bash
cp .env.example .env
```

Install Laravel Dependency

```bash
docker run --rm \
  -u "$(id -u):$(id -g)" \
  -v "$(pwd):/var/www/html" \
  -w /var/www/html \
  laravelsail/php83-composer:latest \
  composer install --ignore-platform-reqs
```

Build application docker image

```bash
./vendor/bin/sail build
```

Start Application with docker

```bash
./vendor/bin/sail up -d
```

Restart sail container

```bash
./vendor/bin/sail restart
```

Generate laravel key

```bash
./vendor/bin/sail php artisan key:generate
```

Run migrations production

```bash
./vendor/bin/sail php artisan migrate
```

Run migrations development

```bash
./vendor/bin/sail php artisan migrate:fresh --seed
```

link storage to display images

```bash
./vendor/bin/sail php artisan storage:link
```

Run tests

```bash
./vendor/bin/sail composer test
```

Run tests

```bash
./vendor/bin/sail composer test:lint
```

Clear cache if you have any problem

```bash
./vendor/bin/sail php artisan optimize:clear
```

Generate api documentation

```bash
./vendor/bin/sail php artisan scribe:generate
```
Install node_modules

```bash
./vendor/bin/sail npm install
```

Build assets

```bash
./vendor/bin/sail npm run dev
```
