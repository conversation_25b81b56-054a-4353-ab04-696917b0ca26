name: Tests

on:
#  push:
#    branches:
#      - main
  pull_request:
    branches:
      - dev

jobs:
  ci:
    runs-on: self-hosted
    strategy:
      fail-fast: true
      matrix:
        php: [8.4]

    name: Tests

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Cache Dependencies
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache/files
          key: dependencies-php-composer-${{ hashFiles('composer.lock') }}

      # - name: Setup PHP
      #   uses: shivammathur/setup-php@v2
      #   with:
      #     php-version: ${{ matrix.php }}
      #     extensions: json, dom, curl, libxml, mbstring, zip
      #     tools: composer:v2
      #     coverage: xdebug

      # - name: Set up Node & NPM
      #   uses: actions/setup-node@v2
      #   with:
      #     node-version: '22.x'

      # - name: Setup Problem Matches
      #   run: |
      #     echo "::add-matcher::${{ runner.tool_cache }}/php.json"
      #     echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install PHP dependencies
        run: composer install --no-interaction --no-progress --ansi

      - name: Get NPM cache directory
        id: npm-cache-dir
        shell: bash
        run: echo "dir=$(npm config get cache)" >> ${GITHUB_OUTPUT}

      - name: Cache dependencies
        id: npm-cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm install

      - name: Build dependencies
        run: npm run build

      - name: Prepare Laravel
        run: |
          cp .env.example .env
          php artisan key:generate

      - name: Tests
        run: composer test
