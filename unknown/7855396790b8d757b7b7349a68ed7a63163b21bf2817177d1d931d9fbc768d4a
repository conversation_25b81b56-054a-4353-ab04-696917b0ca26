<?php

declare(strict_types=1);

use App\Models\ContactUs;

use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\postJson;
use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
});

it('can store a contact us request', function () {
    $data = [
        'name' => '<PERSON>',
        'phone' => '00218911234567',
        'subject' => 'General Inquiry',
        'email' => '<EMAIL>',
        'message' => 'This is a test message for the contact us form.',
    ];

    $response = postJson('/api/contact-us', $data);

    $response->assertStatus(200);
    $response->assertJson([
        'message' => 'Thank you for contacting us. We will get back to you soon.',
    ]);

    assertDatabaseHas(ContactUs::class, [
        'name' => '<PERSON>',
        'phone' => '00218911234567',
        'subject' => 'General Inquiry',
        'email' => '<EMAIL>',
        'message' => 'This is a test message for the contact us form.',
    ]);

    assertDatabaseCount(ContactUs::class, 1);
});

it('validates required fields', function () {
    $response = postJson('/api/contact-us', []);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['name', 'phone', 'subject', 'email', 'message']);

    assertDatabaseCount(ContactUs::class, 0);
});

it('validates email format', function () {
    $data = [
        'name' => 'John Doe',
        'phone' => '00218911234567',
        'subject' => 'General Inquiry',
        'email' => 'invalid-email',
        'message' => 'This is a test message for the contact us form.',
    ];

    $response = postJson('/api/contact-us', $data);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['email']);

    assertDatabaseCount(ContactUs::class, 0);
});

it('validates phone number format', function () {
    $data = [
        'name' => 'John Doe',
        'phone' => '002189612532079',
        'subject' => 'General Inquiry',
        'email' => '<EMAIL>',
        'message' => 'This is a test message for the contact us form.',
    ];

    $response = postJson('/api/contact-us', $data);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['phone']);

    assertDatabaseCount(ContactUs::class, 0);
});
