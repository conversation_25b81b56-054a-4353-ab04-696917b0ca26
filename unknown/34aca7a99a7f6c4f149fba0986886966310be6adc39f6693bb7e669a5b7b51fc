<?php

declare(strict_types=1);

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('', function (Blueprint $table): void {
            $role = Role::firstOrCreate([
                'name' => 'super_admin',
            ]);

            $permissions = [
                'view_any_contact_us',
                'view_contact_us',
                'delete_contact_us',
                'delete_any_contact_us',
            ];

            foreach ($permissions as $permission) {
                Permission::firstOrCreate([
                    'name' => $permission,
                ]);
            }
            $role->givePermissionTo($permissions);
        });
    }

    public function down(): void
    {
        Schema::table('', function (Blueprint $table): void {
            $role = Role::firstOrCreate([
                'name' => 'super_admin',
            ]);

            $permissions = [
                'view_any_contact_us',
                'view_contact_us',
                'delete_contact_us',
                'delete_any_contact_us',
            ];

            $role->revokePermissionTo($permissions);
        });
    }
};
