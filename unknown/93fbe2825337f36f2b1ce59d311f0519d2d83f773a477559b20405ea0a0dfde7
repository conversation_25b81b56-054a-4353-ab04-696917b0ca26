<?php

declare(strict_types=1);

use App\Models\User;
use App\Policies\ContactUsPolicy;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function (): void {
    // Create permissions if they don't exist
    Permission::firstOrCreate(['name' => 'view_any_contact_us']);
    Permission::firstOrCreate(['name' => 'view_contact_us']);
    Permission::firstOrCreate(['name' => 'delete_contact_us']);
    Permission::firstOrCreate(['name' => 'delete_any_contact_us']);
});

// Special test for super_admin role
it('allows super_admin to view any contact us', function () {
    $user = User::factory()->create()->assignRole('super_admin');

    $policy = new ContactUsPolicy();

    expect($policy->viewAny($user))->toBeTrue();
});

// Define abilities and their corresponding permissions
$abilities = [
    'viewAny' => 'view_any_contact_us',
    'view' => 'view_contact_us',
    'delete' => 'delete_contact_us',
    'deleteAny' => 'delete_any_contact_us',
];

// Test each ability with permission
foreach ($abilities as $method => $permission) {
    it("allows user with permission to {$method} contact us", function () use ($method, $permission) {
        $user = User::factory()->create();
        $user->givePermissionTo($permission);

        $policy = new ContactUsPolicy();

        expect($policy->{$method}($user))->toBeTrue();
    });

    it("denies user without permission to {$method} contact us", function () use ($method) {
        $user = User::factory()->create();

        $policy = new ContactUsPolicy();

        expect($policy->{$method}($user))->toBeFalse();
    });
}

// Special tests for methods that always return false
it('denies all users from creating contact us entries', function () {
    // Even users with permissions should not be able to create
    $user = User::factory()->create();
    $role = Role::firstOrCreate(['name' => 'admin']);
    $user->assignRole($role);

    $policy = new ContactUsPolicy();

    expect($policy->create($user))->toBeFalse();
});

it('denies all users from updating contact us entries', function () {
    // Even users with permissions should not be able to update
    $user = User::factory()->create();
    $role = Role::firstOrCreate(['name' => 'admin']);
    $user->assignRole($role);

    $policy = new ContactUsPolicy();

    expect($policy->update($user))->toBeFalse();
});
