{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "prettier": "prettier", "format": "prettier --write ."}, "lint-staged": {"*.{js,tsx,css,md,php,blade.php}": "prettier --write"}, "devDependencies": {"@prettier/plugin-php": "^0.22.2", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@shufo/prettier-plugin-blade": "^1.14.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "commitlint": "^19.3.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "lint-staged": "^13.2.2", "postcss": "^8.4.49", "postcss-nesting": "^13.0.1", "prettier": "^3.2.5", "tailwindcss": "^3.4.16", "vite": "^5.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.32.0"}, "release": {"branches": ["main"], "repositoryUrl": "https://github.com/lamah-co/lamah-sms-gateway.git", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/git", {"message": "chore(release): ${nextRelease.version} \n\n${nextRelease.notes}"}], "@semantic-release/github"]}}