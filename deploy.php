<?php

declare(strict_types=1);

namespace Deployer;

require 'recipe/laravel.php';
require 'contrib/npm.php';

// Hosts

host('sms.lamah.com')
    ->set('remote_user', 'deployer')
    ->set('deploy_path', '~/lamah-sms-gateway')
    ->set('domain', 'sms.lamahtech.com')
    ->set('public_path', 'public')
    ->set('db_type', 'none');

// Config

set('repository', '**************:lamah-co/lamah-sms-gateway.git');

add('shared_files', []);
add('shared_dirs', []);
add('writable_dirs', [
    'bootstrap/cache',
    'storage',
    'storage/app',
    'storage/app/private',
    'storage/app/public',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
]);

desc('Execute artisan queue:restart');
task('artisan:queue:restart', artisan('queue:restart'));

desc('Build api documentation');
task('artisan:scribe:generate', function () {
    cd('{{release_or_current_path}}');
    run('{{bin/php}} artisan scribe:generate');
});

set('nvm', 'source $HOME/.nvm/nvm.sh');

// Must be called in every run command related to npm
// Note: do not separate run command, as it is accounted as different shell session
set('use_nvm', function () {
    return '{{nvm}} && nvm use 22 && node --version';
});

task('build', function () {
    cd('{{release_or_current_path}}');
    run('{{use_nvm}} && npm install');
    run('{{use_nvm}} && npm run build');
});

// Hooks
after('deploy:symlink', 'artisan:queue:restart');
after('deploy:failed', 'deploy:unlock');
after('deploy:vendors', 'build');
after('deploy:vendors', 'artisan:scribe:generate');
