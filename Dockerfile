ARG ALPINE_VERSION=3.21
FROM alpine:${ALPINE_VERSION}
LABEL Maintainer="<PERSON><PERSON><PERSON>er <<EMAIL>>"
LABEL Description="Lamah SMS Gateway Docker Image with PHP, Nginx on Alpine Linux"

# Setup document root
WORKDIR /var/www/html

# Install packages
RUN apk add --no-cache \
    nodejs \
    npm \
    curl \
    nginx \
    php84 \
    php84-ctype \
    php84-curl \
    php84-dom \
    php84-fileinfo \
    php84-fpm \
    php84-gd \
    php84-intl \
    php84-mbstring \
    php84-mysqli \
    php84-opcache \
    php84-openssl \
    php84-phar \
    php84-session \
    php84-tokenizer \
    php84-xml \
    php84-xmlreader \
    php84-xmlwriter \
    supervisor \
    php84-zip \
    php84-pdo_pgsql \
    php84-sockets \
    php84-exif \
    php84-pcntl \
    php84-iconv \
    php84-simplexml \
    php84-opcache \
    php84-pdo_sqlite \
    sqlite

# Create symlink so programs depending on `php` still function
RUN ln -s /usr/bin/php84 /usr/bin/php

# Configure nginx - http
COPY docker/nginx.conf /etc/nginx/nginx.conf
# Configure nginx - default server
COPY docker/conf.d /etc/nginx/conf.d/

# Configure PHP-FPM
ENV PHP_INI_DIR=/etc/php84
COPY docker/fpm-pool.conf ${PHP_INI_DIR}/php-fpm.d/www.conf
COPY docker/php.ini ${PHP_INI_DIR}/conf.d/custom.ini

# Configure supervisord
COPY docker/supervisord.conf /etc/supervisord.conf

COPY composer.json composer.lock ./

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

RUN composer install --no-interaction --no-scripts --optimize-autoloader

COPY --chown=nobody . .

# Install npm dependencies and build
RUN npm ci && npm run build

# Make sure files/folders needed by the processes are accessable when they run under the nobody user
RUN chown -R nobody:nobody /var/www/html /run /var/lib/nginx /var/log/nginx && \
    chmod -R 755 /var/www/html/storage

# Declare image volumes
VOLUME /var/www/html/storage

COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh

RUN chmod +x /usr/local/bin/entrypoint.sh

# Create a log file
RUN touch /var/log/cron.log

# Add cron job directly to crontab
RUN echo "* * * * * /usr/local/bin/php /var/www/html/artisan schedule:run >> /var/log/cron.log 2>&1" | crontab -

# Switch to use a non-root user from here on
USER nobody

RUN php artisan storage:link

# Expose the port nginx is reachable on
EXPOSE 8080

# Configure a healthcheck to validate that everything is up&running
HEALTHCHECK --timeout=10s CMD curl --silent --fail http://127.0.0.1:8080/fpm-ping || exit 1

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
