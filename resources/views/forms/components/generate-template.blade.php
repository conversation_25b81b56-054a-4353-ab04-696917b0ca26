<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div x-data="{
        state: $wire.$entangle('{{ $getStatePath() }}'),
        showModal: false,
        openModal() {
            this.showModal = true;
            document.body.style.overflow = 'hidden';
            // Reset the Livewire component when opening
            Livewire.dispatch('reset-form');
        },
        closeModal() {
            this.showModal = false;
            document.body.style.overflow = 'auto';
        }
    }"
    x-on:template-generated.window="
        // Handle the generated template
        const data = $event.detail[0];
        if (data.content) {
            // Set the content in the textarea
            $wire.set('data.content', data.content);

            // Set parameters if they exist
            if (data.parameters && data.parameters.length > 0) {
                const formattedParams = data.parameters.map(param => ({
                    name: param.name,
                    type: 'string',
                    max_limit: 10
                }));
                $wire.set('data.parameters', formattedParams);
            }
        }
    "
    x-on:close-modal.window="closeModal()"
    >
        <!-- Generate Button -->
        <button
            x-on:click="openModal()"
            type="button"
            class="p-2 inline-flex items-center justify-center rounded-lg border border-transparent font-medium shadow-sm transition-colors focus:outline-none focus:ring-offset-2 text-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500"
        >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            {{ __('Generate Template') }}
        </button>

        <!-- Modal -->
        <div
            x-show="showModal"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="fixed inset-0 z-50 overflow-y-auto"
            style="display: none;"
        >
            <!-- Modal backdrop -->
            <div
                class="fixed inset-0 bg-black bg-opacity-50"
                x-on:click="closeModal()"
            ></div>

            <!-- Modal content -->
            <div class="flex min-h-full items-center justify-center p-4">
                <div
                    x-show="showModal"
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform scale-95"
                    x-transition:enter-end="opacity-100 transform scale-100"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 transform scale-100"
                    x-transition:leave-end="opacity-0 transform scale-95"
                    class="relative w-full max-w-lg bg-white rounded-lg shadow-xl"
                    x-on:click.stop
                >
                    <!-- Modal header -->
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            {{ __('Generate Template') }}
                        </h3>
                        <button
                            x-on:click="closeModal()"
                            type="button"
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Modal body -->
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Template Type') }}
                                </label>
                                <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">{{ __('Select template type') }}</option>
                                    <option value="welcome">{{ __('Welcome Message') }}</option>
                                    <option value="verification">{{ __('Verification Code') }}</option>
                                    <option value="notification">{{ __('Notification') }}</option>
                                    <option value="reminder">{{ __('Reminder') }}</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Description') }}
                                </label>
                                <textarea
                                    rows="3"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="{{ __('Describe what you want the template to do...') }}"
                                ></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Language') }}
                                </label>
                                <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="ar">{{ __('Arabic') }}</option>
                                    <option value="en">{{ __('English') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Modal footer -->
                    <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
                        <button
                            x-on:click="closeModal()"
                            type="button"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            {{ __('Cancel') }}
                        </button>
                        <button
                            x-on:click="state = 'generated'; closeModal()"
                            type="button"
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            {{ __('Generate Template') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-dynamic-component>
