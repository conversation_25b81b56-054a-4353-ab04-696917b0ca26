<div>
    <!-- Modal backdrop -->
    <div 
        class="fixed inset-0 bg-black bg-opacity-50 z-40"
        wire:click="$dispatch('close-modal')"
    ></div>

    <!-- Modal content -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
            <div 
                class="relative w-full max-w-lg bg-white rounded-lg shadow-xl"
                wire:click.stop
            >
                <!-- Modal header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ __('Generate Template') }}
                    </h3>
                    <button
                        wire:click="$dispatch('close-modal')"
                        type="button"
                        class="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal body -->
                <form wire:submit="generateTemplate">
                    <div class="p-6">
                        @if ($errors->has('generation'))
                            <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                                {{ $errors->first('generation') }}
                            </div>
                        @endif

                        <div class="space-y-4">
                            <!-- Template Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Template Type') }} <span class="text-red-500">*</span>
                                </label>
                                <select 
                                    wire:model="templateType"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('templateType') border-red-500 @enderror"
                                    @disabled($isGenerating)
                                >
                                    <option value="">{{ __('Select template type') }}</option>
                                    <option value="welcome">{{ __('Welcome Message') }}</option>
                                    <option value="verification">{{ __('Verification Code') }}</option>
                                    <option value="notification">{{ __('Notification') }}</option>
                                    <option value="reminder">{{ __('Reminder') }}</option>
                                </select>
                                @error('templateType')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Description') }} <span class="text-red-500">*</span>
                                </label>
                                <textarea 
                                    wire:model="description"
                                    rows="3"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('description') border-red-500 @enderror"
                                    placeholder="{{ __('Describe what you want the template to do...') }}"
                                    @disabled($isGenerating)
                                ></textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">
                                    {{ __('Minimum 10 characters, maximum 500 characters') }}
                                </p>
                            </div>

                            <!-- Language -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ __('Language') }} <span class="text-red-500">*</span>
                                </label>
                                <select 
                                    wire:model="language"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('language') border-red-500 @enderror"
                                    @disabled($isGenerating)
                                >
                                    <option value="ar">{{ __('Arabic') }}</option>
                                    <option value="en">{{ __('English') }}</option>
                                </select>
                                @error('language')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Modal footer -->
                    <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
                        <button
                            wire:click="$dispatch('close-modal')"
                            type="button"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            @disabled($isGenerating)
                        >
                            {{ __('Cancel') }}
                        </button>
                        <button
                            type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                            @disabled($isGenerating)
                        >
                            @if($isGenerating)
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ __('Generating...') }}
                            @else
                                {{ __('Generate Template') }}
                            @endif
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
