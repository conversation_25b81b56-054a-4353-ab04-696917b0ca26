name: docker-build-push

on:
  release:
    types: [published]

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: 'lab:latest'
          driver: cloud
          endpoint: 'lamahco/cloud-builder'
      - name: Extract release number
        id: extract_release
        run: echo "RELEASE_NUMBER=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          platforms: linux/amd64,linux/arm64
          context: .
          file: ./Dockerfile
          tags: |
            lamahco/sms-gateway:${{ env.RELEASE_NUMBER }}
            lamahco/sms-gateway:latest
          # For pull requests, export results to the build cache.
          # Otherwise, push to a registry.
          outputs: ${{ github.event_name == 'pull_request' && 'type=cacheonly' || 'type=registry,push=true' }}